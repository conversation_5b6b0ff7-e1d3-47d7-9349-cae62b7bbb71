# Database Design - <PERSON><PERSON><PERSON><PERSON> <PERSON>ế cơ sở dữ liệu

## Tổng quan

Tài liệu này định nghĩa các quy tắc thiết kế database cho Blog API v3, bao gồm naming conventions, data types, indices, và schema structure. Hệ thống sử dụng module-based naming conventions để tổ chức tables theo chức năng và đảm bảo tính nhất quán.

## Database Naming Conventions

### Table Names
- **Format**: `{module_prefix}_{entity}_{suffix}`
- **Module Prefixes**: Mỗi module có prefix riêng để phân tách chức năng
- **Case**: snake_case
- **Language**: English
- **Plural**: Sử dụng số ít (singular)

#### Module-Based Prefixes

**Core System Modules:**
```sql
-- Tenant Management
tenant_organizations
tenant_subscriptions
tenant_domains

-- Website Management
website_sites
website_themes
website_pages
website_menus
website_settings

-- User Management
user_accounts
user_profiles
user_preferences
user_activities

-- Authentication
auth_sessions
auth_tokens
auth_password_resets
auth_login_attempts
```

**Content & Communication Modules:**
```sql
-- Blog Content (keep existing blog_ prefix)
blog_posts
blog_categories
blog_tags
blog_comments

-- Media Management
media_files
media_folders
media_variants

-- Notifications
notification_templates
notification_messages
notification_logs

-- Email System
email_templates
email_campaigns
email_queue
email_tracking
```

**Feature Modules:**
```sql
-- RBAC (Role-Based Access Control)
rbac_roles
rbac_permissions
rbac_user_roles

-- API Management
api_keys
api_key_permissions
api_key_usage

-- Payment System
payment_transactions
payment_subscriptions
payment_plans

-- SEO Optimization
seo_meta_tags
seo_redirects
seo_sitemaps

-- Real-time Communication
socket_connections
socket_rooms
socket_messages

-- User Onboarding
onboarding_journeys
onboarding_steps
onboarding_progress

-- System Tables
system_migrations
system_settings
system_logs
```

#### Naming Examples
```sql
-- GOOD (Module-based naming)
user_accounts
auth_sessions
rbac_roles
media_files
notification_templates

-- BAD (Generic or inconsistent naming)
users
sessions
roles
files
notifications
```

### Column Names
- **Format**: snake_case
- **Language**: English
- **Descriptive**: Tên rõ ràng, tránh viết tắt
- **Boolean**: Prefix với `is_`, `has_`, `can_`

```sql
-- GOOD
user_id
email_address
is_active
has_avatar
created_at

-- BAD
usrId
email
active
avatar
createdAt
```

### Index Names
- **Format**: `idx_{table}_{column(s)}`
- **Unique**: `uk_{table}_{column(s)}`
- **Foreign Key**: `fk_{table}_{ref_table}`

```sql
-- Examples with module-based table names
idx_user_accounts_email
idx_blog_posts_website_id_status
uk_user_accounts_email_website_id
fk_blog_posts_user_accounts
idx_auth_sessions_user_id_expires
idx_rbac_user_roles_user_id_role_id
```

## Data Types Standards

### Primary Keys
```sql
-- Tất cả tables sử dụng auto-increment primary key
id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY
```

### Common Fields
```sql
-- Tenant isolation (required for all tables)
website_id INT UNSIGNED NOT NULL,

-- Timestamps (required for all tables)
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

-- Soft delete (optional)
deleted_at TIMESTAMP NULL,

-- Audit fields (optional)
created_by INT UNSIGNED NULL,
updated_by INT UNSIGNED NULL,
```

### String Fields
```sql
-- Short strings
title VARCHAR(255) NOT NULL,
name VARCHAR(100) NOT NULL,
email VARCHAR(320) NOT NULL,
phone VARCHAR(20) NULL,

-- Medium strings
description TEXT NULL,
summary VARCHAR(500) NULL,

-- Long content
content LONGTEXT NULL,
bio TEXT NULL,

-- Fixed length
uuid CHAR(36) NOT NULL,
hash CHAR(64) NOT NULL,
```

### Numeric Fields
```sql
-- Integers
count INT UNSIGNED DEFAULT 0,
sort_order SMALLINT UNSIGNED DEFAULT 0,
level TINYINT UNSIGNED DEFAULT 0,

-- Decimals
price DECIMAL(10,2) DEFAULT 0.00,
percentage DECIMAL(5,2) DEFAULT 0.00,

-- Boolean (sử dụng TINYINT)
is_active TINYINT(1) DEFAULT 1,
is_featured TINYINT(1) DEFAULT 0,
```

### Date/Time Fields
```sql
-- Timestamps
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
deleted_at TIMESTAMP NULL,

-- Dates
birth_date DATE NULL,
published_date DATE NULL,

-- DateTime
scheduled_at DATETIME NULL,
expires_at DATETIME NULL,
```

### JSON Fields
```sql
-- Configuration
settings JSON NULL,
metadata JSON NULL,
preferences JSON NULL,

-- Arrays
tags JSON NULL,
permissions JSON NULL,
```

## Enum Values & Status Fields

### Common Status Enums

#### General Status
```sql
status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'active'
```

#### Content Status
```sql
status ENUM('draft', 'review', 'published', 'archived', 'deleted') DEFAULT 'draft'
```

#### User Status
```sql
status ENUM('active', 'inactive', 'pending', 'suspended', 'banned') DEFAULT 'pending'
```

#### Payment Status
```sql
status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending'
```

#### Notification Status
```sql
status ENUM('pending', 'sent', 'failed', 'delivered', 'read') DEFAULT 'pending'
```

### Visibility Enums
```sql
visibility ENUM('public', 'private', 'protected', 'draft') DEFAULT 'public'
```

### Priority Enums
```sql
priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal'
```

### Type Enums
```sql
-- User types
user_type ENUM('admin', 'editor', 'author', 'subscriber') DEFAULT 'subscriber'

-- Content types
content_type ENUM('post', 'page', 'product', 'event') DEFAULT 'post'

-- Media types
media_type ENUM('image', 'video', 'audio', 'document', 'archive') DEFAULT 'image'
```

## Database Schema Structure

### Core Tables

#### 1. Tenants & Websites
```sql
-- Tenant Organizations
CREATE TABLE tenant_organizations (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'Tenant name',
    slug VARCHAR(100) NOT NULL COMMENT 'URL-friendly identifier',
    email VARCHAR(320) NOT NULL COMMENT 'Contact email',
    phone VARCHAR(20) NULL COMMENT 'Contact phone',
    logo_url VARCHAR(500) NULL COMMENT 'Logo image URL',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT 'Tenant status',
    plan_type ENUM('free', 'basic', 'pro', 'enterprise') DEFAULT 'free' COMMENT 'Subscription plan',
    settings JSON NULL COMMENT 'Tenant configuration',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    UNIQUE KEY uk_tenant_organizations_slug (slug),
    UNIQUE KEY uk_tenant_organizations_email (email),
    INDEX idx_tenant_organizations_status (status),
    INDEX idx_tenant_organizations_plan_type (plan_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tenant organizations';

-- Website Sites (Sites under tenant)
CREATE TABLE website_sites (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL COMMENT 'Parent tenant ID',
    name VARCHAR(100) NOT NULL COMMENT 'Website name',
    slug VARCHAR(100) NOT NULL COMMENT 'URL-friendly identifier',
    domain VARCHAR(255) NOT NULL COMMENT 'Primary domain',
    alternate_domains JSON NULL COMMENT 'Alternative domains',
    title VARCHAR(255) NOT NULL COMMENT 'Website title',
    description TEXT NULL COMMENT 'Website description',
    logo_url VARCHAR(500) NULL COMMENT 'Logo image URL',
    favicon_url VARCHAR(500) NULL COMMENT 'Favicon URL',
    theme_config JSON NULL COMMENT 'Theme configuration',
    seo_config JSON NULL COMMENT 'SEO configuration',
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active' COMMENT 'Website status',
    is_default TINYINT(1) DEFAULT 0 COMMENT 'Is default website for tenant',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY fk_website_sites_tenant (tenant_id) REFERENCES tenant_organizations(id) ON DELETE CASCADE,
    UNIQUE KEY uk_website_sites_domain (domain),
    UNIQUE KEY uk_website_sites_tenant_slug (tenant_id, slug),
    INDEX idx_website_sites_status (status),
    INDEX idx_website_sites_tenant_id (tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Website sites under tenants';
```

#### 2. Users & Authentication
```sql
-- User Accounts
CREATE TABLE user_accounts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID for tenant isolation',
    email VARCHAR(320) NOT NULL COMMENT 'User email address',
    email_verified_at TIMESTAMP NULL COMMENT 'Email verification timestamp',
    password_hash VARCHAR(255) NOT NULL COMMENT 'Hashed password',
    first_name VARCHAR(50) NOT NULL COMMENT 'User first name',
    last_name VARCHAR(50) NOT NULL COMMENT 'User last name',
    username VARCHAR(50) NULL COMMENT 'Unique username',
    avatar_url VARCHAR(500) NULL COMMENT 'Avatar image URL',
    bio TEXT NULL COMMENT 'User biography',
    phone VARCHAR(20) NULL COMMENT 'Phone number',
    birth_date DATE NULL COMMENT 'Date of birth',
    gender ENUM('male', 'female', 'other') NULL COMMENT 'Gender',
    timezone VARCHAR(50) DEFAULT 'UTC' COMMENT 'User timezone',
    locale VARCHAR(10) DEFAULT 'en' COMMENT 'User locale',
    status ENUM('active', 'inactive', 'pending', 'suspended', 'banned') DEFAULT 'pending' COMMENT 'User status',
    is_verified TINYINT(1) DEFAULT 0 COMMENT 'Is email verified',
    is_admin TINYINT(1) DEFAULT 0 COMMENT 'Is admin user',
    last_login_at TIMESTAMP NULL COMMENT 'Last login timestamp',
    last_login_ip VARCHAR(45) NULL COMMENT 'Last login IP address',
    failed_login_attempts INT UNSIGNED DEFAULT 0 COMMENT 'Failed login attempts count',
    locked_until TIMESTAMP NULL COMMENT 'Account lock expiration',
    password_changed_at TIMESTAMP NULL COMMENT 'Password last changed',
    preferences JSON NULL COMMENT 'User preferences',
    metadata JSON NULL COMMENT 'Additional user data',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY fk_user_accounts_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_accounts_email_website (email, website_id),
    UNIQUE KEY uk_user_accounts_username_website (username, website_id),
    INDEX idx_user_accounts_website_id (website_id),
    INDEX idx_user_accounts_status (status),
    INDEX idx_user_accounts_last_login (last_login_at),
    INDEX idx_user_accounts_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User accounts';

-- Authentication Sessions
CREATE TABLE auth_sessions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    user_id INT UNSIGNED NOT NULL COMMENT 'User ID',
    session_token VARCHAR(255) NOT NULL COMMENT 'Session token',
    refresh_token VARCHAR(255) NOT NULL COMMENT 'Refresh token',
    device_info JSON NULL COMMENT 'Device information',
    ip_address VARCHAR(45) NULL COMMENT 'IP address',
    user_agent TEXT NULL COMMENT 'User agent string',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'Is session active',
    expires_at TIMESTAMP NOT NULL COMMENT 'Session expiration',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY fk_auth_sessions_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    FOREIGN KEY fk_auth_sessions_user (user_id) REFERENCES user_accounts(id) ON DELETE CASCADE,
    UNIQUE KEY uk_auth_sessions_token (session_token),
    UNIQUE KEY uk_auth_sessions_refresh (refresh_token),
    INDEX idx_auth_sessions_website_id (website_id),
    INDEX idx_auth_sessions_user_id (user_id),
    INDEX idx_auth_sessions_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Authentication sessions';
```

#### 3. RBAC (Role-Based Access Control)
```sql
-- RBAC Roles
CREATE TABLE rbac_roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    name VARCHAR(100) NOT NULL COMMENT 'Role name',
    slug VARCHAR(100) NOT NULL COMMENT 'Role slug',
    description TEXT NULL COMMENT 'Role description',
    is_system TINYINT(1) DEFAULT 0 COMMENT 'Is system role (cannot be deleted)',
    is_default TINYINT(1) DEFAULT 0 COMMENT 'Is default role for new users',
    level TINYINT UNSIGNED DEFAULT 0 COMMENT 'Role hierarchy level',
    permissions JSON NULL COMMENT 'Role permissions',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY fk_rbac_roles_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_rbac_roles_slug_website (slug, website_id),
    INDEX idx_rbac_roles_website_id (website_id),
    INDEX idx_rbac_roles_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC roles';

-- RBAC User Roles (Many-to-Many)
CREATE TABLE rbac_user_roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    user_id INT UNSIGNED NOT NULL COMMENT 'User ID',
    role_id INT UNSIGNED NOT NULL COMMENT 'Role ID',
    assigned_by INT UNSIGNED NULL COMMENT 'User who assigned role',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Assignment timestamp',
    expires_at TIMESTAMP NULL COMMENT 'Role expiration',

    FOREIGN KEY fk_rbac_user_roles_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    FOREIGN KEY fk_rbac_user_roles_user (user_id) REFERENCES user_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY fk_rbac_user_roles_role (role_id) REFERENCES rbac_roles(id) ON DELETE CASCADE,
    FOREIGN KEY fk_rbac_user_roles_assigned_by (assigned_by) REFERENCES user_accounts(id) ON DELETE SET NULL,
    UNIQUE KEY uk_rbac_user_roles_user_role (user_id, role_id),
    INDEX idx_rbac_user_roles_website_id (website_id),
    INDEX idx_rbac_user_roles_user_id (user_id),
    INDEX idx_rbac_user_roles_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC user role assignments';
```

#### 4. Blog Content
```sql
-- Categories (Nested Set Model)
CREATE TABLE blog_category (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    name VARCHAR(100) NOT NULL COMMENT 'Category name',
    slug VARCHAR(100) NOT NULL COMMENT 'URL-friendly slug',
    description TEXT NULL COMMENT 'Category description',
    parent_id INT UNSIGNED NULL COMMENT 'Parent category ID',
    lft INT UNSIGNED NOT NULL COMMENT 'Left boundary (nested set)',
    rgt INT UNSIGNED NOT NULL COMMENT 'Right boundary (nested set)',
    level TINYINT UNSIGNED DEFAULT 0 COMMENT 'Category level',
    sort_order SMALLINT UNSIGNED DEFAULT 0 COMMENT 'Sort order',
    image_url VARCHAR(500) NULL COMMENT 'Category image URL',
    meta_title VARCHAR(255) NULL COMMENT 'SEO meta title',
    meta_description VARCHAR(500) NULL COMMENT 'SEO meta description',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'Is category active',
    post_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of posts in category',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_category_website (website_id) REFERENCES blog_website(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_category_parent (parent_id) REFERENCES blog_category(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_category_slug_website (slug, website_id),
    INDEX idx_blog_category_website_id (website_id),
    INDEX idx_blog_category_parent_id (parent_id),
    INDEX idx_blog_category_nested_set (lft, rgt),
    INDEX idx_blog_category_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Blog categories';

-- Tags
CREATE TABLE blog_tag (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    name VARCHAR(100) NOT NULL COMMENT 'Tag name',
    slug VARCHAR(100) NOT NULL COMMENT 'URL-friendly slug',
    description TEXT NULL COMMENT 'Tag description',
    color VARCHAR(7) NULL COMMENT 'Tag color (hex)',
    post_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of posts with tag',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_tag_website (website_id) REFERENCES blog_website(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_tag_slug_website (slug, website_id),
    INDEX idx_blog_tag_website_id (website_id),
    INDEX idx_blog_tag_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Blog tags';

-- Posts
CREATE TABLE blog_post (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    title VARCHAR(255) NOT NULL COMMENT 'Post title',
    slug VARCHAR(255) NOT NULL COMMENT 'URL-friendly slug',
    excerpt TEXT NULL COMMENT 'Post excerpt',
    content LONGTEXT NULL COMMENT 'Post content',
    content_type ENUM('html', 'markdown', 'json') DEFAULT 'html' COMMENT 'Content format',
    author_id INT UNSIGNED NOT NULL COMMENT 'Author user ID',
    category_id INT UNSIGNED NULL COMMENT 'Primary category ID',
    featured_image_url VARCHAR(500) NULL COMMENT 'Featured image URL',
    status ENUM('draft', 'review', 'published', 'archived') DEFAULT 'draft' COMMENT 'Post status',
    visibility ENUM('public', 'private', 'protected', 'password') DEFAULT 'public' COMMENT 'Post visibility',
    password VARCHAR(255) NULL COMMENT 'Password for protected posts',
    is_featured TINYINT(1) DEFAULT 0 COMMENT 'Is featured post',
    is_sticky TINYINT(1) DEFAULT 0 COMMENT 'Is sticky post',
    allow_comments TINYINT(1) DEFAULT 1 COMMENT 'Allow comments',
    view_count INT UNSIGNED DEFAULT 0 COMMENT 'View count',
    like_count INT UNSIGNED DEFAULT 0 COMMENT 'Like count',
    comment_count INT UNSIGNED DEFAULT 0 COMMENT 'Comment count',
    share_count INT UNSIGNED DEFAULT 0 COMMENT 'Share count',
    reading_time INT UNSIGNED DEFAULT 0 COMMENT 'Estimated reading time (minutes)',
    published_at TIMESTAMP NULL COMMENT 'Published timestamp',
    scheduled_at TIMESTAMP NULL COMMENT 'Scheduled publish timestamp',
    meta_title VARCHAR(255) NULL COMMENT 'SEO meta title',
    meta_description VARCHAR(500) NULL COMMENT 'SEO meta description',
    meta_keywords VARCHAR(500) NULL COMMENT 'SEO meta keywords',
    seo_score TINYINT UNSIGNED DEFAULT 0 COMMENT 'SEO score (0-100)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY fk_blog_post_website (website_id) REFERENCES blog_website(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_post_author (author_id) REFERENCES blog_user(id) ON DELETE RESTRICT,
    FOREIGN KEY fk_blog_post_category (category_id) REFERENCES blog_category(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_post_slug_website (slug, website_id),
    INDEX idx_blog_post_website_id (website_id),
    INDEX idx_blog_post_author_id (author_id),
    INDEX idx_blog_post_category_id (category_id),
    INDEX idx_blog_post_status (status),
    INDEX idx_blog_post_published_at (published_at),
    INDEX idx_blog_post_featured (is_featured),
    INDEX idx_blog_post_sticky (is_sticky),
    FULLTEXT idx_blog_post_search (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Blog posts';

-- Post Tags (Many-to-Many)
CREATE TABLE blog_post_tag (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    post_id INT UNSIGNED NOT NULL COMMENT 'Post ID',
    tag_id INT UNSIGNED NOT NULL COMMENT 'Tag ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY fk_blog_post_tag_website (website_id) REFERENCES blog_website(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_post_tag_post (post_id) REFERENCES blog_post(id) ON DELETE CASCADE,
    FOREIGN KEY fk_blog_post_tag_tag (tag_id) REFERENCES blog_tag(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_post_tag_post_tag (post_id, tag_id),
    INDEX idx_blog_post_tag_website_id (website_id),
    INDEX idx_blog_post_tag_post_id (post_id),
    INDEX idx_blog_post_tag_tag_id (tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Post tag relationships';
```

#### 5. Media & Assets
```sql
-- Media Files
CREATE TABLE media_files (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    user_id INT UNSIGNED NOT NULL COMMENT 'User who uploaded',
    filename VARCHAR(255) NOT NULL COMMENT 'Original filename',
    stored_filename VARCHAR(255) NOT NULL COMMENT 'Stored filename',
    title VARCHAR(255) NULL COMMENT 'Media title',
    alt_text VARCHAR(255) NULL COMMENT 'Alt text for images',
    description TEXT NULL COMMENT 'Media description',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME type',
    file_size INT UNSIGNED NOT NULL COMMENT 'File size in bytes',
    width INT UNSIGNED NULL COMMENT 'Image width',
    height INT UNSIGNED NULL COMMENT 'Image height',
    duration INT UNSIGNED NULL COMMENT 'Video/audio duration in seconds',
    storage_driver VARCHAR(20) DEFAULT 'local' COMMENT 'Storage driver (local, s3, etc.)',
    storage_path VARCHAR(500) NOT NULL COMMENT 'Storage path',
    public_url VARCHAR(500) NOT NULL COMMENT 'Public URL',
    cdn_url VARCHAR(500) NULL COMMENT 'CDN URL',
    variants JSON NULL COMMENT 'Image variants (thumbnails, etc.)',
    metadata JSON NULL COMMENT 'File metadata',
    folder_path VARCHAR(500) DEFAULT '/' COMMENT 'Folder path',
    is_optimized TINYINT(1) DEFAULT 0 COMMENT 'Is optimized',
    download_count INT UNSIGNED DEFAULT 0 COMMENT 'Download count',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY fk_media_files_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    FOREIGN KEY fk_media_files_user (user_id) REFERENCES user_accounts(id) ON DELETE RESTRICT,
    INDEX idx_media_files_website_id (website_id),
    INDEX idx_media_files_user_id (user_id),
    INDEX idx_media_files_mime_type (mime_type),
    INDEX idx_media_files_folder_path (folder_path),
    INDEX idx_media_files_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Media files';
```

#### 6. Notifications
```sql
-- Notification Templates
CREATE TABLE notification_templates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    name VARCHAR(100) NOT NULL COMMENT 'Template name',
    slug VARCHAR(100) NOT NULL COMMENT 'Template slug',
    subject VARCHAR(255) NOT NULL COMMENT 'Email subject',
    body_html LONGTEXT NULL COMMENT 'HTML body',
    body_text LONGTEXT NULL COMMENT 'Plain text body',
    variables JSON NULL COMMENT 'Available variables',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'Is template active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY fk_notification_templates_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_notification_templates_slug_website (slug, website_id),
    INDEX idx_notification_templates_website_id (website_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Notification templates';

-- Notification Messages
CREATE TABLE notification_messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL COMMENT 'Website ID',
    user_id INT UNSIGNED NOT NULL COMMENT 'Recipient user ID',
    type VARCHAR(50) NOT NULL COMMENT 'Notification type',
    title VARCHAR(255) NOT NULL COMMENT 'Notification title',
    message TEXT NULL COMMENT 'Notification message',
    data JSON NULL COMMENT 'Additional data',
    channels JSON NULL COMMENT 'Delivery channels',
    status ENUM('pending', 'sent', 'failed', 'delivered', 'read') DEFAULT 'pending' COMMENT 'Status',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT 'Priority',
    scheduled_at TIMESTAMP NULL COMMENT 'Scheduled delivery time',
    sent_at TIMESTAMP NULL COMMENT 'Sent timestamp',
    read_at TIMESTAMP NULL COMMENT 'Read timestamp',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY fk_notification_messages_website (website_id) REFERENCES website_sites(id) ON DELETE CASCADE,
    FOREIGN KEY fk_notification_messages_user (user_id) REFERENCES user_accounts(id) ON DELETE CASCADE,
    INDEX idx_notification_messages_website_id (website_id),
    INDEX idx_notification_messages_user_id (user_id),
    INDEX idx_notification_messages_type (type),
    INDEX idx_notification_messages_status (status),
    INDEX idx_notification_messages_scheduled_at (scheduled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Notification messages';
```

## Database Constraints & Rules

### Foreign Key Constraints

#### Foreign Key Strategy
**KEEP Foreign Keys for data integrity** but implement deadlock prevention strategies:

```sql
-- Always use descriptive constraint names
CONSTRAINT fk_blog_post_author FOREIGN KEY (author_id) REFERENCES blog_user(id) ON DELETE RESTRICT

-- Use appropriate referential actions
ON DELETE CASCADE  -- When parent is deleted, child is deleted
ON DELETE RESTRICT -- Prevent deletion of parent if child exists
ON DELETE SET NULL -- Set foreign key to NULL when parent is deleted
```

#### When to Keep Foreign Keys
- **Critical relationships**: tenant-website, user-role
- **Core business logic**: post-author, order-customer
- **Audit requirements**: financial transactions
- **Data integrity critical**: user sessions, permissions

#### When to Consider Removing Foreign Keys
- **Log tables**: activity_logs, audit_logs, access_logs
- **Analytics tables**: page_views, click_events, metrics
- **Cache tables**: temporary data, session cache
- **High-volume tables**: notifications, queue_jobs
- **Event sourcing tables**: event_store, message_queue

#### Deadlock Prevention Strategies

**1. Consistent Lock Ordering**
```sql
-- Always lock tables in same order: tenant -> website -> user -> post
BEGIN;
SELECT * FROM blog_tenant WHERE id = 1 FOR UPDATE;
SELECT * FROM blog_website WHERE tenant_id = 1 FOR UPDATE;
SELECT * FROM blog_user WHERE website_id = 1 FOR UPDATE;
COMMIT;
```

**2. Shorter Transactions**
```go
// BAD - long transaction increases deadlock risk
func updateUserAndPosts() error {
    tx := db.Begin()
    defer tx.Rollback()
    
    // Update user
    tx.Model(&user).Updates(userData)
    // ... many other operations
    // Update posts
    tx.Model(&posts).Updates(postData)
    
    return tx.Commit().Error
}

// GOOD - shorter, focused transactions
func updateUserAndPosts() error {
    // Update user first
    if err := updateUser(); err != nil {
        return err
    }
    
    // Update posts separately
    return updatePosts()
}
```

**3. Deadlock Detection & Retry**
```go
func executeWithDeadlockRetry(fn func() error) error {
    maxRetries := 3
    for i := 0; i < maxRetries; i++ {
        err := fn()
        if err == nil {
            return nil
        }
        
        // Check if deadlock error (MySQL error 1213, PostgreSQL 40P01)
        if isDeadlockError(err) {
            backoff := time.Duration(i*100+rand.Intn(100)) * time.Millisecond
            time.Sleep(backoff)
            continue
        }
        
        return err
    }
    return errors.New("max retries exceeded due to deadlocks")
}

func isDeadlockError(err error) bool {
    errStr := err.Error()
    return strings.Contains(errStr, "Deadlock found") || 
           strings.Contains(errStr, "deadlock detected")
}
```

**4. Proper Index Strategy**
```sql
-- Foreign key columns must be indexed for performance
CREATE INDEX idx_blog_post_website_id ON blog_post(website_id);
CREATE INDEX idx_blog_post_author_id ON blog_post(author_id);
CREATE INDEX idx_blog_post_category_id ON blog_post(category_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_blog_post_website_status ON blog_post(website_id, status);
CREATE INDEX idx_blog_post_author_created ON blog_post(author_id, created_at);
```

**5. Alternative: Soft Foreign Keys**
```sql
-- For high-volume, non-critical tables: no FK constraint but still validate
CREATE TABLE blog_activity_log (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL, -- No FK constraint
    user_id INT UNSIGNED NOT NULL,    -- No FK constraint
    action VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Still index for performance
    INDEX idx_blog_activity_log_website_id (website_id),
    INDEX idx_blog_activity_log_user_id (user_id),
    INDEX idx_blog_activity_log_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Activity logs - no FK for performance';

-- Validate in application layer
func validateActivityLog(log *ActivityLog) error {
    // Check website exists
    var count int64
    db.Model(&Website{}).Where("id = ?", log.WebsiteID).Count(&count)
    if count == 0 {
        return errors.New("invalid website_id")
    }
    
    // Check user exists  
    db.Model(&User{}).Where("id = ?", log.UserID).Count(&count)
    if count == 0 {
        return errors.New("invalid user_id")
    }
    
    return nil
}
```

#### Monitoring Deadlocks
```sql
-- Monitor deadlock frequency
SHOW ENGINE INNODB STATUS;

-- Check for deadlock patterns
SELECT 
    OBJECT_SCHEMA,
    OBJECT_NAME,
    COUNT_READ,
    COUNT_WRITE,
    SUM_TIMER_WAIT
FROM performance_schema.table_io_waits_summary_by_table
WHERE OBJECT_SCHEMA = 'blog_api_v3'
ORDER BY SUM_TIMER_WAIT DESC;
```

### Check Constraints
```sql
-- Validate enum values
ALTER TABLE blog_posts ADD CONSTRAINT chk_blog_posts_status
CHECK (status IN ('draft', 'review', 'published', 'archived'));

-- Validate numeric ranges
ALTER TABLE blog_posts ADD CONSTRAINT chk_blog_posts_seo_score
CHECK (seo_score >= 0 AND seo_score <= 100);

-- Validate dates
ALTER TABLE user_accounts ADD CONSTRAINT chk_user_accounts_birth_date
CHECK (birth_date < CURDATE());
```

### Default Values
```sql
-- Use meaningful defaults
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
status ENUM('active', 'inactive') DEFAULT 'active',
sort_order SMALLINT UNSIGNED DEFAULT 0,
is_active TINYINT(1) DEFAULT 1
```

## Indexing Strategy

### Primary Indexes
```sql
-- Auto-increment primary key
id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY

-- Composite primary key (for junction tables)
PRIMARY KEY (user_id, role_id)
```

### Unique Indexes
```sql
-- Single column unique
UNIQUE KEY uk_blog_user_email (email)

-- Composite unique with tenant isolation
UNIQUE KEY uk_blog_user_email_website (email, website_id)
```

### Performance Indexes
```sql
-- Foreign key indexes
INDEX idx_blog_post_author_id (author_id)
INDEX idx_blog_post_category_id (category_id)

-- Query optimization indexes
INDEX idx_blog_post_website_status (website_id, status)
INDEX idx_blog_post_published_at (published_at)

-- Covering indexes
INDEX idx_blog_post_list (website_id, status, published_at, id)
```

### Full-Text Search Indexes
```sql
-- Full-text search for content
FULLTEXT idx_blog_post_search (title, content)

-- Usage
SELECT * FROM blog_post 
WHERE MATCH(title, content) AGAINST('search term' IN BOOLEAN MODE);
```

## Database Comments

### Table Comments
```sql
-- Descriptive table comments
COMMENT='User accounts and profiles'
COMMENT='Blog posts and articles'
COMMENT='File and media storage'
```

### Column Comments
```sql
-- Explain purpose and format
user_id INT UNSIGNED NOT NULL COMMENT 'User ID from user_accounts table',
email VARCHAR(320) NOT NULL COMMENT 'User email address (max 320 chars per RFC)',
status ENUM('active', 'inactive') DEFAULT 'active' COMMENT 'Account status',
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
```

## Migration Best Practices

### Migration Files
```sql
-- File naming: YYYY_MM_DD_HHMMSS_description.sql
-- Example: 2024_01_15_143000_create_user_accounts_table.sql

-- Always include rollback instructions
-- UP migration
CREATE TABLE user_accounts (...);

-- DOWN migration (comment)
-- DROP TABLE user_accounts;
```

### Schema Changes
```sql
-- Add columns with default values
ALTER TABLE blog_user ADD COLUMN timezone VARCHAR(50) DEFAULT 'UTC';

-- Modify columns safely
ALTER TABLE blog_user MODIFY COLUMN email VARCHAR(320) NOT NULL;

-- Add indexes
CREATE INDEX idx_blog_user_email ON blog_user(email);
```

## Best Practices Summary

1. **Naming**: Consistent snake_case naming với module-based prefixes
2. **Module Prefixes**: Use appropriate prefixes (user_, auth_, rbac_, etc.) for logical grouping
3. **Data Types**: Sử dụng appropriate data types cho performance
4. **Indexes**: Index foreign keys và query columns with module-aware naming
5. **Constraints**: Validate data integrity với constraints
6. **Comments**: Document schema với meaningful comments
7. **Tenant Isolation**: Always include website_id cho multi-tenancy
8. **Timestamps**: Include created_at/updated_at trên mọi table
9. **Soft Deletes**: Sử dụng deleted_at cho important data
10. **Migrations**: Version control schema changes with module organization