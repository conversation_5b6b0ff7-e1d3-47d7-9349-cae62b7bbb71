# Database Seeding

## Overview

Database seeding is the process of populating your database with initial or test data. The Blog API v3 uses a structured seeding system that allows you to create consistent datasets for development, testing, and production environments.

## Seeding System Architecture

### Seeder Interface

```go
// pkg/database/seeds/seeder.go
package seeds

import (
    "gorm.io/gorm"
)

type Seeder interface {
    Seed(db *gorm.DB) error
    Rollback(db *gorm.DB) error
    Name() string
    Dependencies() []string
}

type BaseSeeder struct {
    name         string
    dependencies []string
}

func (s *BaseSeeder) Name() string {
    return s.name
}

func (s *BaseSeeder) Dependencies() []string {
    return s.dependencies
}
```

### Seeder Manager

```go
// pkg/database/seeds/manager.go
package seeds

import (
    "fmt"
    "gorm.io/gorm"
    "sort"
)

type Manager struct {
    db      *gorm.DB
    seeders map[string]Seeder
    order   []string
}

func NewManager(db *gorm.DB) *Manager {
    return &Manager{
        db:      db,
        seeders: make(map[string]Seeder),
        order:   []string{},
    }
}

func (m *Manager) Register(seeder Seeder) error {
    name := seeder.Name()
    if _, exists := m.seeders[name]; exists {
        return fmt.Errorf("seeder %s already registered", name)
    }
    
    m.seeders[name] = seeder
    m.resolveDependencies()
    return nil
}

func (m *Manager) SeedAll() error {
    for _, name := range m.order {
        seeder := m.seeders[name]
        fmt.Printf("Running seeder: %s\n", name)
        
        if err := seeder.Seed(m.db); err != nil {
            return fmt.Errorf("seeder %s failed: %w", name, err)
        }
        
        fmt.Printf("Seeder completed: %s\n", name)
    }
    return nil
}

func (m *Manager) Seed(names ...string) error {
    for _, name := range names {
        seeder, exists := m.seeders[name]
        if !exists {
            return fmt.Errorf("seeder %s not found", name)
        }
        
        // Check dependencies
        for _, dep := range seeder.Dependencies() {
            if err := m.Seed(dep); err != nil {
                return err
            }
        }
        
        fmt.Printf("Running seeder: %s\n", name)
        if err := seeder.Seed(m.db); err != nil {
            return fmt.Errorf("seeder %s failed: %w", name, err)
        }
    }
    return nil
}

func (m *Manager) Rollback(names ...string) error {
    // Reverse order for rollback
    for i := len(names) - 1; i >= 0; i-- {
        name := names[i]
        seeder, exists := m.seeders[name]
        if !exists {
            return fmt.Errorf("seeder %s not found", name)
        }
        
        fmt.Printf("Rolling back seeder: %s\n", name)
        if err := seeder.Rollback(m.db); err != nil {
            return fmt.Errorf("rollback %s failed: %w", name, err)
        }
    }
    return nil
}

func (m *Manager) resolveDependencies() {
    // Topological sort to resolve dependencies
    visited := make(map[string]bool)
    temp := make(map[string]bool)
    m.order = []string{}
    
    var visit func(string) error
    visit = func(name string) error {
        if temp[name] {
            return fmt.Errorf("circular dependency detected: %s", name)
        }
        if visited[name] {
            return nil
        }
        
        temp[name] = true
        seeder := m.seeders[name]
        
        for _, dep := range seeder.Dependencies() {
            if err := visit(dep); err != nil {
                return err
            }
        }
        
        temp[name] = false
        visited[name] = true
        m.order = append(m.order, name)
        return nil
    }
    
    for name := range m.seeders {
        if !visited[name] {
            visit(name)
        }
    }
}
```

## Seeder Examples

### 1. User Seeder

```go
// pkg/database/seeds/user_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "golang.org/x/crypto/bcrypt"
    "blog-api-v3/internal/modules/auth"
)

type UserSeeder struct {
    BaseSeeder
}

func NewUserSeeder() *UserSeeder {
    return &UserSeeder{
        BaseSeeder: BaseSeeder{
            name:         "users",
            dependencies: []string{},
        },
    }
}

func (s *UserSeeder) Seed(db *gorm.DB) error {
    users := []auth.User{
        {
            Name:     "Admin User",
            Email:    "<EMAIL>",
            Role:     "admin",
            Status:   "active",
        },
        {
            Name:     "John Doe",
            Email:    "<EMAIL>",
            Role:     "user",
            Status:   "active",
        },
        {
            Name:     "Jane Smith",
            Email:    "<EMAIL>",
            Role:     "moderator",
            Status:   "active",
        },
        {
            Name:     "Bob Wilson",
            Email:    "<EMAIL>",
            Role:     "user",
            Status:   "active",
        },
    }
    
    for i := range users {
        // Hash password
        hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
        if err != nil {
            return err
        }
        users[i].Password = string(hashedPassword)
        
        // Check if user already exists
        var existing auth.User
        if err := db.Where("email = ?", users[i].Email).First(&existing).Error; err == nil {
            continue // Skip if exists
        }
        
        if err := db.Create(&users[i]).Error; err != nil {
            return err
        }
    }
    
    return nil
}

func (s *UserSeeder) Rollback(db *gorm.DB) error {
    emails := []string{
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    }
    
    return db.Where("email IN ?", emails).Delete(&auth.User{}).Error
}
```

### 2. Category Seeder

```go
// pkg/database/seeds/category_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "blog-api-v3/internal/modules/blog"
    "blog-api-v3/pkg/utils"
)

type CategorySeeder struct {
    BaseSeeder
}

func NewCategorySeeder() *CategorySeeder {
    return &CategorySeeder{
        BaseSeeder: BaseSeeder{
            name:         "categories",
            dependencies: []string{},
        },
    }
}

func (s *CategorySeeder) Seed(db *gorm.DB) error {
    categories := []blog.Category{
        {
            Name:        "Technology",
            Slug:        "technology",
            Description: "Latest technology trends and news",
            Color:       "#3B82F6",
        },
        {
            Name:        "Programming",
            Slug:        "programming",
            Description: "Programming tutorials and best practices",
            Color:       "#10B981",
        },
        {
            Name:        "Web Development",
            Slug:        "web-development",
            Description: "Frontend and backend development topics",
            Color:       "#F59E0B",
        },
        {
            Name:        "DevOps",
            Slug:        "devops",
            Description: "DevOps practices and tools",
            Color:       "#EF4444",
        },
        {
            Name:        "Mobile Development",
            Slug:        "mobile-development",
            Description: "iOS and Android development",
            Color:       "#8B5CF6",
        },
        {
            Name:        "Data Science",
            Slug:        "data-science",
            Description: "Data analysis and machine learning",
            Color:       "#06B6D4",
        },
    }
    
    for _, category := range categories {
        var existing blog.Category
        if err := db.Where("slug = ?", category.Slug).First(&existing).Error; err == nil {
            continue // Skip if exists
        }
        
        if err := db.Create(&category).Error; err != nil {
            return err
        }
    }
    
    return nil
}

func (s *CategorySeeder) Rollback(db *gorm.DB) error {
    slugs := []string{
        "technology", "programming", "web-development",
        "devops", "mobile-development", "data-science",
    }
    
    return db.Where("slug IN ?", slugs).Delete(&blog.Category{}).Error
}
```

### 3. Tag Seeder

```go
// pkg/database/seeds/tag_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "blog-api-v3/internal/modules/blog"
)

type TagSeeder struct {
    BaseSeeder
}

func NewTagSeeder() *TagSeeder {
    return &TagSeeder{
        BaseSeeder: BaseSeeder{
            name:         "tags",
            dependencies: []string{},
        },
    }
}

func (s *TagSeeder) Seed(db *gorm.DB) error {
    tags := []blog.Tag{
        {Name: "Go", Slug: "go"},
        {Name: "JavaScript", Slug: "javascript"},
        {Name: "React", Slug: "react"},
        {Name: "Vue.js", Slug: "vuejs"},
        {Name: "Node.js", Slug: "nodejs"},
        {Name: "Python", Slug: "python"},
        {Name: "Docker", Slug: "docker"},
        {Name: "Kubernetes", Slug: "kubernetes"},
        {Name: "AWS", Slug: "aws"},
        {Name: "MySQL", Slug: "mysql"},
        {Name: "PostgreSQL", Slug: "postgresql"},
        {Name: "Redis", Slug: "redis"},
        {Name: "API", Slug: "api"},
        {Name: "REST", Slug: "rest"},
        {Name: "GraphQL", Slug: "graphql"},
        {Name: "Microservices", Slug: "microservices"},
        {Name: "Testing", Slug: "testing"},
        {Name: "CI/CD", Slug: "cicd"},
        {Name: "Security", Slug: "security"},
        {Name: "Performance", Slug: "performance"},
    }
    
    for _, tag := range tags {
        var existing blog.Tag
        if err := db.Where("slug = ?", tag.Slug).First(&existing).Error; err == nil {
            continue
        }
        
        if err := db.Create(&tag).Error; err != nil {
            return err
        }
    }
    
    return nil
}

func (s *TagSeeder) Rollback(db *gorm.DB) error {
    slugs := []string{
        "go", "javascript", "react", "vuejs", "nodejs", "python",
        "docker", "kubernetes", "aws", "mysql", "postgresql", "redis",
        "api", "rest", "graphql", "microservices", "testing", "cicd",
        "security", "performance",
    }
    
    return db.Where("slug IN ?", slugs).Delete(&blog.Tag{}).Error
}
```

### 4. Post Seeder

```go
// pkg/database/seeds/post_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "time"
    "math/rand"
    "blog-api-v3/internal/modules/blog"
    "blog-api-v3/internal/modules/auth"
)

type PostSeeder struct {
    BaseSeeder
}

func NewPostSeeder() *PostSeeder {
    return &PostSeeder{
        BaseSeeder: BaseSeeder{
            name:         "posts",
            dependencies: []string{"users", "categories", "tags"},
        },
    }
}

func (s *PostSeeder) Seed(db *gorm.DB) error {
    // Get existing users, categories, and tags
    var users []auth.User
    if err := db.Find(&users).Error; err != nil {
        return err
    }
    
    var categories []blog.Category
    if err := db.Find(&categories).Error; err != nil {
        return err
    }
    
    var tags []blog.Tag
    if err := db.Find(&tags).Error; err != nil {
        return err
    }
    
    posts := []blog.Post{
        {
            Title:   "Getting Started with Go",
            Slug:    "getting-started-with-go",
            Content: s.generateContent("Go is a powerful programming language..."),
            Excerpt: "Learn the basics of Go programming language and why it's perfect for modern applications.",
            Status:  "published",
            AuthorID: users[1].ID, // John Doe
            ViewCount: uint(rand.Intn(1000) + 100),
            LikeCount: uint(rand.Intn(50) + 10),
        },
        {
            Title:   "Building REST APIs with Gin",
            Slug:    "building-rest-apis-with-gin",
            Content: s.generateContent("Gin is a high-performance HTTP web framework..."),
            Excerpt: "A comprehensive guide to building REST APIs using the Gin framework in Go.",
            Status:  "published",
            AuthorID: users[1].ID,
            ViewCount: uint(rand.Intn(800) + 200),
            LikeCount: uint(rand.Intn(40) + 15),
        },
        {
            Title:   "Docker for Beginners",
            Slug:    "docker-for-beginners",
            Content: s.generateContent("Docker is a containerization platform..."),
            Excerpt: "Everything you need to know to get started with Docker containerization.",
            Status:  "published",
            AuthorID: users[2].ID, // Jane Smith
            ViewCount: uint(rand.Intn(1200) + 300),
            LikeCount: uint(rand.Intn(60) + 20),
        },
        {
            Title:   "Advanced React Patterns",
            Slug:    "advanced-react-patterns",
            Content: s.generateContent("React offers several advanced patterns..."),
            Excerpt: "Explore advanced React patterns to build more maintainable applications.",
            Status:  "published",
            AuthorID: users[3].ID, // Bob Wilson
            ViewCount: uint(rand.Intn(900) + 150),
            LikeCount: uint(rand.Intn(45) + 12),
        },
        {
            Title:   "Database Optimization Techniques",
            Slug:    "database-optimization-techniques",
            Content: s.generateContent("Database performance is crucial..."),
            Excerpt: "Learn essential techniques to optimize your database performance.",
            Status:  "draft",
            AuthorID: users[1].ID,
            ViewCount: 0,
            LikeCount: 0,
        },
    }
    
    for i, post := range posts {
        // Set published date for published posts
        if post.Status == "published" {
            publishedAt := time.Now().Add(-time.Duration(rand.Intn(30)) * 24 * time.Hour)
            posts[i].PublishedAt = &publishedAt
        }
        
        // Check if post exists
        var existing blog.Post
        if err := db.Where("slug = ?", post.Slug).First(&existing).Error; err == nil {
            continue
        }
        
        if err := db.Create(&posts[i]).Error; err != nil {
            return err
        }
        
        // Associate with random categories and tags
        s.associateCategories(db, &posts[i], categories)
        s.associateTags(db, &posts[i], tags)
    }
    
    return nil
}

func (s *PostSeeder) generateContent(intro string) string {
    return intro + `

## Introduction

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

## Key Points

1. **First Point**: Detailed explanation of the first important concept
2. **Second Point**: Another crucial aspect to understand
3. **Third Point**: Additional insights and best practices

## Code Example

` + "```go" + `
package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
}
` + "```" + `

## Best Practices

- Always follow coding standards
- Write comprehensive tests
- Document your code properly
- Consider performance implications

## Conclusion

This concludes our comprehensive guide. Remember to practice these concepts and build real projects to solidify your understanding.
`
}

func (s *PostSeeder) associateCategories(db *gorm.DB, post *blog.Post, categories []blog.Category) {
    if len(categories) == 0 {
        return
    }
    
    // Associate with 1-3 random categories
    numCategories := rand.Intn(3) + 1
    selectedCategories := make([]blog.Category, 0, numCategories)
    
    for i := 0; i < numCategories && i < len(categories); i++ {
        idx := rand.Intn(len(categories))
        selectedCategories = append(selectedCategories, categories[idx])
    }
    
    db.Model(post).Association("Categories").Append(selectedCategories)
}

func (s *PostSeeder) associateTags(db *gorm.DB, post *blog.Post, tags []blog.Tag) {
    if len(tags) == 0 {
        return
    }
    
    // Associate with 2-5 random tags
    numTags := rand.Intn(4) + 2
    selectedTags := make([]blog.Tag, 0, numTags)
    
    for i := 0; i < numTags && i < len(tags); i++ {
        idx := rand.Intn(len(tags))
        selectedTags = append(selectedTags, tags[idx])
    }
    
    db.Model(post).Association("Tags").Append(selectedTags)
}

func (s *PostSeeder) Rollback(db *gorm.DB) error {
    slugs := []string{
        "getting-started-with-go",
        "building-rest-apis-with-gin",
        "docker-for-beginners",
        "advanced-react-patterns",
        "database-optimization-techniques",
    }
    
    return db.Where("slug IN ?", slugs).Delete(&blog.Post{}).Error
}
```

### 5. Comment Seeder

```go
// pkg/database/seeds/comment_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "math/rand"
    "blog-api-v3/internal/modules/blog"
    "blog-api-v3/internal/modules/auth"
)

type CommentSeeder struct {
    BaseSeeder
}

func NewCommentSeeder() *CommentSeeder {
    return &CommentSeeder{
        BaseSeeder: BaseSeeder{
            name:         "comments",
            dependencies: []string{"users", "posts"},
        },
    }
}

func (s *CommentSeeder) Seed(db *gorm.DB) error {
    var users []auth.User
    if err := db.Find(&users).Error; err != nil {
        return err
    }
    
    var posts []blog.Post
    if err := db.Where("status = ?", "published").Find(&posts).Error; err != nil {
        return err
    }
    
    comments := []string{
        "Great article! Very informative and well-written.",
        "Thanks for sharing this. It helped me understand the concept better.",
        "Could you provide more examples on this topic?",
        "I've been looking for this information everywhere. Thank you!",
        "Excellent explanation. Looking forward to more content like this.",
        "This is exactly what I needed. Clear and concise.",
        "Amazing post! Keep up the good work.",
        "Very helpful tutorial. Step-by-step instructions are perfect.",
        "I learned something new today. Thanks for the detailed explanation.",
        "Brilliant work! This saved me hours of research.",
    }
    
    for _, post := range posts {
        // Add 2-8 comments per post
        numComments := rand.Intn(7) + 2
        
        for i := 0; i < numComments; i++ {
            comment := blog.Comment{
                Content: comments[rand.Intn(len(comments))],
                Status:  "approved",
                PostID:  post.ID,
                UserID:  users[rand.Intn(len(users))].ID,
            }
            
            if err := db.Create(&comment).Error; err != nil {
                return err
            }
            
            // 30% chance to add a reply
            if rand.Float32() < 0.3 {
                reply := blog.Comment{
                    Content:  "Thanks for your comment! I'm glad you found it helpful.",
                    Status:   "approved",
                    PostID:   post.ID,
                    UserID:   post.AuthorID,
                    ParentID: &comment.ID,
                }
                
                db.Create(&reply)
            }
        }
    }
    
    return nil
}

func (s *CommentSeeder) Rollback(db *gorm.DB) error {
    return db.Where("1 = 1").Delete(&blog.Comment{}).Error
}
```

### 6. Notification Template Seeder

```go
// pkg/database/seeds/notification_template_seeder.go
package seeds

import (
    "gorm.io/gorm"
    "encoding/json"
    "blog-api-v3/internal/modules/notification"
)

type NotificationTemplateSeeder struct {
    BaseSeeder
}

func NewNotificationTemplateSeeder() *NotificationTemplateSeeder {
    return &NotificationTemplateSeeder{
        BaseSeeder: BaseSeeder{
            name:         "notification_templates",
            dependencies: []string{},
        },
    }
}

func (s *NotificationTemplateSeeder) Seed(db *gorm.DB) error {
    templates := []notification.NotificationTemplate{
        {
            Name:     "welcome_email",
            Subject:  "Welcome to {{.AppName}}!",
            Body:     s.getWelcomeEmailTemplate(),
            Type:     "email",
            Category: "user_registration",
            Variables: s.jsonify(map[string]interface{}{
                "user_name": "string",
                "app_name":  "string",
                "app_url":   "string",
            }),
        },
        {
            Name:     "new_comment_notification",
            Subject:  "New comment on your post",
            Body:     "{{.CommenterName}} commented on your post '{{.PostTitle}}'",
            Type:     "database",
            Category: "post_comment",
            Variables: s.jsonify(map[string]interface{}{
                "commenter_name": "string",
                "post_title":     "string",
                "post_url":       "string",
            }),
        },
        {
            Name:     "password_reset_email",
            Subject:  "Reset your password",
            Body:     s.getPasswordResetTemplate(),
            Type:     "email",
            Category: "password_reset",
            Variables: s.jsonify(map[string]interface{}{
                "user_name":  "string",
                "reset_url":  "string",
                "expires_in": "string",
            }),
        },
        {
            Name:     "post_published_notification",
            Subject:  "Your post has been published",
            Body:     "Your post '{{.PostTitle}}' has been published and is now live!",
            Type:     "database",
            Category: "post_published",
            Variables: s.jsonify(map[string]interface{}{
                "post_title": "string",
                "post_url":   "string",
            }),
        },
    }
    
    for _, template := range templates {
        var existing notification.NotificationTemplate
        if err := db.Where("name = ?", template.Name).First(&existing).Error; err == nil {
            continue
        }
        
        if err := db.Create(&template).Error; err != nil {
            return err
        }
    }
    
    return nil
}

func (s *NotificationTemplateSeeder) getWelcomeEmailTemplate() string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to {{.AppName}}</title>
</head>
<body>
    <h1>Welcome to {{.AppName}}, {{.UserName}}!</h1>
    
    <p>Thank you for joining our community. We're excited to have you on board!</p>
    
    <p>Here are some things you can do to get started:</p>
    <ul>
        <li>Complete your profile</li>
        <li>Browse our latest posts</li>
        <li>Join discussions in the comments</li>
        <li>Follow your favorite authors</li>
    </ul>
    
    <p>If you have any questions, feel free to reach out to our support team.</p>
    
    <p>Best regards,<br>The {{.AppName}} Team</p>
    
    <p><a href="{{.AppUrl}}">Visit {{.AppName}}</a></p>
</body>
</html>
`
}

func (s *NotificationTemplateSeeder) getPasswordResetTemplate() string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reset Your Password</title>
</head>
<body>
    <h1>Password Reset Request</h1>
    
    <p>Hi {{.UserName}},</p>
    
    <p>We received a request to reset your password. Click the link below to create a new password:</p>
    
    <p><a href="{{.ResetUrl}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
    
    <p>This link will expire in {{.ExpiresIn}}.</p>
    
    <p>If you didn't request this password reset, please ignore this email.</p>
    
    <p>Best regards,<br>The Support Team</p>
</body>
</html>
`
}

func (s *NotificationTemplateSeeder) jsonify(data interface{}) notification.JSON {
    bytes, _ := json.Marshal(data)
    return notification.JSON(bytes)
}

func (s *NotificationTemplateSeeder) Rollback(db *gorm.DB) error {
    names := []string{
        "welcome_email",
        "new_comment_notification",
        "password_reset_email",
        "post_published_notification",
    }
    
    return db.Where("name IN ?", names).Delete(&notification.NotificationTemplate{}).Error
}
```

## Seeder Registration

### Central Seeder Registry

```go
// pkg/database/seeds/registry.go
package seeds

func RegisterAll(manager *Manager) error {
    seeders := []Seeder{
        NewUserSeeder(),
        NewCategorySeeder(),
        NewTagSeeder(),
        NewPostSeeder(),
        NewCommentSeeder(),
        NewNotificationTemplateSeeder(),
    }
    
    for _, seeder := range seeders {
        if err := manager.Register(seeder); err != nil {
            return err
        }
    }
    
    return nil
}
```

## Environment-Specific Seeding

### Development Environment

```go
// pkg/database/seeds/development.go
package seeds

import (
    "gorm.io/gorm"
)

func SeedDevelopment(db *gorm.DB) error {
    manager := NewManager(db)
    
    if err := RegisterAll(manager); err != nil {
        return err
    }
    
    // Seed all data for development
    return manager.SeedAll()
}
```

### Testing Environment

```go
// pkg/database/seeds/testing.go
package seeds

import (
    "gorm.io/gorm"
)

func SeedTesting(db *gorm.DB) error {
    manager := NewManager(db)
    
    // Register only essential seeders for testing
    testSeeders := []Seeder{
        NewUserSeeder(),
        NewCategorySeeder(),
        NewTagSeeder(),
    }
    
    for _, seeder := range testSeeders {
        if err := manager.Register(seeder); err != nil {
            return err
        }
    }
    
    return manager.SeedAll()
}
```

### Production Environment

```go
// pkg/database/seeds/production.go
package seeds

import (
    "gorm.io/gorm"
)

func SeedProduction(db *gorm.DB) error {
    manager := NewManager(db)
    
    // Register only essential production data
    prodSeeders := []Seeder{
        NewNotificationTemplateSeeder(),
        // Add admin user seeder, default categories, etc.
    }
    
    for _, seeder := range prodSeeders {
        if err := manager.Register(seeder); err != nil {
            return err
        }
    }
    
    return manager.SeedAll()
}
```

## CLI Integration

### Seeding Commands

```go
// cmd/server/seed_commands.go
package main

import (
    "fmt"
    "os"
    "blog-api-v3/pkg/database/seeds"
)

func handleSeedCommand(db *gorm.DB, args []string) {
    if len(args) < 2 {
        fmt.Println("Usage: seed [all|specific_seeder|env] [options]")
        os.Exit(1)
    }
    
    manager := seeds.NewManager(db)
    
    switch args[1] {
    case "all":
        if err := seeds.RegisterAll(manager); err != nil {
            fmt.Printf("Registration failed: %v\n", err)
            os.Exit(1)
        }
        
        if err := manager.SeedAll(); err != nil {
            fmt.Printf("Seeding failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Println("All seeders completed successfully")
        
    case "dev", "development":
        if err := seeds.SeedDevelopment(db); err != nil {
            fmt.Printf("Development seeding failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Println("Development seeding completed")
        
    case "test", "testing":
        if err := seeds.SeedTesting(db); err != nil {
            fmt.Printf("Testing seeding failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Println("Testing seeding completed")
        
    case "prod", "production":
        if err := seeds.SeedProduction(db); err != nil {
            fmt.Printf("Production seeding failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Println("Production seeding completed")
        
    default:
        // Try to run specific seeder
        if err := seeds.RegisterAll(manager); err != nil {
            fmt.Printf("Registration failed: %v\n", err)
            os.Exit(1)
        }
        
        if err := manager.Seed(args[1]); err != nil {
            fmt.Printf("Seeding %s failed: %v\n", args[1], err)
            os.Exit(1)
        }
        fmt.Printf("Seeder %s completed successfully\n", args[1])
    }
}
```

## Factory Pattern for Testing

### User Factory

```go
// pkg/database/factories/user_factory.go
package factories

import (
    "fmt"
    "gorm.io/gorm"
    "golang.org/x/crypto/bcrypt"
    "blog-api-v3/internal/modules/auth"
)

type UserFactory struct {
    db *gorm.DB
}

func NewUserFactory(db *gorm.DB) *UserFactory {
    return &UserFactory{db: db}
}

func (f *UserFactory) Create(overrides ...map[string]interface{}) (*auth.User, error) {
    user := &auth.User{
        Name:     "Test User",
        Email:    fmt.Sprintf("<EMAIL>", rand.Intn(10000)),
        Role:     "user",
        Status:   "active",
    }
    
    // Apply overrides
    for _, override := range overrides {
        if name, ok := override["name"].(string); ok {
            user.Name = name
        }
        if email, ok := override["email"].(string); ok {
            user.Email = email
        }
        if role, ok := override["role"].(string); ok {
            user.Role = role
        }
        if status, ok := override["status"].(string); ok {
            user.Status = status
        }
    }
    
    // Set default password
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
    if err != nil {
        return nil, err
    }
    user.Password = string(hashedPassword)
    
    if err := f.db.Create(user).Error; err != nil {
        return nil, err
    }
    
    return user, nil
}

func (f *UserFactory) CreateMany(count int, overrides ...map[string]interface{}) ([]*auth.User, error) {
    users := make([]*auth.User, 0, count)
    
    for i := 0; i < count; i++ {
        user, err := f.Create(overrides...)
        if err != nil {
            return nil, err
        }
        users = append(users, user)
    }
    
    return users, nil
}
```

## Best Practices

### 1. Idempotent Seeders
- Check for existing data before creating
- Use upsert operations when appropriate
- Handle conflicts gracefully

### 2. Dependency Management
- Clearly define seeder dependencies
- Use topological sorting for execution order
- Fail fast on circular dependencies

### 3. Environment Awareness
- Different data sets for different environments
- Minimal data for testing
- Production-safe seeding

### 4. Data Quality
- Use realistic, diverse data
- Include edge cases for testing
- Maintain data consistency

### 5. Performance Considerations
- Batch operations when possible
- Use transactions for related data
- Consider foreign key constraints

## Makefile Integration

```makefile
seed: ## Run all seeders
	@go run cmd/server/main.go seed all

seed-dev: ## Run development seeders
	@go run cmd/server/main.go seed development

seed-test: ## Run testing seeders
	@go run cmd/server/main.go seed testing

seed-prod: ## Run production seeders
	@go run cmd/server/main.go seed production

seed-users: ## Run only user seeder
	@go run cmd/server/main.go seed users

seed-fresh: ## Reset database and reseed
	@make migrate-fresh && make seed-dev
```

## Related Documentation

- [Database Migrations](./migrations.md)
- [Database Models](./models.md)
- [Testing Strategy](../development/testing.md)