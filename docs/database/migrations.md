# Database Migrations

## Overview

The Blog API v3 uses a custom migration system built on top of GORM's AutoMigrate functionality. This system provides version control for database schema changes, ensuring consistent database state across different environments.

## Migration System Architecture

### Migration Structure

```go
type Migration struct {
    ID   string
    Name string
    Up   func(*gorm.DB) error
    Down func(*gorm.DB) error
}
```

### Migration Manager

```go
// pkg/database/migrations/manager.go
package migrations

import (
    "fmt"
    "gorm.io/gorm"
    "time"
)

type Manager struct {
    db         *gorm.DB
    migrations []Migration
}

func NewManager(db *gorm.DB) *Manager {
    return &Manager{
        db:         db,
        migrations: []Migration{},
    }
}

func (m *Manager) Register(migration Migration) {
    m.migrations = append(m.migrations, migration)
}

func (m *Manager) Up() error {
    // Ensure migration table exists
    if err := m.createMigrationTable(); err != nil {
        return err
    }

    for _, migration := range m.migrations {
        if applied, err := m.isMigrationApplied(migration.ID); err != nil {
            return err
        } else if applied {
            continue
        }

        fmt.Printf("Running migration: %s\n", migration.Name)
        
        if err := migration.Up(m.db); err != nil {
            return fmt.Errorf("migration %s failed: %w", migration.ID, err)
        }

        if err := m.recordMigration(migration.ID, migration.Name); err != nil {
            return err
        }

        fmt.Printf("Migration completed: %s\n", migration.Name)
    }

    return nil
}

func (m *Manager) Down(steps int) error {
    appliedMigrations, err := m.getAppliedMigrations()
    if err != nil {
        return err
    }

    if steps > len(appliedMigrations) {
        steps = len(appliedMigrations)
    }

    // Get migrations to rollback (reverse order)
    toRollback := appliedMigrations[len(appliedMigrations)-steps:]

    for i := len(toRollback) - 1; i >= 0; i-- {
        migration := m.findMigration(toRollback[i].ID)
        if migration == nil {
            return fmt.Errorf("migration %s not found", toRollback[i].ID)
        }

        fmt.Printf("Rolling back migration: %s\n", migration.Name)

        if err := migration.Down(m.db); err != nil {
            return fmt.Errorf("rollback %s failed: %w", migration.ID, err)
        }

        if err := m.removeMigrationRecord(migration.ID); err != nil {
            return err
        }

        fmt.Printf("Rollback completed: %s\n", migration.Name)
    }

    return nil
}

func (m *Manager) Status() error {
    appliedMigrations, err := m.getAppliedMigrations()
    if err != nil {
        return err
    }

    fmt.Println("Migration Status:")
    fmt.Println("================")

    appliedMap := make(map[string]bool)
    for _, applied := range appliedMigrations {
        appliedMap[applied.ID] = true
    }

    for _, migration := range m.migrations {
        status := "Pending"
        if appliedMap[migration.ID] {
            status = "Applied"
        }
        fmt.Printf("[%s] %s - %s\n", status, migration.ID, migration.Name)
    }

    return nil
}

type MigrationRecord struct {
    ID        uint      `gorm:"primarykey"`
    MigrationID string  `gorm:"uniqueIndex;not null"`
    Name      string    `gorm:"not null"`
    AppliedAt time.Time `gorm:"not null"`
}

func (m *Manager) createMigrationTable() error {
    return m.db.AutoMigrate(&MigrationRecord{})
}

func (m *Manager) isMigrationApplied(migrationID string) (bool, error) {
    var count int64
    err := m.db.Model(&MigrationRecord{}).Where("migration_id = ?", migrationID).Count(&count).Error
    return count > 0, err
}

func (m *Manager) recordMigration(migrationID, name string) error {
    record := &MigrationRecord{
        MigrationID: migrationID,
        Name:        name,
        AppliedAt:   time.Now(),
    }
    return m.db.Create(record).Error
}

func (m *Manager) removeMigrationRecord(migrationID string) error {
    return m.db.Where("migration_id = ?", migrationID).Delete(&MigrationRecord{}).Error
}

func (m *Manager) getAppliedMigrations() ([]*MigrationRecord, error) {
    var records []*MigrationRecord
    err := m.db.Order("applied_at").Find(&records).Error
    return records, err
}

func (m *Manager) findMigration(id string) *Migration {
    for _, migration := range m.migrations {
        if migration.ID == id {
            return &migration
        }
    }
    return nil
}
```

## Migration Examples

### 1. Basic Table Creation

```go
// pkg/database/migrations/001_create_user_accounts_table.go
package migrations

import (
    "gorm.io/gorm"
    "blog-api-v3/internal/modules/user"
)

var CreateUserAccountsTable = Migration{
    ID:   "001_create_user_accounts_table",
    Name: "Create user_accounts table",
    Up: func(db *gorm.DB) error {
        return db.AutoMigrate(&user.Account{})
    },
    Down: func(db *gorm.DB) error {
        return db.Migrator().DropTable(&user.Account{})
    },
}
```

### 2. Adding Columns

```go
// pkg/database/migrations/002_add_avatar_to_user_accounts.go
package migrations

import (
    "gorm.io/gorm"
)

var AddAvatarToUserAccounts = Migration{
    ID:   "002_add_avatar_to_user_accounts",
    Name: "Add avatar column to user_accounts table",
    Up: func(db *gorm.DB) error {
        return db.Exec("ALTER TABLE user_accounts ADD COLUMN avatar VARCHAR(255)").Error
    },
    Down: func(db *gorm.DB) error {
        return db.Exec("ALTER TABLE user_accounts DROP COLUMN avatar").Error
    },
}
```

### 3. Creating Indexes

```go
// pkg/database/migrations/003_add_indexes.go
package migrations

import (
    "gorm.io/gorm"
)

var AddIndexes = Migration{
    ID:   "003_add_indexes",
    Name: "Add database indexes for performance",
    Up: func(db *gorm.DB) error {
        // Add index on posts.published_at for faster queries
        if err := db.Exec("CREATE INDEX idx_posts_published_at ON posts(published_at)").Error; err != nil {
            return err
        }
        
        // Add composite index on posts table
        if err := db.Exec("CREATE INDEX idx_posts_status_author ON posts(status, author_id)").Error; err != nil {
            return err
        }
        
        // Add index on comments.post_id
        return db.Exec("CREATE INDEX idx_comments_post_id ON comments(post_id)").Error
    },
    Down: func(db *gorm.DB) error {
        if err := db.Exec("DROP INDEX idx_posts_published_at ON posts").Error; err != nil {
            return err
        }
        
        if err := db.Exec("DROP INDEX idx_posts_status_author ON posts").Error; err != nil {
            return err
        }
        
        return db.Exec("DROP INDEX idx_comments_post_id ON comments").Error
    },
}
```

### 4. Data Migration

```go
// pkg/database/migrations/004_migrate_user_roles.go
package migrations

import (
    "gorm.io/gorm"
)

var MigrateUserRoles = Migration{
    ID:   "004_migrate_user_roles",
    Name: "Migrate user roles from string to enum",
    Up: func(db *gorm.DB) error {
        // Add new role_id column
        if err := db.Exec("ALTER TABLE user_accounts ADD COLUMN role_id INT").Error; err != nil {
            return err
        }

        // Create rbac_roles table
        if err := db.Exec(`
            CREATE TABLE rbac_roles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(50) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `).Error; err != nil {
            return err
        }

        // Insert default roles
        if err := db.Exec(`
            INSERT INTO rbac_roles (name) VALUES
            ('admin'), ('moderator'), ('user')
        `).Error; err != nil {
            return err
        }
        
        // Migrate existing role data
        if err := db.Exec(`
            UPDATE user_accounts u
            JOIN rbac_roles r ON u.role = r.name
            SET u.role_id = r.id
        `).Error; err != nil {
            return err
        }

        // Make role_id NOT NULL
        if err := db.Exec("ALTER TABLE user_accounts MODIFY role_id INT NOT NULL").Error; err != nil {
            return err
        }

        // Drop old role column
        return db.Exec("ALTER TABLE user_accounts DROP COLUMN role").Error
    },
    Down: func(db *gorm.DB) error {
        // Add back role column
        if err := db.Exec("ALTER TABLE user_accounts ADD COLUMN role VARCHAR(50)").Error; err != nil {
            return err
        }

        // Migrate data back
        if err := db.Exec(`
            UPDATE user_accounts u
            JOIN rbac_roles r ON u.role_id = r.id
            SET u.role = r.name
        `).Error; err != nil {
            return err
        }

        // Drop new structures
        if err := db.Exec("ALTER TABLE user_accounts DROP COLUMN role_id").Error; err != nil {
            return err
        }
        
        return db.Exec("DROP TABLE roles").Error
    },
}
```

### 5. Complex Schema Changes

```go
// pkg/database/migrations/005_restructure_posts.go
package migrations

import (
    "gorm.io/gorm"
)

var RestructurePosts = Migration{
    ID:   "005_restructure_posts",
    Name: "Restructure posts table for better performance",
    Up: func(db *gorm.DB) error {
        // Start transaction
        tx := db.Begin()
        
        // Create new blog_posts table with better structure
        if err := tx.Exec(`
            CREATE TABLE blog_posts_new (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                content LONGTEXT,
                excerpt TEXT,
                featured_image VARCHAR(255),
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                view_count INT UNSIGNED DEFAULT 0,
                like_count INT UNSIGNED DEFAULT 0,
                author_id BIGINT NOT NULL,
                published_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP NULL,
                
                INDEX idx_posts_slug (slug),
                INDEX idx_posts_status (status),
                INDEX idx_posts_author (author_id),
                INDEX idx_posts_published (published_at),
                INDEX idx_posts_deleted (deleted_at)
            )
        `).Error; err != nil {
            tx.Rollback()
            return err
        }
        
        // Copy data from old table
        if err := tx.Exec(`
            INSERT INTO posts_new 
            (id, title, slug, content, excerpt, featured_image, status, 
             view_count, like_count, author_id, published_at, created_at, updated_at, deleted_at)
            SELECT 
                id, title, slug, content, excerpt, featured_image, status,
                COALESCE(view_count, 0), COALESCE(like_count, 0), author_id, 
                published_at, created_at, updated_at, deleted_at
            FROM posts
        `).Error; err != nil {
            tx.Rollback()
            return err
        }
        
        // Drop old table and rename new one
        if err := tx.Exec("DROP TABLE posts").Error; err != nil {
            tx.Rollback()
            return err
        }
        
        if err := tx.Exec("RENAME TABLE posts_new TO posts").Error; err != nil {
            tx.Rollback()
            return err
        }
        
        return tx.Commit().Error
    },
    Down: func(db *gorm.DB) error {
        // Revert to original structure
        tx := db.Begin()
        
        if err := tx.Exec(`
            CREATE TABLE blog_posts_old (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL UNIQUE,
                content TEXT,
                status VARCHAR(20) DEFAULT 'draft',
                author_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `).Error; err != nil {
            tx.Rollback()
            return err
        }

        if err := tx.Exec(`
            INSERT INTO blog_posts_old (id, title, slug, content, status, author_id, created_at, updated_at)
            SELECT id, title, slug, content, status, author_id, created_at, updated_at
            FROM blog_posts
        `).Error; err != nil {
            tx.Rollback()
            return err
        }
        
        if err := tx.Exec("DROP TABLE posts").Error; err != nil {
            tx.Rollback()
            return err
        }
        
        if err := tx.Exec("RENAME TABLE posts_old TO posts").Error; err != nil {
            tx.Rollback()
            return err
        }
        
        return tx.Commit().Error
    },
}
```

## Migration Registration

### Central Migration Registry

```go
// pkg/database/migrations/registry.go
package migrations

func RegisterAll(manager *Manager) {
    // Core tables
    manager.Register(CreateUserAccountsTable)
    manager.Register(CreateBlogPostsTable)
    manager.Register(CreateBlogCategoriesTable)
    manager.Register(CreateBlogTagsTable)
    manager.Register(CreateBlogCommentsTable)

    // Junction tables
    manager.Register(CreateBlogPostCategoriesTable)
    manager.Register(CreateBlogPostTagsTable)

    // Auth tables
    manager.Register(CreateAuthRefreshTokensTable)
    manager.Register(CreateAuthPasswordResetsTable)

    // Notification tables
    manager.Register(CreateNotificationMessagesTable)
    manager.Register(CreateNotificationTemplatesTable)
    manager.Register(CreateNotificationPreferencesTable)
    manager.Register(CreateNotificationLogsTable)
    
    // Schema improvements
    manager.Register(AddAvatarToUserAccounts)
    manager.Register(AddIndexes)
    manager.Register(MigrateUserRoles)
    manager.Register(RestructurePosts)
}
```

## CLI Integration

### Migration Commands

```go
// cmd/server/migration_commands.go
package main

import (
    "fmt"
    "os"
    "strconv"
    "blog-api-v3/pkg/database/migrations"
)

func handleMigrationCommand(db *gorm.DB, args []string) {
    if len(args) < 2 {
        fmt.Println("Usage: migrate [up|down|status|create] [options]")
        os.Exit(1)
    }
    
    manager := migrations.NewManager(db)
    migrations.RegisterAll(manager)
    
    switch args[1] {
    case "up":
        if err := manager.Up(); err != nil {
            fmt.Printf("Migration failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Println("All migrations completed successfully")
        
    case "down":
        steps := 1
        if len(args) > 2 {
            if s, err := strconv.Atoi(args[2]); err == nil {
                steps = s
            }
        }
        
        if err := manager.Down(steps); err != nil {
            fmt.Printf("Rollback failed: %v\n", err)
            os.Exit(1)
        }
        fmt.Printf("Rolled back %d migrations\n", steps)
        
    case "status":
        if err := manager.Status(); err != nil {
            fmt.Printf("Status check failed: %v\n", err)
            os.Exit(1)
        }
        
    case "create":
        if len(args) < 3 {
            fmt.Println("Usage: migrate create <migration_name>")
            os.Exit(1)
        }
        createMigrationFile(args[2])
        
    default:
        fmt.Printf("Unknown command: %s\n", args[1])
        os.Exit(1)
    }
}

func createMigrationFile(name string) {
    timestamp := time.Now().Format("20060102150405")
    filename := fmt.Sprintf("%s_%s.go", timestamp, name)
    
    template := `package migrations

import (
    "gorm.io/gorm"
)

var %s = Migration{
    ID:   "%s_%s",
    Name: "%s",
    Up: func(db *gorm.DB) error {
        // Add your migration logic here
        return nil
    },
    Down: func(db *gorm.DB) error {
        // Add your rollback logic here
        return nil
    },
}
`
    
    content := fmt.Sprintf(template, 
        toCamelCase(name), 
        timestamp, 
        name,
        humanize(name),
    )
    
    filepath := fmt.Sprintf("pkg/database/migrations/%s", filename)
    if err := os.WriteFile(filepath, []byte(content), 0644); err != nil {
        fmt.Printf("Failed to create migration file: %v\n", err)
        os.Exit(1)
    }
    
    fmt.Printf("Migration file created: %s\n", filepath)
}
```

## Best Practices

### 1. Migration Naming
- Use descriptive names: `create_user_accounts_table`, `add_index_to_blog_posts`
- Include timestamp prefix for ordering
- Use snake_case for consistency
- Follow module-based naming: `{module_prefix}_{entity}`

### 2. Reversible Migrations
- Always implement both `Up` and `Down` functions
- Test rollback functionality
- Consider data loss implications

### 3. Data Safety
- Use transactions for complex migrations
- Backup data before destructive operations
- Test migrations on production-like data

### 4. Performance Considerations
- Create indexes in separate migrations
- Consider table size for schema changes
- Use online DDL when available

### 5. Environment Considerations
- Test migrations in staging first
- Consider downtime requirements
- Plan for rollback scenarios

## Makefile Integration

```makefile
migrate-up: ## Run all pending migrations
	@go run cmd/server/main.go migrate up

migrate-down: ## Rollback last migration
	@go run cmd/server/main.go migrate down 1

migrate-status: ## Show migration status
	@go run cmd/server/main.go migrate status

migrate-create: ## Create new migration file
	@read -p "Migration name: " name; \
	go run cmd/server/main.go migrate create $$name

migrate-reset: ## Reset database (rollback all migrations)
	@go run cmd/server/main.go migrate down 999

migrate-fresh: ## Reset and re-run all migrations
	@make migrate-reset && make migrate-up
```

## Related Documentation

- [Database Seeding](./seeding.md)
- [Database Models](./models.md)
- [Development Workflow](../development/workflow.md)