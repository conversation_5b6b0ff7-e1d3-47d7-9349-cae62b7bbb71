# Documentation Navigation Guide

## 📚 Tổng quan

Tài liệu này cung cấp hướng dẫn navigation để tìm thông tin nhanh chóng trong hệ thống tài liệu Blog API v3.

## 🏗️ Architecture & Design

### Core Architecture
- **[Architecture Overview](./ARCHITECTURE.md)** - Tổng quan kiến trúc hệ thống
- **[Project Structure](./architecture/project-structure.md)** - C<PERSON><PERSON> trúc thư mục chi tiết
- **[Inter-module Communication](./architecture/inter-module-communication.md)** - Giao tiếp giữa các module

### Multi-tenancy & Security
- **[Multi-tenant User Management](./architecture/multi-tenant-user-management.md)** - Quản lý user đa tenant
- **[Tenant Priority System](./architecture/tenant-priority-system.md)** - Hệ thống ưu tiên tenant
- **[Logging System](./architecture/logging-system.md)** - <PERSON><PERSON> thống logging tập trung

### Infrastructure
- **[Queue System](./architecture/queue-system.md)** - Message queue và background jobs
- **[Cron Scheduler](./architecture/cron-scheduler.md)** - Task scheduling system

## 🗄️ Database

### Design & Schema
- **[Database Design](./database/database-design.md)** - Quy tắc thiết kế database
- **[ERD Schema](./database/erd-schema.md)** - Entity Relationship Diagram
- **[Migrations](./database/migrations.md)** - Database migration system
- **[Seeding](./database/seeding.md)** - Data seeding strategies

### Tenant Management
- **[Tenant Initialization](./database/tenant-initialization.md)** - Setup tenant database

## 🔧 Modules

### Core System Modules
- **[Auth Module](./modules/auth.md)** - Authentication & authorization
- **[User Module](./modules/user.md)** - User profile & preferences
- **[RBAC Module](./modules/rbac.md)** - Role-based access control
- **[Tenant Module](./modules/tenant.md)** - Multi-tenancy management

### Content Management
- **[Blog Module](./modules/blog.md)** - Blog posts & content management
- **[Blog Submission Flow](./modules/blog-submission-flow.md)** - Content workflow
- **[Media Module](./modules/media.md)** - File & media management
- **[SEO Module](./modules/seo.md)** - SEO optimization

### Communication & Features
- **[Notification Module](./modules/notification.md)** - Email & push notifications
- **[Socket Module](./modules/socket.md)** - Real-time communication
- **[Website Module](./modules/website.md)** - Website management
- **[Onboarding Module](./modules/onboarding.md)** - User onboarding flows
- **[Payment Module](./modules/payment.md)** - Payment processing

## 🔌 API Documentation

### API Standards
- **[Response Standard](./api/response-standard.md)** - API response format standards
- **[CMS API](./api/cms-api.md)** - Admin/CMS API endpoints
- **[Frontend API](./api/frontend-api.md)** - Public API endpoints

## 🛠️ Development

### Development Workflow
- **[Local Testing](./development/local-testing.md)** - Local development setup
- **[Testing Guidelines](./development/testing.md)** - Testing strategies
- **[Golang Libraries](./development/golang-libraries.md)** - Third-party libraries

## 🌟 Features

### Special Features
- **[Internationalization (i18n)](./features/i18n.md)** - Multi-language support

## 🔍 Quick Reference

### Common Tasks
| Task | Primary Document | Related Documents |
|------|------------------|-------------------|
| **User Registration** | [Auth Module](./modules/auth.md#user-registration-flow) | [Multi-tenant User Management](./architecture/multi-tenant-user-management.md) |
| **Blog Post Creation** | [Blog Module](./modules/blog.md#post-creation-flow) | [Blog Submission Flow](./modules/blog-submission-flow.md) |
| **Database Schema** | [Database Design](./database/database-design.md) | [ERD Schema](./database/erd-schema.md) |
| **API Integration** | [Response Standard](./api/response-standard.md) | [CMS API](./api/cms-api.md), [Frontend API](./api/frontend-api.md) |
| **Multi-tenancy** | [Tenant Module](./modules/tenant.md) | [Multi-tenant User Management](./architecture/multi-tenant-user-management.md) |
| **Testing Setup** | [Testing Guidelines](./development/testing.md) | [Local Testing](./development/local-testing.md) |

### Module Dependencies
```mermaid
graph TD
    A[Auth Module] --> B[User Module]
    A --> C[RBAC Module]
    B --> D[Blog Module]
    C --> D
    D --> E[Media Module]
    D --> F[SEO Module]
    A --> G[Notification Module]
    H[Tenant Module] --> A
    H --> B
    H --> D
```

## 📖 Reading Paths

### For New Developers
1. **[Architecture Overview](./ARCHITECTURE.md)** - Hiểu tổng quan hệ thống
2. **[Project Structure](./architecture/project-structure.md)** - Nắm cấu trúc code
3. **[Database Design](./database/database-design.md)** - Hiểu database schema
4. **[Auth Module](./modules/auth.md)** - Bắt đầu với authentication
5. **[Testing Guidelines](./development/testing.md)** - Setup testing environment

### For API Integration
1. **[Response Standard](./api/response-standard.md)** - API format standards
2. **[Frontend API](./api/frontend-api.md)** hoặc **[CMS API](./api/cms-api.md)** - Tùy use case
3. **[Auth Module](./modules/auth.md)** - Authentication flow
4. Module-specific docs tùy theo feature cần integrate

### For System Administration
1. **[Tenant Module](./modules/tenant.md)** - Multi-tenant management
2. **[Database Migrations](./database/migrations.md)** - Database updates
3. **[Queue System](./architecture/queue-system.md)** - Background job management
4. **[Logging System](./architecture/logging-system.md)** - Monitoring và debugging

## 🔗 Cross-References

### Module Interconnections

| Module | Dependencies | Dependents | Key Integrations |
|--------|-------------|------------|------------------|
| **Auth** | User, RBAC, Tenant | All modules | JWT tokens, session management |
| **User** | Auth, RBAC | Blog, Media, Notification | User profiles, preferences |
| **RBAC** | User, Tenant | Auth, Blog, Media | Permissions, role assignments |
| **Blog** | User, Media, SEO | Notification, Socket | Content creation, publishing |
| **Media** | User, Storage | Blog, User | File uploads, image processing |
| **Notification** | User, Email | All modules | Event-driven notifications |
| **Tenant** | Database | All modules | Multi-tenancy isolation |

### API Integration Patterns

| Use Case | Primary API | Supporting APIs | Authentication |
|----------|-------------|-----------------|----------------|
| **Public Blog** | [Frontend API](./api/frontend-api.md) | [Media API](./modules/media.md#api-endpoints) | Optional |
| **Content Management** | [CMS API](./api/cms-api.md) | [Blog API](./modules/blog.md#api-endpoints) | Required |
| **User Dashboard** | [Frontend API](./api/frontend-api.md) | [User API](./modules/user.md#api-endpoints) | Required |
| **Admin Panel** | [CMS API](./api/cms-api.md) | [RBAC API](./modules/rbac.md#api-endpoints) | Admin Required |

### Database Schema References

| Entity Type | Primary Schema | Related Schemas | Migration Files |
|-------------|---------------|-----------------|-----------------|
| **Users** | [User Accounts](./database/database-design.md#user-authentication) | [RBAC](./database/erd-schema.md#rbac-system), [Tenant](./database/erd-schema.md#core-tenant--user-management) | `001_create_user_accounts_table` |
| **Content** | [Blog Posts](./database/database-design.md#blog-content) | [Media](./database/erd-schema.md#media-management), [SEO](./database/erd-schema.md#seo-management) | `003_create_blog_posts_table` |
| **Permissions** | [RBAC System](./database/erd-schema.md#rbac-system) | [Users](./database/database-design.md#user-authentication) | `002_create_rbac_tables` |

### Configuration Dependencies

| Component | Config File | Environment Variables | Related Modules |
|-----------|-------------|----------------------|-----------------|
| **Database** | `config/database.yaml` | `DB_HOST`, `DB_NAME` | All modules |
| **Cache** | `config/redis.yaml` | `REDIS_URL` | Auth, User, Blog |
| **Storage** | `config/storage.yaml` | `S3_BUCKET`, `MINIO_URL` | Media |
| **Email** | `config/email.yaml` | `SMTP_HOST`, `SENDGRID_KEY` | Notification |
| **Queue** | `config/queue.yaml` | `QUEUE_DRIVER` | Background jobs |

### Troubleshooting Guide

| Issue Type | Primary Document | Diagnostic Steps | Related Logs |
|------------|------------------|------------------|--------------|
| **Authentication Errors** | [Auth Module](./modules/auth.md#troubleshooting) | Check JWT tokens, session validity | `auth.log`, `security.log` |
| **Database Issues** | [Database Design](./database/database-design.md#troubleshooting) | Check connections, migrations | `database.log`, `migration.log` |
| **API Errors** | [Response Standard](./api/response-standard.md#error-handling) | Check error codes, request format | `api.log`, `error.log` |
| **Multi-tenant Issues** | [Tenant Module](./modules/tenant.md#troubleshooting) | Check tenant isolation, context | `tenant.log`, `isolation.log` |

### Performance Optimization

| Area | Primary Guide | Metrics to Monitor | Related Tools |
|------|---------------|-------------------|---------------|
| **API Performance** | [Frontend API](./api/frontend-api.md#performance-optimization) | Response time, throughput | Prometheus, Grafana |
| **Database Performance** | [Database Design](./database/database-design.md#indexing-strategy) | Query time, connection pool | MySQL slow log, EXPLAIN |
| **Cache Performance** | [Platform Services](./architecture/project-structure.md#platform-services) | Hit ratio, memory usage | Redis monitoring |
| **Storage Performance** | [Media Module](./modules/media.md#performance) | Upload speed, CDN hits | S3 metrics, CDN analytics |

Mỗi tài liệu module đều có section "Tài liệu liên quan" ở cuối để dễ dàng navigate đến các tài liệu related. Sử dụng các bảng tham chiếu trên để tìm hiểu sâu hơn về các topic liên quan.
