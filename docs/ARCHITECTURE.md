# Blog API v3 Architecture Overview

## Tổng quan Hệ thống

Blog API v3 là một hệ thống blog API hiện đại được xây dựng bằng Go, thiết kế theo kiến trúc modular với khả năng mở rộng cao thông qua plugin system. Hệ thống hỗ trợ multi-tenancy, real-time communication, và cung cấp 2 bộ API riêng biệt cho CMS và Frontend.

## Quick Links

📚 **[Complete Documentation](/docs/README.md)** - Full documentation index

### Kiến trúc Core
- 🏗️ [Architecture Overview](/docs/architecture/overview.md) - Thiế<PERSON> kế hệ thống và nguyên tắc
- 📁 [Project Structure](/docs/architecture/project-structure.md) - Tổ chức thư mục
- ⚙️ [Configuration](/docs/architecture/configuration.md) - Cấu hình môi trường
- 🗄️ [Database Design](/docs/architecture/database.md) - Thiết kế CSDL
- 🔄 [Inter-module Communication](/docs/architecture/inter-module-communication.md) - <PERSON><PERSON><PERSON> tiếp giữa modules
- ⚡ [Queue System](/docs/architecture/queue-system.md) - Hệ thống xử lý bất đồng bộ
- ⏰ [Cron & Scheduler](/docs/architecture/cron-scheduler.md) - Scheduled jobs và automation

### API Documentation
- 📋 [Response Standard](/docs/api/response-standard.md) - Chuẩn response format cho tất cả APIs
- 🔧 [CMS API](/docs/api/cms-api.md) - API cho quản trị nội dung
- 🌐 [Frontend API](/docs/api/frontend-api.md) - API cho website công khai
- 🌍 [Internationalization](/docs/features/i18n.md) - Hỗ trợ đa ngôn ngữ

### Database
- 🔄 [Migrations](/docs/database/migrations.md) - Hệ thống migration
- 🌱 [Seeding](/docs/database/seeding.md) - Data seeding và fixtures
- 📊 [Models](/docs/database/models.md) - GORM models và relationships

### Modules
- 🔐 [Auth Module](/docs/modules/auth.md) - Xác thực và phân quyền
- 📝 [Blog Module](/docs/modules/blog.md) - Quản lý blog
- 🔔 [Socket Module](/docs/modules/socket.md) - Real-time communication 
- 📁 [Media Module](/docs/modules/media.md) - Quản lý file media
- 👥 [RBAC Module](/docs/modules/rbac.md) - Role-based access control
- 🏢 [Tenant Module](/docs/modules/tenant.md) - Multi-tenancy support
- 🌐 [Website Module](/docs/modules/website.md) - Quản lý frontend website
- 🚀 [Onboarding Module](/docs/modules/onboarding.md) - Hệ thống onboarding
- 🔍 [SEO Module](/docs/modules/seo.md) - Search Engine Optimization
- 💳 [Payment Module](/docs/modules/payment.md) - Payment processing integration

## Technology Stack

### Core Technologies
- **Language**: Go 1.21+ với generics support
- **Framework**: Gin (High-performance HTTP web framework)  
- **Database**: MySQL 8.0+ với GORM ORM
- **Authentication**: JWT tokens với refresh token support
- **Caching**: Redis (optional) cho session và cache
- **Real-time**: WebSocket cho live notifications
- **Queue**: Redis Streams/RabbitMQ cho background jobs
- **Storage**: Local storage/MinIO/S3 cho media files
- **Environment**: Docker support với multi-stage builds
- **I18n**: Go-i18n library với JSON translation files

### API Architecture
- **Response Standard**: Chuẩn response format thống nhất cho tất cả endpoints
- **Cursor Pagination**: Sử dụng cursor-based pagination thay vì offset  
- **Rate Limiting**: Per-IP và per-user rate limiting
- **Error Handling**: Centralized error handling với meaningful error codes
- **Validation**: Request validation với detailed error messages
- **Internationalization**: Multi-language support cho messages và content

## Kiến trúc Hệ thống

### High-level Architecture
Blog API v3 được thiết kế theo kiến trúc modular với các thành phần chính:

#### Core Layer
- **Config Management**: Quản lý cấu hình môi trường với .env support
- **Database Layer**: GORM với MySQL, migration và seeding system
- **Authentication**: JWT-based auth với role-based permissions
- **Response Standard**: Unified response format cho consistency
- **I18n Layer**: Internationalization support cho multi-language

#### Module Layer
- **Modular Design**: Mỗi module là independent với interface riêng
- **Plugin System**: Extensible architecture với hot-pluggable plugins
- **Inter-module Communication**: Event-driven communication giữa modules
- **Service Layer**: Business logic separation từ HTTP handlers

> **📖 Module vs Plugin**: Xem [Module vs Plugin Boundaries](./architecture/module-vs-plugin-boundaries.md) để hiểu rõ architectural boundaries giữa core modules và plugins.

#### API Layer
- **Dual API Design**: 
  - CMS API cho admin/content management
  - Frontend API cho public website access
- **Middleware Stack**: Authentication, rate limiting, CORS, logging, i18n
- **Route Organization**: RESTful routes với versioning support

### Project Structure Overview

> **📖 Chi tiết đầy đủ**: Xem [Project Structure](./architecture/project-structure.md) để biết cấu trúc thư mục chi tiết và mô tả từng component.

**Tóm tắt cấu trúc chính**:
```
blog-api-v3/
├── cmd/                       # Application entry points
│   ├── api/                   # Main API server
│   ├── worker/                # Background worker
│   └── cli/                   # CLI tools
├── internal/                  # Private application code
│   ├── config/                # Configuration management
│   ├── middleware/            # HTTP middleware
│   ├── modules/               # Feature modules (auth, blog, etc.)
│   └── shared/                # Shared utilities
├── pkg/                       # Reusable packages
├── deployments/               # Deployment configurations
├── docs/                      # Documentation
├── docs/                      # Comprehensive documentation
└── tests/                     # Test files
```

## Key Features

### Dual API Architecture
- **CMS API**: Dành cho admin và content managers với full CRUD operations
- **Frontend API**: Public API cho website visitors với read-only access và user interactions
- **Unified Response Format**: Chuẩn response structure cho tất cả APIs với cursor pagination

### Internationalization (i18n)
- **Multi-language Support**: Hỗ trợ đa ngôn ngữ cho UI messages và content
- **Dynamic Language Switching**: Thay đổi ngôn ngữ theo Accept-Language header hoặc query parameter
- **Translation Management**: Centralized translation files với fallback support
- **Content Localization**: Database content có thể được localize theo từng ngôn ngữ

### Module System
- **Independent Modules**: Mỗi module hoạt động độc lập với interface chuẩn
- **Hot-pluggable**: Modules có thể enable/disable mà không ảnh hưởng hệ thống
- **Event-driven Communication**: Modules giao tiếp thông qua events và message queues

### Performance & Scalability  
- **Cursor-based Pagination**: Hiệu suất cao cho large datasets
- **Caching Strategy**: Multi-level caching với Redis
- **Background Jobs**: Asynchronous processing với queue system
- **Rate Limiting**: Intelligent rate limiting per user/IP
- **CDN Integration**: Optimized media delivery

### Real-time Features
- **WebSocket Support**: Live notifications và real-time updates
- **Event Broadcasting**: Real-time events cho comments, likes, views
- **Presence System**: User online/offline status tracking

## Environment Configuration

### Configuration Management
Hệ thống sử dụng environment variables cho configuration, load từ `.env` file với `godotenv`. Hỗ trợ multiple environments (development, staging, production) với configuration validation.

### Configuration Structure
- **Server Config**: Host, port, mode settings cho HTTP server
- **Database Config**: MySQL connection parameters với SSL support  
- **Auth Config**: JWT secrets, token expiration settings
- **App Config**: Application name, environment, log level
- **Plugin Config**: Enable/disable plugins và plugin-specific settings
- **Cache Config**: Redis connection cho caching layer
- **I18n Config**: Default language, supported languages, fallback settings

### Environment Variables Support
Hệ thống hỗ trợ đầy đủ environment variables với default values và validation. Configuration được load theo thứ tự: environment variables → .env file → default values.

## Database Architecture

### Database Layer Overview
- **GORM Integration**: Full-featured ORM với MySQL driver
- **Connection Pooling**: Optimized connection management  
- **Migration System**: Version-controlled schema changes
- **Seeding System**: Reproducible test data generation
- **Model Relationships**: Complex relationships với eager loading support

### Migration System Overview
- **Version Control**: Migrations được đánh số và thực thi theo thứ tự
- **Up/Down Functions**: Mỗi migration có thể rollback được
- **CLI Commands**: Support migrate up/down commands từ CLI
- **Auto Migration**: GORM AutoMigrate cho development
- **Production Safety**: Validation và backup trước khi migrate production

### Seeding System Overview
- **Environment-specific Seeds**: Different seed data cho dev/test/prod
- **Dependency Management**: Seeds có thể depend on nhau
- **Incremental Seeding**: Chỉ seed data chưa tồn tại
- **Custom Seeders**: Module-specific seeders

### Model Architecture
- **Base Model**: Common fields (ID, timestamps, soft delete)
- **Relationships**: Complex many-to-many, one-to-many relationships
- **Hooks**: GORM hooks cho business logic
- **Validation**: Model-level validation rules
- **Localization**: Models support multiple languages for content fields

## Module Architecture Overview

### Module Interface Design
Mỗi module implement standard interface với lifecycle methods:
- **Initialization**: Setup dependencies và configuration
- **Route Registration**: Đăng ký HTTP routes
- **Migration**: Database schema changes
- **Seeding**: Test data generation

### Key Modules

#### Auth Module
- **JWT Authentication**: Access/refresh token flow
- **Role-based Authorization**: Hierarchical permission system
- **User Management**: Registration, profile, password management
- **Session Management**: Redis-based session storage

#### Blog Module  
- **Content Management**: Posts, categories, tags, comments
- **SEO Features**: Meta tags, sitemap generation
- **Media Integration**: Featured images, galleries
- **Publishing Workflow**: Draft → Review → Published

#### Socket Module
- **Real-time Communication**: WebSocket server integration
- **Event Broadcasting**: Live updates cho user interactions
- **Presence System**: Online user tracking
- **Notification Delivery**: Real-time notification push

#### Media Module
- **File Upload**: Multiple storage backends (local, MinIO, S3)
- **Image Processing**: Thumbnail generation, resizing
- **CDN Integration**: Optimized content delivery
- **Metadata Extraction**: EXIF data, file information

## Plugin System Overview

### Plugin Architecture
- **Interface-based**: Standard plugin interface
- **Lifecycle Management**: Init, start, stop phases
- **Route Registration**: Plugins có thể add routes
- **Middleware Integration**: Custom middleware injection
- **Configuration**: Plugin-specific settings

### Available Plugins
- **Rate Limiter**: Request rate limiting
- **Cache**: Response caching strategies  
- **Storage**: Different storage backends
- **Analytics**: Usage tracking và metrics
- **Security**: Additional security layers

## Development Workflow

### Development Tools
- **Makefile**: Build automation và common tasks
- **Air**: Live reload cho development
- **Docker**: Containerized development environment
- **Testing**: Unit và integration test framework

### CLI Commands
- **Migration**: `go run cmd/server migrate up/down`
- **Seeding**: `go run cmd/server seed`
- **Server**: `go run cmd/server` hoặc `make dev`
- **Testing**: `make test` với coverage reporting

### Build & Deployment
- **Docker Multi-stage**: Optimized production images
- **Environment Configuration**: Config per environment
- **Health Checks**: Kubernetes-ready health endpoints
- **Graceful Shutdown**: Proper cleanup on termination

## Best Practices

### Architecture Principles
- **Separation of Concerns**: Clear layer separation
- **Dependency Injection**: Loose coupling giữa components
- **Interface Segregation**: Small, focused interfaces
- **Single Responsibility**: Mỗi component có một nhiệm vụ cụ thể

### Performance Guidelines
- **Database Optimization**: Proper indexing và query optimization
- **Caching Strategy**: Multi-level caching implementation
- **Connection Pooling**: Efficient resource utilization
- **Background Processing**: Asynchronous job processing

### Security Standards
- **Input Validation**: Comprehensive request validation
- **Authentication**: Secure JWT implementation
- **Authorization**: Role-based access control
- **Rate Limiting**: DDoS protection
- **Data Protection**: Sensitive data encryption

### Code Quality
- **Testing Strategy**: Unit, integration, và end-to-end testing
- **Documentation**: Comprehensive API và code documentation  
- **Code Review**: Mandatory review process
- **Linting**: Automated code quality checks

## Monitoring & Observability

### Logging
- **Structured Logging**: JSON-formatted logs với contextual information
- **Log Levels**: Appropriate log levels cho different environments
- **Correlation IDs**: Request tracing across services

### Metrics
- **Performance Metrics**: Response times, throughput
- **Business Metrics**: User activity, content engagement
- **System Metrics**: Resource utilization, error rates

### Health Checks
- **Readiness Probes**: Service ready để receive traffic
- **Liveness Probes**: Service health status
- **Dependency Checks**: Database, Redis, external service health

## Deployment Architecture

### Container Strategy
- **Multi-stage Builds**: Optimized Docker images
- **Base Images**: Security-hardened base images
- **Environment Variables**: Configuration through env vars

### Orchestration
- **Kubernetes**: Container orchestration platform
- **Horizontal Scaling**: Auto-scaling based on metrics
- **Service Discovery**: Internal service communication
- **Load Balancing**: Traffic distribution

### Data Management
- **Database**: MySQL cluster với replication
- **Cache**: Redis cluster cho high availability
- **Storage**: Distributed file storage
- **Backups**: Automated backup strategies

Hệ thống được thiết kế để handle production workloads với high availability, scalability, và maintainability. Kiến trúc modular cho phép development team work independently trên different features while maintaining system cohesion.