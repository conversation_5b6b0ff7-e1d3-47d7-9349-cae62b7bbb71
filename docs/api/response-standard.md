# Chuẩn Response API - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Tài liệu này định nghĩa chuẩn response format mà tất cả các modules và API endpoints phải tuân theo trong Blog API v3. Việc standardization này đảm bảo consistency và dễ dàng integration cho frontend clients.

## Cấu trúc Response Chuẩn

### Core Response Structure

```go
package response

import (
    "time"
)

// Status chứa thông tin về trạng thái của request
type Status struct {
    Code      int         `json:"code"`
    Message   string      `json:"message"`
    Success   bool        `json:"success"`
    ErrorCode string      `json:"error_code,omitempty"`
    Path      string      `json:"path"`
    Timestamp string      `json:"timestamp"`
    Details   interface{} `json:"details,omitempty"`
}

// Meta chứa metadata về pagination và thông tin bổ sung
type Meta struct {
    NextCursor string `json:"next_cursor,omitempty"`
    <PERSON><PERSON><PERSON>    bool   `json:"has_more,omitempty"`
    Total      *int64 `json:"total,omitempty"`        // Optional total count
    Limit      int    `json:"limit,omitempty"`        // Page size
    Runtime    string `json:"runtime,omitempty"`      // Request processing time
    WebsiteID  *int64 `json:"website_id,omitempty"`   // Website context for multi-tenancy
}

// Response là cấu trúc chính cho tất cả API responses
type Response struct {
    Status  Status      `json:"status"`
    Data    interface{} `json:"data"`
    Meta    *Meta       `json:"meta,omitempty"`
    Website *WebsiteContext `json:"website,omitempty"`  // Website context for multi-tenancy
}

// WebsiteContext chứa thông tin website cho multi-tenancy
type WebsiteContext struct {
    ID     int64  `json:"id"`
    Domain string `json:"domain"`
    Name   string `json:"name"`
    Theme  string `json:"theme,omitempty"`
}

// Detail để mô tả chi tiết lỗi validation hoặc thông tin bổ sung
type Detail struct {
    Field   string `json:"field,omitempty"`
    Message string `json:"message"`
    Code    string `json:"code,omitempty"`
}
```

### Helper Functions

```go
package response

import (
    "net/http"
    "time"
    "fmt"
)

// SuccessResponse tạo response thành công
func SuccessResponse(data interface{}, path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusOK,
            Message:   "Request processed successfully",
            Success:   true,
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data: data,
    }
}

// SuccessWithMetaResponse tạo response thành công với metadata
func SuccessWithMetaResponse(data interface{}, meta *Meta, path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusOK,
            Message:   "Request processed successfully",
            Success:   true,
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data: data,
        Meta: meta,
    }
}

// SuccessWithWebsiteResponse tạo response thành công với website context
func SuccessWithWebsiteResponse(data interface{}, websiteCtx *WebsiteContext, path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusOK,
            Message:   "Request processed successfully",
            Success:   true,
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data:    data,
        Website: websiteCtx,
    }
}

// SuccessWithMetaAndWebsiteResponse tạo response thành công với metadata và website context
func SuccessWithMetaAndWebsiteResponse(data interface{}, meta *Meta, websiteCtx *WebsiteContext, path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusOK,
            Message:   "Request processed successfully",
            Success:   true,
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data:    data,
        Meta:    meta,
        Website: websiteCtx,
    }
}

// ErrorResponse tạo response lỗi
func ErrorResponse(code int, message, errorCode, path string, details interface{}) *Response {
    return &Response{
        Status: Status{
            Code:      code,
            Message:   message,
            Success:   false,
            ErrorCode: errorCode,
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
            Details:   details,
        },
        Data: nil,
    }
}

// ValidationErrorResponse tạo response cho lỗi validation
func ValidationErrorResponse(path string, details []Detail) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusUnprocessableEntity,
            Message:   "Validation failed",
            Success:   false,
            ErrorCode: "VALIDATION_ERROR",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
            Details:   details,
        },
        Data: nil,
    }
}

// NotFoundResponse tạo response cho resource không tìm thấy
func NotFoundResponse(resource, path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusNotFound,
            Message:   fmt.Sprintf("%s not found", resource),
            Success:   false,
            ErrorCode: "NOT_FOUND",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data: nil,
    }
}

// UnauthorizedResponse tạo response cho lỗi authentication
func UnauthorizedResponse(path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusUnauthorized,
            Message:   "Authentication required",
            Success:   false,
            ErrorCode: "UNAUTHORIZED",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data: nil,
    }
}

// ForbiddenResponse tạo response cho lỗi authorization
func ForbiddenResponse(path string) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusForbidden,
            Message:   "Insufficient permissions",
            Success:   false,
            ErrorCode: "FORBIDDEN",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
        },
        Data: nil,
    }
}

// InternalErrorResponse tạo response cho lỗi server
func InternalErrorResponse(path string, details interface{}) *Response {
    return &Response{
        Status: Status{
            Code:      http.StatusInternalServerError,
            Message:   "Internal server error",
            Success:   false,
            ErrorCode: "INTERNAL_ERROR",
            Path:      path,
            Timestamp: time.Now().UTC().Format(time.RFC3339),
            Details:   details,
        },
        Data: nil,
    }
}
```

## Status Codes và Error Codes

### HTTP Status Codes

| Status Code | Ý nghĩa | Sử dụng |
|-------------|---------|---------|
| `200` | OK | Request thành công |
| `201` | Created | Resource được tạo thành công |
| `400` | Bad Request | Request không hợp lệ |
| `401` | Unauthorized | Cần authentication |
| `403` | Forbidden | Không có quyền truy cập |
| `404` | Not Found | Resource không tồn tại |
| `422` | Unprocessable Entity | Lỗi validation |
| `429` | Too Many Requests | Rate limit exceeded |
| `500` | Internal Server Error | Lỗi server |

### Error Codes Chuẩn

| Error Code | HTTP Status | Mô tả |
|------------|-------------|--------|
| `VALIDATION_ERROR` | 422 | Dữ liệu đầu vào không hợp lệ |
| `NOT_FOUND` | 404 | Resource không tìm thấy |
| `UNAUTHORIZED` | 401 | Chưa đăng nhập |
| `FORBIDDEN` | 403 | Không có quyền |
| `DUPLICATE_ENTRY` | 409 | Dữ liệu đã tồn tại |
| `RATE_LIMIT_EXCEEDED` | 429 | Vượt quá giới hạn request |
| `INTERNAL_ERROR` | 500 | Lỗi hệ thống |
| `DATABASE_ERROR` | 500 | Lỗi cơ sở dữ liệu |
| `EXTERNAL_SERVICE_ERROR` | 502 | Lỗi dịch vụ bên ngoài |

## Response Examples

### Success Response - Single Item

```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/api/v1/posts/building-modern-apis",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "id": 123,
    "title": "Building Modern APIs with Go",
    "slug": "building-modern-apis",
    "content": "Full post content...",
    "author": {
      "id": 1,
      "name": "John Doe"
    },
    "published_at": "2024-01-15T10:30:00Z"
  }
}
```

### Success Response - List with Pagination

```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/api/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": [
    {
      "id": 123,
      "title": "Post Title 1",
      "slug": "post-title-1"
    },
    {
      "id": 124,
      "title": "Post Title 2", 
      "slug": "post-title-2"
    }
  ],
  "meta": {
    "next_cursor": "eyJpZCI6MTI0LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
    "has_more": true,
    "total": 156,
    "limit": 12,
    "runtime": "45ms",
    "website_id": 1
  },
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog",
    "theme": "modern-blog"
  }
}
```

### Error Response - Validation Error

```json
{
  "status": {
    "code": 422,
    "message": "Validation failed",
    "success": false,
    "error_code": "VALIDATION_ERROR",
    "path": "/api/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": [
      {
        "field": "title",
        "message": "Title is required",
        "code": "REQUIRED"
      },
      {
        "field": "content",
        "message": "Content must be at least 100 characters",
        "code": "MIN_LENGTH"
      }
    ]
  },
  "data": null
}
```

### Error Response - Not Found

```json
{
  "status": {
    "code": 404,
    "message": "Post not found",
    "success": false,
    "error_code": "NOT_FOUND",
    "path": "/api/v1/posts/invalid-slug",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": null
}
```

### Error Response - Authentication Required

```json
{
  "status": {
    "code": 401,
    "message": "Authentication required",
    "success": false,
    "error_code": "UNAUTHORIZED",
    "path": "/cms/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": null
}
```

### Error Response - Rate Limit

```json
{
  "status": {
    "code": 429,
    "message": "Rate limit exceeded",
    "success": false,
    "error_code": "RATE_LIMIT_EXCEEDED",
    "path": "/api/v1/search",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "limit": 100,
      "reset_time": "2024-01-15T11:00:00Z",
      "retry_after": 1800
    }
  },
  "data": null
}
```

## Pagination Standard

### Cursor-based Pagination

Tất cả list endpoints phải sử dụng cursor-based pagination:

```json
{
  "meta": {
    "next_cursor": "eyJpZCI6MTI0LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ",
    "has_more": true,
    "limit": 12,
    "total": 156,
    "runtime": "45ms"
  }
}
```

### Cursor Format

Cursor được encode bằng base64 và chứa thông tin cần thiết để lấy page tiếp theo:

```go
type CursorData struct {
    ID   int64     `json:"id"`
    Time time.Time `json:"time,omitempty"`
    Score float64  `json:"score,omitempty"` // For search results
}

// Encode cursor
func EncodeCursor(data CursorData) string {
    jsonData, _ := json.Marshal(data)
    return base64.StdEncoding.EncodeToString(jsonData)
}

// Decode cursor
func DecodeCursor(cursor string) (*CursorData, error) {
    data, err := base64.StdEncoding.DecodeString(cursor)
    if err != nil {
        return nil, err
    }
    
    var cursorData CursorData
    err = json.Unmarshal(data, &cursorData)
    return &cursorData, err
}
```

## Middleware Implementation

### Response Wrapper Middleware

```go
package middleware

import (
    "time"
    "github.com/gin-gonic/gin"
    "your-project/pkg/response"
)

// ResponseWrapper middleware để wrap tất cả responses
func ResponseWrapper() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        // Record start time
        start := time.Now()
        
        // Process request
        c.Next()
        
        // Calculate runtime
        runtime := time.Since(start)
        
        // Add runtime to response if meta exists
        if meta, exists := c.Get("response_meta"); exists {
            if m, ok := meta.(*response.Meta); ok {
                m.Runtime = runtime.String()
            }
        }
        
        // Set common headers
        c.Header("X-Response-Time", runtime.String())
        c.Header("X-Request-ID", c.GetString("request_id"))
    })
}

// ErrorHandler middleware để handle errors
func ErrorHandler() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        c.Next()
        
        // Handle any errors that occurred
        if len(c.Errors) > 0 {
            err := c.Errors.Last()
            
            var resp *response.Response
            switch err.Type {
            case gin.ErrorTypePublic:
                resp = response.ErrorResponse(400, err.Error(), "BAD_REQUEST", c.Request.URL.Path, nil)
            case gin.ErrorTypeBind:
                resp = response.ValidationErrorResponse(c.Request.URL.Path, []response.Detail{
                    {Message: err.Error(), Code: "BINDING_ERROR"},
                })
            default:
                resp = response.InternalErrorResponse(c.Request.URL.Path, err.Error())
            }
            
            c.JSON(resp.Status.Code, resp)
            return
        }
    })
}
```

## Usage Examples

### Controller Implementation

```go
package controllers

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "your-project/pkg/response"
    "your-project/internal/services"
)

type PostController struct {
    postService services.PostService
}

// GetPosts returns list of posts with pagination
func (pc *PostController) GetPosts(c *gin.Context) {
    // Get website ID from context
    websiteID := c.GetInt64("website_id")
    
    // Parse query parameters
    cursor := c.Query("cursor")
    limit := c.DefaultQuery("limit", "12")
    
    // Get posts from service with website context
    posts, nextCursor, hasMore, total, err := pc.postService.GetPostsForWebsite(websiteID, cursor, limit)
    if err != nil {
        resp := response.InternalErrorResponse(c.Request.URL.Path, err.Error())
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    // Create meta data with website context
    meta := &response.Meta{
        NextCursor: nextCursor,
        HasMore:    hasMore,
        Total:      &total,
        Limit:      parseInt(limit),
        WebsiteID:  &websiteID,
    }
    
    // Get website context
    websiteCtx := &response.WebsiteContext{
        ID:     websiteID,
        Domain: pc.getWebsiteDomain(websiteID),
        Name:   pc.getWebsiteName(websiteID),
        Theme:  pc.getWebsiteTheme(websiteID),
    }
    
    // Send response with website context
    resp := response.SuccessWithMetaAndWebsiteResponse(posts, meta, websiteCtx, c.Request.URL.Path)
    c.JSON(http.StatusOK, resp)
}

// GetPost returns single post
func (pc *PostController) GetPost(c *gin.Context) {
    slug := c.Param("slug")
    
    post, err := pc.postService.GetPostBySlug(slug)
    if err != nil {
        if errors.Is(err, services.ErrPostNotFound) {
            resp := response.NotFoundResponse("Post", c.Request.URL.Path)
            c.JSON(resp.Status.Code, resp)
            return
        }
        
        resp := response.InternalErrorResponse(c.Request.URL.Path, err.Error())
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    resp := response.SuccessResponse(post, c.Request.URL.Path)
    c.JSON(http.StatusOK, resp)
}

// CreatePost creates new post
func (pc *PostController) CreatePost(c *gin.Context) {
    var req CreatePostRequest
    
    if err := c.ShouldBindJSON(&req); err != nil {
        details := []response.Detail{
            {Message: err.Error(), Code: "BINDING_ERROR"},
        }
        resp := response.ValidationErrorResponse(c.Request.URL.Path, details)
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    // Validate request
    if validationErrors := pc.validateCreatePost(&req); len(validationErrors) > 0 {
        resp := response.ValidationErrorResponse(c.Request.URL.Path, validationErrors)
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    post, err := pc.postService.CreatePost(&req)
    if err != nil {
        resp := response.InternalErrorResponse(c.Request.URL.Path, err.Error())
        c.JSON(resp.Status.Code, resp)
        return
    }
    
    resp := response.SuccessResponse(post, c.Request.URL.Path)
    c.JSON(http.StatusCreated, resp)
}

func (pc *PostController) validateCreatePost(req *CreatePostRequest) []response.Detail {
    var details []response.Detail
    
    if req.Title == "" {
        details = append(details, response.Detail{
            Field:   "title",
            Message: "Title is required",
            Code:    "REQUIRED",
        })
    }
    
    if len(req.Content) < 100 {
        details = append(details, response.Detail{
            Field:   "content",
            Message: "Content must be at least 100 characters",
            Code:    "MIN_LENGTH",
        })
    }
    
    return details
}
```

## Testing Standards

### Unit Test Examples

```go
package controllers_test

import (
    "testing"
    "encoding/json"
    "net/http/httptest"
    "github.com/stretchr/testify/assert"
    "your-project/pkg/response"
)

func TestGetPosts_Success(t *testing.T) {
    // Setup
    controller := setupPostController()
    router := setupRouter(controller)
    
    // Execute
    req := httptest.NewRequest("GET", "/api/v1/posts?limit=10", nil)
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // Assert
    assert.Equal(t, 200, w.Code)
    
    var resp response.Response
    err := json.Unmarshal(w.Body.Bytes(), &resp)
    assert.NoError(t, err)
    
    assert.True(t, resp.Status.Success)
    assert.Equal(t, 200, resp.Status.Code)
    assert.Equal(t, "Request processed successfully", resp.Status.Message)
    assert.NotNil(t, resp.Data)
    assert.NotNil(t, resp.Meta)
    assert.Equal(t, 10, resp.Meta.Limit)
}

func TestGetPost_NotFound(t *testing.T) {
    // Setup
    controller := setupPostController()
    router := setupRouter(controller)
    
    // Execute
    req := httptest.NewRequest("GET", "/api/v1/posts/invalid-slug", nil)
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // Assert
    assert.Equal(t, 404, w.Code)
    
    var resp response.Response
    err := json.Unmarshal(w.Body.Bytes(), &resp)
    assert.NoError(t, err)
    
    assert.False(t, resp.Status.Success)
    assert.Equal(t, 404, resp.Status.Code)
    assert.Equal(t, "NOT_FOUND", resp.Status.ErrorCode)
    assert.Nil(t, resp.Data)
}
```

## Documentation Standards

### API Docs Format

Tất cả API documentation phải include:

1. **Request Format**: Method, URL, headers, body
2. **Response Format**: Sử dụng chuẩn response structure
3. **Error Cases**: List tất cả possible error responses
4. **Examples**: Cả success và error examples

### Swagger/OpenAPI Integration

```yaml
components:
  schemas:
    Status:
      type: object
      required:
        - code
        - message
        - success
        - path
        - timestamp
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Request processed successfully"
        success:
          type: boolean
          example: true
        error_code:
          type: string
          example: "NOT_FOUND"
        path:
          type: string
          example: "/api/v1/posts"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        details:
          type: object
          
    Meta:
      type: object
      properties:
        next_cursor:
          type: string
          example: "eyJpZCI6MTI0fQ"
        has_more:
          type: boolean
          example: true
        total:
          type: integer
          example: 156
        limit:
          type: integer
          example: 12
        runtime:
          type: string
          example: "45ms"
          
    Response:
      type: object
      required:
        - status
        - data
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
        meta:
          $ref: '#/components/schemas/Meta'
```

## Validation Rules

### Tất cả modules phải:

1. ✅ Sử dụng chuẩn response structure
2. ✅ Implement proper error handling
3. ✅ Sử dụng cursor-based pagination
4. ✅ Include proper status codes và error codes
5. ✅ Provide detailed validation errors
6. ✅ Add request/response logging
7. ✅ Include performance metrics (runtime)
8. ✅ Follow naming conventions
9. ✅ Include website context for multi-tenancy
10. ✅ Filter all data by website_id
11. ✅ Validate user access to website resources

### Code Review Checklist

- [ ] Response structure tuân theo chuẩn
- [ ] Error codes được định nghĩa properly
- [ ] Pagination implementation đúng
- [ ] Validation errors chi tiết
- [ ] Status codes appropriate
- [ ] Runtime metrics included
- [ ] Tests cover success và error cases
- [ ] Documentation updated
- [ ] Website context included in responses
- [ ] Website ID validation implemented
- [ ] Multi-tenancy isolation enforced
- [ ] Cache keys use website_id prefix

## Migration Guide

### Updating Existing APIs

1. **Install response package**:
   ```go
   import "your-project/pkg/response"
   ```

2. **Update controller methods**:
   ```go
   // Old way
   c.JSON(200, gin.H{"data": posts, "total": total})
   
   // New way with website context
   websiteID := c.GetInt64("website_id")
   websiteCtx := &response.WebsiteContext{
       ID:     websiteID,
       Domain: getWebsiteDomain(websiteID),
       Name:   getWebsiteName(websiteID),
   }
   resp := response.SuccessWithMetaAndWebsiteResponse(posts, meta, websiteCtx, c.Request.URL.Path)
   c.JSON(http.StatusOK, resp)
   ```

3. **Update error handling**:
   ```go
   // Old way
   c.JSON(404, gin.H{"error": "Post not found"})
   
   // New way
   resp := response.NotFoundResponse("Post", c.Request.URL.Path)
   c.JSON(resp.Status.Code, resp)
   ```

4. **Update tests**:
   ```go
   var resp response.Response
   json.Unmarshal(w.Body.Bytes(), &resp)
   assert.True(t, resp.Status.Success)
   assert.NotNil(t, resp.Website)  // Test website context
   ```

### Multi-Tenancy Middleware Integration

```go
func WebsiteContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetHeader("X-Website-ID")
        if websiteID == "" {
            resp := response.ErrorResponse(400, "Website ID is required", "MISSING_WEBSITE_ID", c.Request.URL.Path, nil)
            c.JSON(resp.Status.Code, resp)
            c.Abort()
            return
        }
        
        id, err := strconv.ParseInt(websiteID, 10, 64)
        if err != nil {
            resp := response.ErrorResponse(400, "Invalid website ID", "INVALID_WEBSITE_ID", c.Request.URL.Path, nil)
            c.JSON(resp.Status.Code, resp)
            c.Abort()
            return
        }
        
        c.Set("website_id", id)
        c.Next()
    }
}
```

## Best Practices

1. **Consistency**: Luôn sử dụng helper functions thay vì tạo response manually
2. **Error Details**: Cung cấp meaningful error messages và codes
3. **Performance**: Include runtime metrics để monitor performance
4. **Security**: Không expose sensitive information trong error details
5. **Logging**: Log tất cả errors với proper context
6. **Testing**: Test cả success và error scenarios
7. **Documentation**: Keep API docs updated với response examples

## Tài liệu liên quan

- [API Overview](./overview.md)
- [Error Handling](./error-handling.md)
- [Authentication](./authentication.md)
- [Rate Limiting](./rate-limiting.md)
- [Testing Guidelines](../best-practices/testing.md)