# Frontend API - Tà<PERSON> liệu Tiếng Việt

## Tổng quan

Frontend API được thiết kế cho website visitors và public interfaces, cung cấp read-only access đến content và các tính năng tương tác cơ bản. API này tối ưu cho performance và caching.

## Base URL

```
Production: https://yourblog.com/api/v1
Staging: https://staging.yourblog.com/api/v1  
Development: http://localhost:8080/api/v1
```

## Authentication

### Optional Authentication
```http
Authorization: Bearer <jwt_token>  # Optional for personalized content
```

### Headers yêu cầu
```http
Content-Type: application/json
Accept: application/json
X-Website-ID: <website_id>  # Required for multi-tenant
X-Tenant-Domain: <domain_name>  # Alternative: domain-based routing
User-Agent: <client_info>
```

## API Structure Overview

```mermaid
flowchart TD
    A[Frontend API] --> B[Content Delivery]
    A --> C[User Interactions]
    A --> D[Site Configuration]
    A --> E[Search & Discovery]
    A --> F[Media Delivery]
    A --> G[Analytics Tracking]
    
    B --> B1[Posts]
    B --> B2[Categories]
    B --> B3[Tags]
    B --> B4[Pages]
    B --> B5[Authors]
    
    C --> C1[Comments]
    C --> C2[Likes/Reactions]
    C --> C3[Subscriptions]
    C --> C4[Contact Forms]
    
    D --> D1[Site Settings]
    D --> D2[Navigation Menus]
    D --> D3[Theme Config]
    D --> D4[SEO Data]
    
    E --> E1[Search Posts]
    E --> E2[Trending Content]
    E --> E3[Related Posts]
    E --> E4[Archives]
```

## Content Delivery

### Posts

#### List Published Posts
```http
GET /api/v1/posts?cursor=eyJpZCI6MTIzLCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDozMDowMFoifQ&limit=12&category=technology&sort=published_at&order=desc
X-Website-ID: <website_id>
```

**Query Parameters:**
- `cursor`: Cursor for pagination (base64 encoded position)
- `limit`: Items per page (default: 12, max: 50)
- `category`: Filter by category slug
- `tag`: Filter by tag slug
- `author`: Filter by author ID or slug
- `search`: Search in title and content
- `sort`: Sort field (published_at, views, likes)
- `order`: Sort order (asc, desc)
- `featured`: Filter featured posts (true/false)

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Request processed successfully",
    "success": true,
    "path": "/api/v1/posts",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "data": {
    "posts": [
      {
        "id": 123,
        "title": "Building Modern APIs with Go",
        "slug": "building-modern-apis-with-go",
        "excerpt": "Learn how to build scalable REST APIs using Go and Gin framework...",
        "featured_image": {
          "url": "https://cdn.example.com/image.jpg",
          "alt": "API Development",
          "sizes": {
            "thumbnail": "https://cdn.example.com/image-150x150.jpg",
            "medium": "https://cdn.example.com/image-400x300.jpg",
            "large": "https://cdn.example.com/image-800x600.jpg"
          }
        },
        "author": {
          "id": 1,
          "name": "John Doe",
          "slug": "john-doe",
          "avatar": "https://cdn.example.com/avatar.jpg",
          "bio": "Software Developer"
        },
        "categories": [
          {
            "id": 5,
            "name": "Technology",
            "slug": "technology",
            "color": "#3B82F6"
          }
        ],
        "tags": [
          {
            "id": 10,
            "name": "Go",
            "slug": "go"
          },
          {
            "id": 15,
            "name": "API",
            "slug": "api"
          }
        ],
        "stats": {
          "views": 1250,
          "likes": 45,
          "comments": 12,
          "reading_time": 8
        },
        "published_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  },
  "meta": {
    "limit": 12,
    "has_more": true,
    "next_cursor": "eyJpZCI6MTM0LCJ0aW1lIjoiMjAyNC0wMS0xNFQwODozMDowMFoifQ",
    "prev_cursor": null,
    "total": 156,
    "website_id": 1
  },
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog"
  }
}
```

#### Get Single Post
```http
GET /api/v1/posts/{slug}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "post": {
      "id": 123,
      "title": "Building Modern APIs with Go",
      "slug": "building-modern-apis-with-go",
      "content": "Full markdown content of the post...",
      "excerpt": "Brief description...",
      "featured_image": {
        "url": "https://cdn.example.com/image.jpg",
        "alt": "API Development",
        "caption": "Modern API architecture"
      },
      "author": {
        "id": 1,
        "name": "John Doe",
        "slug": "john-doe",
        "avatar": "https://cdn.example.com/avatar.jpg",
        "bio": "Software Developer and Tech Writer",
        "social_links": {
          "twitter": "johndoe",
          "github": "johndoe",
          "linkedin": "johndoe"
        },
        "stats": {
          "posts_count": 25,
          "followers_count": 189
        }
      },
      "categories": [...],
      "tags": [...],
      "seo": {
        "meta_title": "Building Modern APIs with Go - Complete Guide",
        "meta_description": "Learn how to build scalable REST APIs...",
        "meta_keywords": ["go", "api", "rest", "backend"],
        "canonical_url": "https://yourblog.com/posts/building-modern-apis-with-go"
      },
      "related_posts": [
        {
          "id": 124,
          "title": "Go Best Practices",
          "slug": "go-best-practices",
          "featured_image": {...},
          "published_at": "2024-01-10T08:00:00Z"
        }
      ],
      "stats": {
        "views": 1250,
        "likes": 45,
        "comments": 12,
        "shares": 8,
        "reading_time": 8
      },
      "table_of_contents": [
        {
          "id": "introduction",
          "title": "Introduction",
          "level": 1
        },
        {
          "id": "setting-up-gin",
          "title": "Setting up Gin Framework",
          "level": 2
        }
      ],
      "published_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### Get Trending Posts
```http
GET /api/v1/posts/trending?period=week&limit=10
```

**Query Parameters:**
- `period`: Time period (day, week, month, year)
- `limit`: Number of posts (default: 10, max: 20)

### Categories

#### List Categories
```http
GET /api/v1/categories?include_posts_count=true&parent_only=false
```

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 5,
        "name": "Technology",
        "slug": "technology",
        "description": "Latest technology trends and tutorials",
        "color": "#3B82F6",
        "image": {
          "url": "https://cdn.example.com/category-tech.jpg",
          "alt": "Technology Category"
        },
        "posts_count": 45,
        "parent_id": null,
        "children": [
          {
            "id": 8,
            "name": "Programming",
            "slug": "programming",
            "posts_count": 23
          }
        ],
        "latest_post": {
          "id": 123,
          "title": "Latest Tech Post",
          "published_at": "2024-01-15T10:30:00Z"
        }
      }
    ]
  }
}
```

#### Get Category with Posts
```http
GET /api/v1/categories/{slug}/posts?cursor=eyJpZCI6MTIzfQ&limit=12
```

### Tags

#### List Popular Tags
```http
GET /api/v1/tags?sort=posts_count&limit=50
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tags": [
      {
        "id": 10,
        "name": "Go",
        "slug": "go",
        "description": "Go programming language",
        "posts_count": 23,
        "color": "#00ADD8"
      }
    ]
  }
}
```

#### Get Tag with Posts
```http
GET /api/v1/tags/{slug}/posts?cursor=eyJpZCI6MTIzfQ&limit=12
```

### Authors

#### List Authors
```http
GET /api/v1/authors?sort=posts_count&limit=20
```

**Response:**
```json
{
  "success": true,
  "data": {
    "authors": [
      {
        "id": 1,
        "name": "John Doe",
        "slug": "john-doe",
        "avatar": "https://cdn.example.com/avatar.jpg",
        "bio": "Software Developer and Tech Writer",
        "social_links": {
          "twitter": "johndoe",
          "github": "johndoe",
          "website": "https://johndoe.com"
        },
        "stats": {
          "posts_count": 25,
          "total_views": 15000,
          "followers_count": 189
        },
        "latest_post": {
          "id": 123,
          "title": "Latest Post",
          "published_at": "2024-01-15T10:30:00Z"
        }
      }
    ]
  }
}
```

#### Get Author Profile
```http
GET /api/v1/authors/{slug}
```

#### Get Author Posts
```http
GET /api/v1/authors/{slug}/posts?cursor=eyJpZCI6MTIzfQ&limit=12
```

## User Interactions

### Comments

#### List Post Comments
```http
GET /api/v1/posts/{post_id}/comments?cursor=eyJpZCI6NDU2fQ&limit=20&sort=created_at&order=desc
```

**Response:**
```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": 456,
        "content": "Great article! Very helpful for beginners.",
        "author": {
          "id": 789,
          "name": "Jane Smith",
          "avatar": "https://cdn.example.com/avatar2.jpg",
          "is_verified": false
        },
        "likes_count": 5,
        "replies_count": 2,
        "replies": [
          {
            "id": 457,
            "content": "Thanks for the feedback!",
            "author": {
              "id": 1,
              "name": "John Doe",
              "avatar": "https://cdn.example.com/avatar.jpg",
              "is_verified": true,
              "is_author": true
            },
            "likes_count": 2,
            "created_at": "2024-01-15T11:00:00Z"
          }
        ],
        "created_at": "2024-01-15T10:45:00Z"
      }
    ],
    "pagination": {
      "limit": 20,
      "has_next": true,
      "has_prev": false,
      "next_cursor": "eyJpZCI6NDU3LCJ0aW1lIjoiMjAyNC0wMS0xNVQxMDo0NTowMFoifQ",
      "prev_cursor": null
    }
  }
}
```

#### Create Comment
```http
POST /api/v1/posts/{post_id}/comments
Content-Type: application/json

{
  "content": "This is a great article!",
  "parent_id": null,
  "author": {
    "name": "Anonymous User",
    "email": "<EMAIL>",
    "website": "https://example.com"
  }
}
```

#### Like Comment
```http
POST /api/v1/comments/{comment_id}/like
```

### Reactions

#### Add Post Reaction
```http
POST /api/v1/posts/{post_id}/reactions
Content-Type: application/json

{
  "type": "like"  // like, love, laugh, wow, sad, angry
}
```

#### Get Post Reactions
```http
GET /api/v1/posts/{post_id}/reactions
```

**Response:**
```json
{
  "success": true,
  "data": {
    "reactions": {
      "like": 45,
      "love": 12,
      "wow": 3
    },
    "total": 60,
    "user_reaction": "like"
  }
}
```

### Newsletter Subscription

#### Subscribe to Newsletter
```http
POST /api/v1/newsletter/subscribe
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "John Doe",
  "preferences": {
    "weekly_digest": true,
    "new_posts": false,
    "categories": ["technology", "programming"]
  }
}
```

#### Unsubscribe from Newsletter
```http
POST /api/v1/newsletter/unsubscribe
Content-Type: application/json

{
  "email": "<EMAIL>",
  "token": "unsubscribe_token"
}
```

### Contact Forms

#### Submit Contact Form
```http
POST /api/v1/contact
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "subject": "Question about your blog",
  "message": "I have a question about...",
  "category": "general"
}
```

## Search & Discovery

### Search

#### Search Posts
```http
GET /api/v1/search?q=golang&type=posts&cursor=eyJzY29yZSI6MC45NSwiaWQiOjEyM30&limit=12
```

**Query Parameters:**
- `q`: Search query
- `type`: Content type (posts, authors, categories, all)
- `cursor`: Cursor for pagination (based on relevance score and ID)
- `limit`: Results per page
- `category`: Filter by category
- `date_from`: Filter from date
- `date_to`: Filter to date

**Response:**
```json
{
  "success": true,
  "data": {
    "results": {
      "posts": [
        {
          "id": 123,
          "title": "Building Modern APIs with Go",
          "slug": "building-modern-apis-with-go",
          "excerpt": "Learn how to build scalable REST APIs...",
          "highlighted_content": "...building scalable <mark>Go</mark> applications...",
          "relevance_score": 0.95,
          "published_at": "2024-01-15T10:30:00Z"
        }
      ],
      "authors": [...],
      "categories": [...]
    },
    "pagination": {
      "limit": 12,
      "has_next": true,
      "has_prev": false,
      "next_cursor": "eyJzY29yZSI6MC44NywiaWQiOjEzNH0",
      "prev_cursor": null
    },
    "total_results": 45,
    "search_time": 0.12,
    "suggestions": ["golang tutorial", "go best practices"]
  }
}
```

#### Search Suggestions
```http
GET /api/v1/search/suggestions?q=gola
```

**Response:**
```json
{
  "success": true,
  "data": {
    "suggestions": [
      "golang",
      "golang tutorial",
      "golang best practices"
    ]
  }
}
```

### Archives

#### Get Archive by Date
```http
GET /api/v1/archives/2024/01?limit=20
```

#### Get Archive List
```http
GET /api/v1/archives
```

**Response:**
```json
{
  "success": true,
  "data": {
    "archives": [
      {
        "year": 2024,
        "months": [
          {
            "month": 1,
            "name": "January",
            "posts_count": 12,
            "url": "/archives/2024/01"
          }
        ],
        "posts_count": 45
      }
    ]
  }
}
```

## Site Configuration

### Site Settings

#### Get Site Information
```http
GET /api/v1/site
```

**Response:**
```json
{
  "success": true,
  "data": {
    "site": {
      "name": "My Tech Blog",
      "description": "A blog about technology and programming",
      "url": "https://yourblog.com",
      "logo": {
        "url": "https://cdn.example.com/logo.png",
        "alt": "My Tech Blog"
      },
      "favicon": "https://cdn.example.com/favicon.ico",
      "language": "en",
      "timezone": "UTC",
      "social_links": {
        "twitter": "mytechblog",
        "github": "mytechblog",
        "linkedin": "company/mytechblog"
      },
      "contact_email": "<EMAIL>",
      "copyright": "© 2024 My Tech Blog. All rights reserved."
    }
  }
}
```

### Navigation Menus

#### Get Navigation Menu
```http
GET /api/v1/menus/{location}  # header, footer, sidebar
```

**Response:**
```json
{
  "success": true,
  "data": {
    "menu": {
      "id": 1,
      "name": "Main Navigation",
      "location": "header",
      "items": [
        {
          "id": 1,
          "title": "Home",
          "url": "/",
          "type": "page",
          "target": "_self",
          "order": 1,
          "children": []
        },
        {
          "id": 2,
          "title": "Blog",
          "url": "/blog",
          "type": "page",
          "order": 2,
          "children": [
            {
              "id": 3,
              "title": "Technology",
              "url": "/category/technology",
              "type": "category",
              "order": 1
            }
          ]
        }
      ]
    }
  }
}
```

### Theme Configuration

#### Get Theme Settings
```http
GET /api/v1/theme
```

**Response:**
```json
{
  "success": true,
  "data": {
    "theme": {
      "name": "Modern Blog",
      "version": "1.2.0",
      "settings": {
        "primary_color": "#3B82F6",
        "secondary_color": "#64748B",
        "font_family": "Inter",
        "header_style": "minimal",
        "sidebar_position": "right",
        "post_layout": "card",
        "dark_mode_enabled": true
      },
      "custom_css": "/* Custom styles */",
      "features": {
        "search": true,
        "comments": true,
        "newsletter": true,
        "social_sharing": true,
        "reading_progress": true
      }
    }
  }
}
```

## Media Delivery

### Images

#### Get Optimized Image
```http
GET /api/v1/media/images/{id}?size=medium&format=webp&quality=80
```

**Query Parameters:**
- `size`: Image size (thumbnail, small, medium, large, original)
- `format`: Output format (webp, jpg, png)
- `quality`: Compression quality (1-100)
- `width`: Custom width
- `height`: Custom height

### File Downloads

#### Download File
```http
GET /api/v1/media/files/{id}/download
```

## Analytics Tracking

### Page Views

#### Track Page View
```http
POST /api/v1/analytics/pageview
Content-Type: application/json

{
  "url": "/posts/building-modern-apis-with-go",
  "title": "Building Modern APIs with Go",
  "referrer": "https://google.com",
  "user_agent": "Mozilla/5.0...",
  "session_id": "abc123",
  "user_id": 456  // Optional for logged-in users
}
```

### Event Tracking

#### Track Custom Event
```http
POST /api/v1/analytics/event
Content-Type: application/json

{
  "event": "post_share",
  "properties": {
    "post_id": 123,
    "platform": "twitter",
    "url": "/posts/building-modern-apis-with-go"
  },
  "session_id": "abc123"
}
```

## RSS/Sitemap

### RSS Feeds

#### Main RSS Feed
```http
GET /api/v1/feed.xml
```

#### Category RSS Feed
```http
GET /api/v1/categories/{slug}/feed.xml
```

#### Author RSS Feed
```http
GET /api/v1/authors/{slug}/feed.xml
```

### Sitemap

#### Get Sitemap
```http
GET /api/v1/sitemap.xml
```

## Error Handling

### Error Response Format
```json
{
  "status": {
    "code": 404,
    "message": "Post not found",
    "success": false,
    "error_code": "NOT_FOUND",
    "path": "/api/v1/posts/invalid-post-slug",
    "timestamp": "2024-01-15T10:30:00Z",
    "details": {
      "slug": "invalid-post-slug"
    }
  },
  "data": null,
  "website": {
    "id": 1,
    "domain": "myblog.com",
    "name": "My Tech Blog"
  }
}
```

### HTTP Status Codes
- `200` OK - Request successful
- `400` Bad Request - Invalid request data
- `404` Not Found - Resource not found
- `429` Too Many Requests - Rate limit exceeded
- `500` Internal Server Error - Server error

## Caching & Performance

### Cache Headers
```http
Cache-Control: public, max-age=300
ETag: "abc123def456"
Last-Modified: Mon, 15 Jan 2024 10:30:00 GMT
```

### CDN Integration
- Images automatically served through CDN
- Optimized image formats (WebP, AVIF)
- Responsive image sizing
- Lazy loading support

## Rate Limiting

```http
X-RateLimit-Limit: 500
X-RateLimit-Remaining: 499
X-RateLimit-Reset: 1640995200
```

### Rate Limits by Endpoint
- **Content Reading**: 500 requests per hour
- **Search**: 100 requests per hour
- **Comments/Interactions**: 50 requests per hour
- **Analytics**: 1000 requests per hour

## CORS Configuration

```http
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

## WebSocket Events (Real-time)

### Subscribe to Real-time Updates
```javascript
const ws = new WebSocket('wss://yourblog.com/api/v1/ws');

// Subscribe to post updates
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'post:123',
  events: ['new_comment', 'like_update']
}));

// Receive real-time updates
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Real-time update:', data);
};
```

### Available Events
- `new_comment` - New comment on post
- `like_update` - Like count updated
- `view_update` - View count updated
- `new_post` - New post published

## SDK và Client Libraries

### JavaScript SDK
```javascript
import BlogAPI from '@yourblog/frontend-sdk';

const api = new BlogAPI({
  baseURL: 'https://yourblog.com/api/v1',
  websiteId: 1,  // Required for multi-tenancy
  domain: 'yourblog.com'
});

// Get posts for specific website
const posts = await api.posts.list({
  category: 'technology',
  limit: 12
});

// Get single post (automatically scoped to website)
const post = await api.posts.get('building-modern-apis-with-go');

// Add comment (website context included)
await api.comments.create(postId, {
  content: 'Great article!',
  author: { name: 'John', email: '<EMAIL>' }
});
```

### React Hooks
```javascript
import { usePosts, usePost } from '@yourblog/react-hooks';

function PostList({ websiteId }) {
  const { posts, loading, error, website } = usePosts({
    websiteId,  // Required for multi-tenancy
    category: 'technology',
    limit: 12
  });
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h1>{website.name}</h1>
      {posts.map(post => (
        <PostCard key={post.id} post={post} website={website} />
      ))}
    </div>
  );
}
```

## Best Practices

### Performance Optimization
- Use CDN for all static assets
- Implement proper caching headers
- Optimize images with responsive sizes
- Minimize API calls with batching

### SEO Optimization
- Include structured data in responses
- Provide canonical URLs
- Support Open Graph metadata
- Generate XML sitemaps

### Security
- Rate limiting on all endpoints
- Input validation and sanitization
- HTTPS-only in production
- CORS configuration

### Accessibility
- Provide alt text for images
- Support screen readers with semantic markup
- Include ARIA labels in responses

## Multi-Tenancy Features

### Website Context
- All API responses include website context
- Content is automatically filtered by website_id
- URLs are scoped to the website domain
- Caching is isolated per website

### Domain-Based Routing
- Automatic website detection from domain
- Subdomain support (blog1.yourdomain.com)
- Custom domain support (myblog.com)
- CDN routing optimization

### Performance Optimization
- Website-specific caching strategies
- CDN configuration per website
- Database query optimization
- Static asset optimization

### Security
- Website-based rate limiting
- Content access control
- Domain validation
- CORS configuration per website

## Tài liệu liên quan

- [CMS API](./cms-api.md)
- [Authentication Guide](./authentication.md)
- [Error Handling](./error-handling.md)
- [Rate Limiting](./rate-limiting.md)
- [WebSocket Documentation](./websocket.md)
- [Response Standard](./response-standard.md)