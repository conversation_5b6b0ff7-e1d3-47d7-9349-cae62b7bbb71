# Golang Libraries - <PERSON><PERSON> sách thư viện Go

## Tổng quan

Tài liệu này liệt kê tất cả các thư viện Go được sử dụng trong Blog API v3, bao gồ<PERSON> mục đích sử dụng, <PERSON><PERSON><PERSON><PERSON> bả<PERSON>, và cấu hình cần thiết. Tất cả các thư viện được tổ chức theo chức năng để dễ dàng quản lý và nâng cấp.

## Core Framework & Web

### Gin Web Framework
```go
github.com/gin-gonic/gin v1.9.1
```
- **<PERSON><PERSON><PERSON> đích**: HTTP web framework chính
- **Tính năng**: Routing, middleware, JSON binding, validation
- **C<PERSON>u hình**: Custom middleware cho CORS, logging, authentication
- **L<PERSON> do chọn**: Performance cao, ecosystem phong phú, c<PERSON> pháp đơn giản

### Gin Middleware
```go
github.com/gin-contrib/cors v1.4.0          // CORS support
github.com/gin-contrib/sessions v0.0.5      // Session management
github.com/gin-contrib/static v0.0.1        // Static file serving
github.com/gin-contrib/gzip v0.0.6          // GZIP compression
github.com/gin-contrib/requestid v0.0.6     // Request ID middleware
github.com/gin-contrib/secure v0.0.1        // Security headers
```

## Database & ORM

### GORM ORM
```go
gorm.io/gorm v1.25.4
gorm.io/driver/mysql v1.5.1               // MySQL driver
gorm.io/plugin/soft_delete v1.2.1         // Soft delete plugin
```
- **Mục đích**: Object-Relational Mapping
- **Tính năng**: Auto migration, associations, hooks, soft delete
- **Plugin**: Soft delete, validation, serialization
- **Lý do chọn**: Active Record pattern, excellent MySQL support

### Database Migration
```go
github.com/golang-migrate/migrate/v4 v4.16.2
github.com/golang-migrate/migrate/v4/database/mysql v4.16.2
github.com/golang-migrate/migrate/v4/source/file v4.16.2
```
- **Mục đích**: Database schema migration
- **Tính năng**: Up/down migration, version control, rollback
- **Cấu hình**: Migration files trong `database/migrations/`

### Database Connection Pool
```go
github.com/jmoiron/sqlx v1.3.5             // Extended database/sql
go.uber.org/multierr v1.11.0               // Multiple error handling
```

## Configuration & Environment

### Viper Configuration
```go
github.com/spf13/viper v1.16.0
```
- **Mục đích**: Configuration management
- **Tính năng**: Multiple config formats, environment variables, remote config
- **Supported formats**: YAML, JSON, TOML, ENV
- **Lý do chọn**: Flexible, multiple sources, hot reload

### Environment Variables
```go
github.com/joho/godotenv v1.4.0
```
- **Mục đích**: Load environment variables từ .env file
- **Tính năng**: Development environment setup
- **Usage**: Development và testing environments

## Authentication & Security

### JWT Authentication
```go
github.com/golang-jwt/jwt/v5 v5.0.0
```
- **Mục đích**: JSON Web Token implementation
- **Tính năng**: Token generation, validation, claims parsing
- **Algorithm**: HS256, RS256 support

### Password Hashing
```go
golang.org/x/crypto v0.13.0
```
- **Package**: bcrypt, argon2, scrypt
- **Mục đích**: Secure password hashing
- **Algorithm**: bcrypt với cost factor configurable

### OAuth2 & Social Login
```go
golang.org/x/oauth2 v0.12.0                // OAuth2 client
github.com/markbates/goth v1.77.0          // Multi-provider OAuth
```
- **Providers**: Google, Facebook, GitHub, Twitter
- **Tính năng**: Social login integration

### Rate Limiting
```go
github.com/ulule/limiter/v3 v3.11.2
```
- **Mục đích**: API rate limiting
- **Storage**: Redis, memory, database backends
- **Algorithms**: Token bucket, fixed window

## Caching & Session

### Redis Client
```go
github.com/redis/go-redis/v9 v9.2.1
```
- **Mục đích**: Redis client cho caching và sessions
- **Tính năng**: Clustering, pipelining, pub/sub, streams
- **Use cases**: Cache, sessions, queues, real-time features

### Cache Abstraction
```go
github.com/patrickmn/go-cache v2.1.0
```
- **Mục đích**: In-memory cache với TTL
- **Tính năng**: Thread-safe, automatic cleanup
- **Use case**: Application-level caching

## Message Queue & Job Processing

### RabbitMQ
```go
github.com/streadway/amqp v1.1.0           // AMQP 0.9.1 client
github.com/wagslane/go-rabbitmq v0.12.4    // High-level wrapper
```
- **Mục đích**: Message queue cho background jobs
- **Patterns**: Work queues, pub/sub, routing, RPC
- **Use cases**: Email sending, image processing, notifications

### Background Jobs
```go
github.com/hibiken/asynq v0.24.1
```
- **Mục đích**: Distributed task queue
- **Features**: Retry logic, scheduling, monitoring
- **Storage**: Redis-based
- **Use cases**: Email jobs, cleanup tasks, periodic jobs

## File Storage & Media

### MinIO Client
```go
github.com/minio/minio-go/v7 v7.0.63
```
- **Mục đích**: Object storage client (MinIO/S3 compatible)
- **Features**: Multipart upload, presigned URLs, bucket policies
- **Use cases**: File upload, image storage, CDN integration

### Image Processing
```go
github.com/disintegration/imaging v1.6.2   // Image manipulation
github.com/h2non/bimg v1.1.9               // Fast image processing (libvips)
```
- **Features**: Resize, crop, rotate, format conversion
- **Formats**: JPEG, PNG, WebP, AVIF support
- **Performance**: Hardware-accelerated processing

### File Upload
```go
github.com/gabriel-vasile/mimetype v1.4.2  // MIME type detection
```
- **Mục đích**: File type validation và MIME detection
- **Security**: Prevent malicious file uploads

## Validation & Serialization

### Validation
```go
github.com/go-playground/validator/v10 v10.15.4
```
- **Mục đích**: Struct validation
- **Features**: Tag-based validation, custom validators
- **Integration**: Gin auto-validation

### JSON Processing
```go
github.com/json-iterator/go v1.1.12        // Faster JSON
github.com/tidwall/gjson v1.16.0           // JSON path queries
```
- **Performance**: Faster than standard encoding/json
- **Features**: Streaming, path queries, validation

## Logging & Monitoring

### Structured Logging
```go
github.com/sirupsen/logrus v1.9.3          // Structured logging
go.uber.org/zap v1.25.0                    // High-performance logging
```
- **Features**: Structured logs, multiple outputs, log levels
- **Performance**: Zap cho high-throughput, Logrus cho development
- **Formats**: JSON, text, custom formatters

### Metrics & Monitoring
```go
github.com/prometheus/client_golang v1.17.0 // Prometheus metrics
```
- **Metrics**: HTTP requests, database queries, custom metrics
- **Integration**: Grafana dashboards, alerting

### Distributed Tracing
```go
go.opentelemetry.io/otel v1.19.0           // OpenTelemetry SDK
go.opentelemetry.io/otel/trace v1.19.0     // Tracing API
go.opentelemetry.io/otel/exporters/jaeger v1.17.0 // Jaeger exporter
```
- **Purpose**: Distributed tracing và observability
- **Features**: Span tracking, performance monitoring
- **Integration**: Jaeger, Zipkin compatible

## Email & Notifications

### Email Sending
```go
gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
github.com/go-mail/mail v2.3.1
```
- **Features**: HTML/text emails, attachments, templates
- **SMTP**: Support cho multiple providers
- **Templates**: HTML email templates với data binding

### Email Templates
```go
github.com/matcornic/hermes/v2 v2.1.0
```
- **Purpose**: Beautiful email templates
- **Features**: Responsive design, customizable themes
- **Use cases**: Welcome emails, notifications, newsletters

### Push Notifications
```go
github.com/sideshow/apns2 v0.23.0          // Apple Push Notifications
github.com/appleboy/go-fcm v0.1.5          // Firebase Cloud Messaging
```
- **Platforms**: iOS (APNS), Android (FCM)
- **Features**: Badge updates, rich notifications

## Search & Indexing

### Elasticsearch
```go
github.com/elastic/go-elasticsearch/v8 v8.10.0
```
- **Purpose**: Full-text search và analytics
- **Features**: Indexing, searching, aggregations
- **Use cases**: Content search, analytics, logging

### Search Utilities
```go
github.com/blevesearch/bleve/v2 v2.3.10    // Pure Go search engine
```
- **Alternative**: Embedded search engine
- **Features**: Full-text search, faceting, highlighting

## Internationalization (i18n)

### i18n Support
```go
github.com/nicksnyder/go-i18n/v2 v2.2.1
golang.org/x/text v0.13.0                  // Text processing utilities
```
- **Features**: Message translation, pluralization
- **Formats**: YAML, JSON translation files
- **Localization**: Date, number, currency formatting

## Testing & Development

### Testing Framework
```go
github.com/stretchr/testify v1.8.4         // Testing toolkit
github.com/DATA-DOG/go-sqlmock v1.5.0      // SQL mock for testing
github.com/golang/mock v1.6.0              // Interface mocking
```
- **Features**: Assertions, mocks, test suites
- **Database**: SQL mock cho database testing
- **Mocking**: Auto-generated mocks từ interfaces

### HTTP Testing
```go
github.com/gavv/httpexpect/v2 v2.16.0      // HTTP API testing
```
- **Features**: Fluent API testing, JSON validation
- **Integration**: E2E API testing

### Fake Data
```go
github.com/bxcodec/faker/v3 v3.8.0         // Fake data generation
github.com/Pallinder/go-randomdata v1.2.0  // Random data generation
```
- **Purpose**: Test data generation
- **Features**: Realistic fake data cho testing

## Utilities & Helpers

### String Utilities
```go
github.com/gosimple/slug v1.13.1           // URL-friendly slugs
github.com/iancoleman/strcase v0.3.0       // String case conversion
```
- **Features**: URL slugs, case conversion
- **Use cases**: SEO-friendly URLs, API naming

### UUID Generation
```go
github.com/google/uuid v1.3.1
```
- **Purpose**: UUID v4 generation
- **Use cases**: Unique identifiers, request tracking

### Time Utilities
```go
github.com/araddon/dateparse v0.0.0-20210429162001-6b43995a97de
github.com/jinzhu/now v1.1.5               // Time helper
```
- **Features**: Flexible date parsing, time manipulation
- **Timezone**: Multi-timezone support

### File Utilities
```go
github.com/spf13/afero v1.9.5              // Filesystem abstraction
github.com/fsnotify/fsnotify v1.6.0        // File system notifications
```
- **Features**: Virtual filesystems, file watching
- **Testing**: Mock filesystem cho testing

## CLI & Command Line

### CLI Framework
```go
github.com/spf13/cobra v1.7.0              // CLI framework
github.com/spf13/pflag v1.0.5              // POSIX-compliant flags
```
- **Purpose**: Command-line interface
- **Features**: Commands, subcommands, flags, help generation
- **Use cases**: Migration commands, admin tools

### Progress & Output
```go
github.com/schollz/progressbar/v3 v3.13.1  // Progress bars
github.com/fatih/color v1.15.0             // Colored output
```
- **Features**: Progress indicators, colored console output
- **CLI UX**: Better command-line experience

## Development Tools

### Air (Hot Reload)
```go
github.com/cosmtrek/air v1.47.0
```
- **Purpose**: Hot reload cho development
- **Features**: Auto-restart on file changes
- **Configuration**: `.air.toml` config file

### Code Generation
```go
github.com/99designs/gqlgen v0.17.36       // GraphQL code generator
go.uber.org/mock v0.3.0                    // Mock generation
```
- **Purpose**: Code generation from schemas
- **Features**: Type-safe GraphQL, interface mocks

## Security & Crypto

### Cryptography
```go
golang.org/x/crypto v0.13.0                // Extended crypto
github.com/golang/crypto v0.0.0            // Additional crypto algorithms
```
- **Algorithms**: AES, RSA, ECDSA, bcrypt, scrypt, argon2
- **Use cases**: Password hashing, data encryption

### Security Headers
```go
github.com/unrolled/secure v1.13.0
```
- **Purpose**: Security middleware
- **Features**: HTTPS redirect, security headers, content type validation

## API Documentation

### Swagger/OpenAPI
```go
github.com/swaggo/swag v1.16.2             // Swagger generator
github.com/swaggo/gin-swagger v1.6.0       // Gin integration
github.com/swaggo/files v1.0.1             // Static files
```
- **Purpose**: API documentation generation
- **Features**: Auto-generate từ comments, interactive docs

## GraphQL (Optional)

### GraphQL Server
```go
github.com/99designs/gqlgen v0.17.36       // GraphQL server
github.com/graph-gophers/graphql-go v1.5.0 // Alternative GraphQL
```
- **Purpose**: GraphQL API (alternative to REST)
- **Features**: Schema-first, type safety, resolvers

## Performance & Profiling

### Profiling Tools
```go
github.com/pkg/profile v1.7.0              // Easy profiling
go.uber.org/automaxprocs v1.5.3            // Container CPU detection
```
- **Purpose**: Performance profiling và optimization
- **Features**: CPU, memory, goroutine profiling

### Load Testing
```go
github.com/tsenart/vegeta v12.8.4          // HTTP load testing
```
- **Purpose**: Load testing và benchmarking
- **Features**: Rate-limited requests, metrics reporting

## Docker & Deployment

### Docker Integration
```go
github.com/fsouza/go-dockerclient v1.9.7   // Docker client
```
- **Purpose**: Docker container management
- **Features**: Container lifecycle, image management

## Go Module Configuration

### go.mod Example
```go
module github.com/yourusername/blog-api-v3

go 1.21

require (
    // Web Framework
    github.com/gin-gonic/gin v1.9.1
    github.com/gin-contrib/cors v1.4.0
    github.com/gin-contrib/sessions v0.0.5
    
    // Database
    gorm.io/gorm v1.25.4
    gorm.io/driver/mysql v1.5.1
    github.com/golang-migrate/migrate/v4 v4.16.2
    
    // Configuration
    github.com/spf13/viper v1.16.0
    github.com/joho/godotenv v1.4.0
    
    // Authentication
    github.com/golang-jwt/jwt/v5 v5.0.0
    golang.org/x/crypto v0.13.0
    
    // Caching
    github.com/redis/go-redis/v9 v9.2.1
    
    // Queue
    github.com/hibiken/asynq v0.24.1
    
    // File Storage
    github.com/minio/minio-go/v7 v7.0.63
    
    // Validation
    github.com/go-playground/validator/v10 v10.15.4
    
    // Logging
    go.uber.org/zap v1.25.0
    
    // Testing
    github.com/stretchr/testify v1.8.4
    
    // CLI
    github.com/spf13/cobra v1.7.0
    
    // Utilities
    github.com/google/uuid v1.3.1
    github.com/gosimple/slug v1.13.1
)
```

## Library Selection Criteria

### Performance
- **Benchmarks**: Proven performance metrics
- **Memory Usage**: Low memory footprint
- **Concurrency**: Thread-safe implementations
- **Scalability**: Handles high load

### Maintainability
- **Active Development**: Regular updates và bug fixes
- **Community**: Large user base và contributors
- **Documentation**: Comprehensive docs và examples
- **Testing**: Good test coverage

### Security
- **Vulnerability**: No known security issues
- **Best Practices**: Follows security best practices
- **Updates**: Regular security patches
- **Audit**: Security audit history

### Compatibility
- **Go Version**: Compatible với Go 1.21+
- **Dependencies**: Minimal dependency conflicts
- **Platform**: Cross-platform support
- **Licensing**: Compatible licenses (MIT, Apache, BSD)

## Version Management

### Dependency Updates
```bash
# Check for updates
go list -u -m all

# Update specific dependency
go get -u github.com/gin-gonic/gin

# Update all dependencies
go get -u ./...

# Tidy up go.mod
go mod tidy
```

### Version Pinning
```go
// Pin to specific version
github.com/gin-gonic/gin v1.9.1

// Pin to commit hash
github.com/some/library v0.0.0-20231010120000-abcdef123456

// Use replace directive for local development
replace github.com/some/library => ../local/library
```

## Development Environment Setup

### Required Tools
```bash
# Go toolchain
go version # Should be 1.21+

# Development tools
go install github.com/cosmtrek/air@latest           # Hot reload
go install github.com/swaggo/swag/cmd/swag@latest  # Swagger docs
go install github.com/golang/mock/mockgen@latest   # Mock generation

# Database tools
go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest

# Testing tools
go install gotest.tools/gotestsum@latest           # Better test output
```

### IDE Extensions
- **VS Code**: Go extension, REST Client
- **GoLand**: Built-in Go support
- **Vim/Neovim**: vim-go, coc-go

## Production Considerations

### Binary Optimization
```bash
# Build optimized binary
go build -ldflags="-s -w" -o blog-api cmd/api/main.go

# With version info
go build -ldflags="-s -w -X main.version=v1.0.0" -o blog-api cmd/api/main.go
```

### Docker Optimization
```dockerfile
# Multi-stage build
FROM golang:1.21-alpine AS builder
# ... build stage

FROM alpine:latest
RUN apk --no-cache add ca-certificates
COPY --from=builder /app/blog-api .
CMD ["./blog-api"]
```

## Troubleshooting

### Common Issues

#### Dependency Conflicts
```bash
# Clean module cache
go clean -modcache

# Recreate go.mod
rm go.mod go.sum
go mod init your-module-name
go mod tidy
```

#### Version Incompatibility
```bash
# Check why dependency is needed
go mod why github.com/some/library

# View dependency graph
go mod graph
```

#### Build Issues
```bash
# Verbose build output
go build -v

# Check build constraints
go list -f '{{.GoFiles}}' .
```

## Best Practices

### Dependency Management
- **Minimal Dependencies**: Only add necessary libraries
- **Regular Updates**: Keep dependencies updated
- **Security Scanning**: Scan for vulnerabilities
- **License Compliance**: Check license compatibility

### Code Organization
- **Separation**: Keep library-specific code isolated
- **Interfaces**: Use interfaces for testability
- **Configuration**: Centralized library configuration
- **Error Handling**: Consistent error handling patterns

### Testing
- **Mock External**: Mock external library calls
- **Integration Tests**: Test library integrations
- **Performance Tests**: Benchmark critical paths
- **Security Tests**: Test security-related libraries

## Tài liệu liên quan

- [Local Testing](./local-testing.md)
- [Performance Optimization](../best-practices/performance.md)
- [Security Guidelines](../best-practices/security.md)
- [Database Configuration](../database/configuration.md)
- [API Documentation](../api/overview.md)