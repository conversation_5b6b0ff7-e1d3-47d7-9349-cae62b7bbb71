# Testing Documentation - Blog API v3

## Tổng quan

Tài liệu này mô tả chiến lược testing toàn diện cho Blog API v3, bao gồm unit tests, integration tests, end-to-end tests, performance tests, và security tests. Hệ thống testing đượ<PERSON> thiết kế để đảm bảo chất lượng code, reliability, và maintainability.

## Chiến lược Testing

### Testing Pyramid

```mermaid
graph TD
    A[E2E Tests] --> B[Integration Tests]
    B --> C[Unit Tests]
    
    style A fill:#ff6b6b
    style B fill:#ffd93d
    style C fill:#6bcf7f
    
    A1[API Tests<br/>UI Tests<br/>User Scenarios] --> A
    B1[Service Integration<br/>Database Tests<br/>External APIs] --> B
    C1[Functions<br/>Methods<br/>Components] --> C
```

### Test Coverage Goals

- **Unit Tests**: 90%+ code coverage
- **Integration Tests**: 80%+ critical paths
- **E2E Tests**: 70%+ user journeys
- **Performance Tests**: 100% critical endpoints
- **Security Tests**: 100% authentication/authorization flows

## Testing Framework & Tools

### Core Testing Stack

```go
// Testing dependencies
import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/suite"
    "github.com/go-playground/validator/v10"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
)

// Database testing
import (
    "github.com/DATA-DOG/go-sqlmock"
    "github.com/testcontainers/testcontainers-go"
    "github.com/testcontainers/testcontainers-go/modules/postgres"
)

// HTTP testing
import (
    "net/http/httptest"
    "github.com/gin-gonic/gin"
)

// Mock generation
//go:generate mockgen -source=interface.go -destination=mocks/mock.go
```

### Test Structure

> **📖 Chi tiết đầy đủ**: Xem [Project Structure - Tests](../architecture/project-structure.md#tests-structure) để biết cấu trúc test chi tiết.

**Tóm tắt cấu trúc test**:
```
tests/
├── unit/                   # Unit tests by module
│   ├── auth/
│   ├── blog/
│   └── utils/
├── integration/            # Integration tests
│   ├── auth/
│   ├── blog/
│   └── api/
├── e2e/                    # End-to-end tests
├── fixtures/              # Test data
├── mocks/                 # Generated mocks
└── helpers/               # Test utilities
```

**Key Points**:
- Tests được tổ chức theo module structure
- Mỗi module có unit, integration, và e2e tests riêng
- Shared fixtures và helpers cho reusability

## Unit Testing

### Service Layer Testing

```go
// services/user_service_test.go
package services

import (
    "context"
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "blog-api/internal/models"
    "blog-api/internal/repositories/mocks"
)

type UserServiceTestSuite struct {
    suite.Suite
    mockUserRepo    *mocks.MockUserRepository
    mockCacheRepo   *mocks.MockCacheRepository
    userService     *UserService
}

func (suite *UserServiceTestSuite) SetupTest() {
    suite.mockUserRepo = new(mocks.MockUserRepository)
    suite.mockCacheRepo = new(mocks.MockCacheRepository)
    suite.userService = NewUserService(suite.mockUserRepo, suite.mockCacheRepo)
}

func (suite *UserServiceTestSuite) TestCreateUser_Success() {
    // Given
    websiteID := uint(1)
    userRequest := &CreateUserRequest{
        Name:     "John Doe",
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    expectedUser := &models.User{
        ID:        1,
        WebsiteID: websiteID,
        Name:      userRequest.Name,
        Email:     userRequest.Email,
        Status:    "active",
    }
    
    suite.mockUserRepo.On("GetByEmail", websiteID, userRequest.Email).Return(nil, gorm.ErrRecordNotFound)
    suite.mockUserRepo.On("Create", mock.AnythingOfType("*models.User")).Return(expectedUser, nil)
    suite.mockCacheRepo.On("Delete", mock.AnythingOfType("string")).Return(nil)
    
    // When
    result, err := suite.userService.CreateUser(context.Background(), websiteID, userRequest)
    
    // Then
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), expectedUser.Name, result.Name)
    assert.Equal(suite.T(), expectedUser.Email, result.Email)
    
    suite.mockUserRepo.AssertExpectations(suite.T())
    suite.mockCacheRepo.AssertExpectations(suite.T())
}

func (suite *UserServiceTestSuite) TestCreateUser_EmailExists() {
    // Given
    websiteID := uint(1)
    userRequest := &CreateUserRequest{
        Name:     "John Doe",
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    existingUser := &models.User{
        ID:        1,
        WebsiteID: websiteID,
        Email:     userRequest.Email,
    }
    
    suite.mockUserRepo.On("GetByEmail", websiteID, userRequest.Email).Return(existingUser, nil)
    
    // When
    result, err := suite.userService.CreateUser(context.Background(), websiteID, userRequest)
    
    // Then
    assert.Error(suite.T(), err)
    assert.Nil(suite.T(), result)
    assert.Contains(suite.T(), err.Error(), "email already exists")
    
    suite.mockUserRepo.AssertExpectations(suite.T())
}

func TestUserServiceTestSuite(t *testing.T) {
    suite.Run(t, new(UserServiceTestSuite))
}
```

### Repository Layer Testing

```go
// repositories/user_repository_test.go
package repositories

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
    "blog-api/internal/models"
)

type UserRepositoryTestSuite struct {
    suite.Suite
    db   *gorm.DB
    repo *UserRepository
}

func (suite *UserRepositoryTestSuite) SetupSuite() {
    // Setup in-memory SQLite database
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    assert.NoError(suite.T(), err)
    
    // Auto-migrate models
    err = db.AutoMigrate(&models.User{}, &models.Website{})
    assert.NoError(suite.T(), err)
    
    suite.db = db
    suite.repo = NewUserRepository(db)
}

func (suite *UserRepositoryTestSuite) SetupTest() {
    // Clean database before each test
    suite.db.Exec("DELETE FROM users")
    suite.db.Exec("DELETE FROM websites")
    
    // Create test website
    website := &models.Website{
        ID:     1,
        Name:   "Test Website",
        Domain: "test.com",
    }
    suite.db.Create(website)
}

func (suite *UserRepositoryTestSuite) TestCreate_Success() {
    // Given
    user := &models.User{
        WebsiteID: 1,
        Name:      "John Doe",
        Email:     "<EMAIL>",
        Password:  "hashed_password",
    }
    
    // When
    result, err := suite.repo.Create(user)
    
    // Then
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.NotZero(suite.T(), result.ID)
    assert.Equal(suite.T(), user.Name, result.Name)
    assert.Equal(suite.T(), user.Email, result.Email)
}

func (suite *UserRepositoryTestSuite) TestGetByEmail_Success() {
    // Given
    user := &models.User{
        WebsiteID: 1,
        Name:      "John Doe",
        Email:     "<EMAIL>",
        Password:  "hashed_password",
    }
    suite.db.Create(user)
    
    // When
    result, err := suite.repo.GetByEmail(1, "<EMAIL>")
    
    // Then
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), user.Email, result.Email)
}

func (suite *UserRepositoryTestSuite) TestGetByEmail_NotFound() {
    // When
    result, err := suite.repo.GetByEmail(1, "<EMAIL>")
    
    // Then
    assert.Error(suite.T(), err)
    assert.Nil(suite.T(), result)
    assert.Equal(suite.T(), gorm.ErrRecordNotFound, err)
}

func (suite *UserRepositoryTestSuite) TestTenantIsolation() {
    // Given - Create users for different websites
    user1 := &models.User{
        WebsiteID: 1,
        Email:     "<EMAIL>",
        Name:      "User 1",
    }
    user2 := &models.User{
        WebsiteID: 2,
        Email:     "<EMAIL>",
        Name:      "User 2",
    }
    
    suite.db.Create(&models.Website{ID: 2, Name: "Website 2"})
    suite.db.Create(user1)
    suite.db.Create(user2)
    
    // When - Query users for website 1
    users, err := suite.repo.GetByWebsite(1, "", 10)
    
    // Then - Should only return users from website 1
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), users, 1)
    assert.Equal(suite.T(), uint(1), users[0].WebsiteID)
    assert.Equal(suite.T(), "<EMAIL>", users[0].Email)
}

func TestUserRepositoryTestSuite(t *testing.T) {
    suite.Run(t, new(UserRepositoryTestSuite))
}
```

### Handler Layer Testing

```go
// handlers/user_handler_test.go
package handlers

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "blog-api/internal/services/mocks"
)

type UserHandlerTestSuite struct {
    suite.Suite
    mockUserService *mocks.MockUserService
    handler         *UserHandler
    router          *gin.Engine
}

func (suite *UserHandlerTestSuite) SetupTest() {
    gin.SetMode(gin.TestMode)
    
    suite.mockUserService = new(mocks.MockUserService)
    suite.handler = NewUserHandler(suite.mockUserService)
    
    suite.router = gin.New()
    suite.router.POST("/users", suite.handler.CreateUser)
    suite.router.GET("/users/:id", suite.handler.GetUser)
}

func (suite *UserHandlerTestSuite) TestCreateUser_Success() {
    // Given
    websiteID := uint(1)
    request := CreateUserRequest{
        Name:     "John Doe",
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    expectedUser := &UserResponse{
        ID:    1,
        Name:  request.Name,
        Email: request.Email,
    }
    
    suite.mockUserService.On("CreateUser", mock.Anything, websiteID, mock.AnythingOfType("*CreateUserRequest")).
        Return(expectedUser, nil)
    
    // When
    jsonData, _ := json.Marshal(request)
    req, _ := http.NewRequest("POST", "/users", bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Website-ID", "1")
    
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)
    
    // Then
    assert.Equal(suite.T(), http.StatusCreated, w.Code)
    
    var response APIResponse
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    assert.True(suite.T(), response.Success)
    assert.Equal(suite.T(), "User created successfully", response.Message)
    
    suite.mockUserService.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestCreateUser_ValidationError() {
    // Given
    request := CreateUserRequest{
        Name:     "", // Empty name should fail validation
        Email:    "invalid-email",
        Password: "123", // Too short password
    }
    
    // When
    jsonData, _ := json.Marshal(request)
    req, _ := http.NewRequest("POST", "/users", bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Website-ID", "1")
    
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)
    
    // Then
    assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
    
    var response APIResponse
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    assert.False(suite.T(), response.Success)
    assert.Contains(suite.T(), response.Message, "validation failed")
}

func TestUserHandlerTestSuite(t *testing.T) {
    suite.Run(t, new(UserHandlerTestSuite))
}
```

## Integration Testing

### API Integration Tests

```go
// integration/api/user_api_test.go
package api

import (
    "bytes"
    "encoding/json"
    "net/http"
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "blog-api/internal/models"
    "blog-api/tests/testhelpers"
)

type UserAPITestSuite struct {
    suite.Suite
    testServer *testhelpers.TestServer
    website    *models.Website
}

func (suite *UserAPITestSuite) SetupSuite() {
    suite.testServer = testhelpers.NewTestServer()
    suite.website = suite.testServer.CreateTestWebsite("Test Website")
}

func (suite *UserAPITestSuite) TearDownSuite() {
    suite.testServer.Close()
}

func (suite *UserAPITestSuite) SetupTest() {
    suite.testServer.CleanDatabase()
}

func (suite *UserAPITestSuite) TestCreateUser_EndToEnd() {
    // Given
    request := map[string]interface{}{
        "name":     "John Doe",
        "email":    "<EMAIL>",
        "password": "password123",
    }
    
    // When
    response := suite.testServer.POST("/api/cms/v1/users", request, suite.website.ID)
    
    // Then
    assert.Equal(suite.T(), http.StatusCreated, response.Code)
    
    var apiResponse testhelpers.APIResponse
    err := json.Unmarshal(response.Body.Bytes(), &apiResponse)
    assert.NoError(suite.T(), err)
    assert.True(suite.T(), apiResponse.Success)
    
    // Verify user was created in database
    var user models.User
    err = suite.testServer.DB.Where("email = ? AND website_id = ?", "<EMAIL>", suite.website.ID).First(&user).Error
    assert.NoError(suite.T(), err)
    assert.Equal(suite.T(), "John Doe", user.Name)
}

func (suite *UserAPITestSuite) TestUserCRUD_Complete() {
    // Create user
    createRequest := map[string]interface{}{
        "name":     "Jane Doe",
        "email":    "<EMAIL>",
        "password": "password123",
    }
    
    createResponse := suite.testServer.POST("/api/cms/v1/users", createRequest, suite.website.ID)
    assert.Equal(suite.T(), http.StatusCreated, createResponse.Code)
    
    // Extract user ID from response
    var createApiResponse testhelpers.APIResponse
    json.Unmarshal(createResponse.Body.Bytes(), &createApiResponse)
    userID := createApiResponse.Data.(map[string]interface{})["id"]
    
    // Get user
    getResponse := suite.testServer.GET(fmt.Sprintf("/api/cms/v1/users/%v", userID), suite.website.ID)
    assert.Equal(suite.T(), http.StatusOK, getResponse.Code)
    
    // Update user
    updateRequest := map[string]interface{}{
        "name": "Jane Smith",
    }
    
    updateResponse := suite.testServer.PUT(fmt.Sprintf("/api/cms/v1/users/%v", userID), updateRequest, suite.website.ID)
    assert.Equal(suite.T(), http.StatusOK, updateResponse.Code)
    
    // Delete user
    deleteResponse := suite.testServer.DELETE(fmt.Sprintf("/api/cms/v1/users/%v", userID), suite.website.ID)
    assert.Equal(suite.T(), http.StatusOK, deleteResponse.Code)
    
    // Verify user is deleted
    getAfterDeleteResponse := suite.testServer.GET(fmt.Sprintf("/api/cms/v1/users/%v", userID), suite.website.ID)
    assert.Equal(suite.T(), http.StatusNotFound, getAfterDeleteResponse.Code)
}

func TestUserAPITestSuite(t *testing.T) {
    suite.Run(t, new(UserAPITestSuite))
}
```

### Database Integration Tests

```go
// integration/database/user_db_test.go
package database

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "github.com/testcontainers/testcontainers-go"
    "github.com/testcontainers/testcontainers-go/modules/postgres"
    "blog-api/internal/models"
    "blog-api/tests/testhelpers"
)

type UserDBTestSuite struct {
    suite.Suite
    container *postgres.PostgresContainer
    db        *gorm.DB
}

func (suite *UserDBTestSuite) SetupSuite() {
    ctx := context.Background()
    
    // Start PostgreSQL container
    container, err := postgres.RunContainer(ctx,
        testcontainers.WithImage("postgres:13"),
        postgres.WithDatabase("testdb"),
        postgres.WithUsername("testuser"),
        postgres.WithPassword("testpass"),
    )
    assert.NoError(suite.T(), err)
    
    suite.container = container
    
    // Connect to database
    connectionString, err := container.ConnectionString(ctx, "sslmode=disable")
    assert.NoError(suite.T(), err)
    
    suite.db = testhelpers.SetupTestDB(connectionString)
}

func (suite *UserDBTestSuite) TearDownSuite() {
    suite.container.Terminate(context.Background())
}

func (suite *UserDBTestSuite) TestUserConstraints() {
    // Test unique constraint on email per website
    website1 := &models.Website{Name: "Website 1", Domain: "site1.com"}
    website2 := &models.Website{Name: "Website 2", Domain: "site2.com"}
    
    suite.db.Create(website1)
    suite.db.Create(website2)
    
    // Create user in website 1
    user1 := &models.User{
        WebsiteID: website1.ID,
        Email:     "<EMAIL>",
        Name:      "User 1",
    }
    err := suite.db.Create(user1).Error
    assert.NoError(suite.T(), err)
    
    // Try to create user with same email in website 1 (should fail)
    user2 := &models.User{
        WebsiteID: website1.ID,
        Email:     "<EMAIL>",
        Name:      "User 2",
    }
    err = suite.db.Create(user2).Error
    assert.Error(suite.T(), err)
    assert.Contains(suite.T(), err.Error(), "duplicate key")
    
    // Create user with same email in website 2 (should succeed)
    user3 := &models.User{
        WebsiteID: website2.ID,
        Email:     "<EMAIL>",
        Name:      "User 3",
    }
    err = suite.db.Create(user3).Error
    assert.NoError(suite.T(), err)
}

func TestUserDBTestSuite(t *testing.T) {
    suite.Run(t, new(UserDBTestSuite))
}
```

## End-to-End Testing

### User Journey Tests

```go
// e2e/scenarios/user_journey_test.go
package scenarios

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "blog-api/tests/testhelpers"
)

type UserJourneyTestSuite struct {
    suite.Suite
    testServer *testhelpers.TestServer
    website    *models.Website
}

func (suite *UserJourneyTestSuite) SetupSuite() {
    suite.testServer = testhelpers.NewTestServer()
    suite.website = suite.testServer.CreateTestWebsite("Blog Website")
}

func (suite *UserJourneyTestSuite) TestCompleteUserJourney() {
    // 1. User Registration
    registerRequest := map[string]interface{}{
        "name":     "John Blogger",
        "email":    "<EMAIL>",
        "password": "securepassword123",
    }
    
    registerResponse := suite.testServer.POST("/api/cms/v1/auth/register", registerRequest, suite.website.ID)
    assert.Equal(suite.T(), http.StatusCreated, registerResponse.Code)
    
    // Extract tokens
    var registerApiResponse testhelpers.APIResponse
    json.Unmarshal(registerResponse.Body.Bytes(), &registerApiResponse)
    tokens := registerApiResponse.Data.(map[string]interface{})["tokens"].(map[string]interface{})
    accessToken := tokens["access_token"].(string)
    
    // 2. User Profile Update
    profileRequest := map[string]interface{}{
        "bio":      "I'm a passionate blogger",
        "website":  "https://johnblogger.com",
        "location": "San Francisco, CA",
    }
    
    profileResponse := suite.testServer.PUTWithAuth("/api/cms/v1/auth/profile", profileRequest, suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusOK, profileResponse.Code)
    
    // 3. Create Blog Post
    postRequest := map[string]interface{}{
        "title":   "My First Blog Post",
        "content": "This is my first blog post content...",
        "status":  "published",
        "tags":    []string{"first-post", "introduction"},
    }
    
    postResponse := suite.testServer.POSTWithAuth("/api/cms/v1/posts", postRequest, suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusCreated, postResponse.Code)
    
    // 4. Get User's Posts
    postsResponse := suite.testServer.GETWithAuth("/api/cms/v1/posts?author=me", suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusOK, postsResponse.Code)
    
    var postsApiResponse testhelpers.APIResponse
    json.Unmarshal(postsResponse.Body.Bytes(), &postsApiResponse)
    posts := postsApiResponse.Data.([]interface{})
    assert.Len(suite.T(), posts, 1)
    
    // 5. Update Post
    postID := posts[0].(map[string]interface{})["id"]
    updatePostRequest := map[string]interface{}{
        "title":   "My Updated First Blog Post",
        "content": "This is my updated first blog post content...",
    }
    
    updatePostResponse := suite.testServer.PUTWithAuth(fmt.Sprintf("/api/cms/v1/posts/%v", postID), updatePostRequest, suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusOK, updatePostResponse.Code)
    
    // 6. User Logout
    logoutResponse := suite.testServer.POSTWithAuth("/api/cms/v1/auth/logout", nil, suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusOK, logoutResponse.Code)
    
    // 7. Verify token is invalidated
    profileAfterLogoutResponse := suite.testServer.GETWithAuth("/api/cms/v1/auth/profile", suite.website.ID, accessToken)
    assert.Equal(suite.T(), http.StatusUnauthorized, profileAfterLogoutResponse.Code)
}

func TestUserJourneyTestSuite(t *testing.T) {
    suite.Run(t, new(UserJourneyTestSuite))
}
```

## Performance Testing

### Load Testing

```go
// performance/load/user_load_test.go
package load

import (
    "context"
    "fmt"
    "sync"
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
    "blog-api/tests/testhelpers"
)

func TestUserAPILoad(t *testing.T) {
    testServer := testhelpers.NewTestServer()
    defer testServer.Close()
    
    website := testServer.CreateTestWebsite("Load Test Website")
    
    // Test parameters
    concurrency := 100
    requestsPerWorker := 100
    totalRequests := concurrency * requestsPerWorker
    
    // Metrics
    var (
        successCount int64
        errorCount   int64
        totalLatency time.Duration
        mu           sync.Mutex
    )
    
    // Worker function
    worker := func(workerID int) {
        for i := 0; i < requestsPerWorker; i++ {
            start := time.Now()
            
            request := map[string]interface{}{
                "name":     fmt.Sprintf("User %d-%d", workerID, i),
                "email":    fmt.Sprintf("<EMAIL>", workerID, i),
                "password": "password123",
            }
            
            response := testServer.POST("/api/cms/v1/users", request, website.ID)
            latency := time.Since(start)
            
            mu.Lock()
            if response.Code == 201 {
                successCount++
            } else {
                errorCount++
            }
            totalLatency += latency
            mu.Unlock()
        }
    }
    
    // Start load test
    startTime := time.Now()
    var wg sync.WaitGroup
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            worker(workerID)
        }(i)
    }
    
    wg.Wait()
    duration := time.Since(startTime)
    
    // Calculate metrics
    successRate := float64(successCount) / float64(totalRequests) * 100
    avgLatency := totalLatency / time.Duration(totalRequests)
    throughput := float64(totalRequests) / duration.Seconds()
    
    // Assertions
    assert.GreaterOrEqual(t, successRate, 95.0, "Success rate should be at least 95%")
    assert.LessOrEqual(t, avgLatency, 100*time.Millisecond, "Average latency should be under 100ms")
    assert.GreaterOrEqual(t, throughput, 1000.0, "Throughput should be at least 1000 req/sec")
    
    // Log results
    t.Logf("Load Test Results:")
    t.Logf("- Total Requests: %d", totalRequests)
    t.Logf("- Success Rate: %.2f%%", successRate)
    t.Logf("- Average Latency: %v", avgLatency)
    t.Logf("- Throughput: %.2f req/sec", throughput)
    t.Logf("- Duration: %v", duration)
}
```

### Stress Testing

```go
// performance/stress/database_stress_test.go
package stress

import (
    "context"
    "sync"
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
    "blog-api/internal/models"
    "blog-api/tests/testhelpers"
)

func TestDatabaseConnectionStress(t *testing.T) {
    testServer := testhelpers.NewTestServer()
    defer testServer.Close()
    
    website := testServer.CreateTestWebsite("Stress Test Website")
    
    // Stress test parameters
    concurrency := 200
    duration := 30 * time.Second
    
    var (
        operationCount int64
        errorCount     int64
        mu             sync.Mutex
    )
    
    ctx, cancel := context.WithTimeout(context.Background(), duration)
    defer cancel()
    
    worker := func(workerID int) {
        for {
            select {
            case <-ctx.Done():
                return
            default:
                // Perform database operations
                user := &models.User{
                    WebsiteID: website.ID,
                    Name:      fmt.Sprintf("Stress User %d", workerID),
                    Email:     fmt.Sprintf("<EMAIL>", workerID, time.Now().UnixNano()),
                    Password:  "password123",
                }
                
                err := testServer.DB.Create(user).Error
                
                mu.Lock()
                if err != nil {
                    errorCount++
                } else {
                    operationCount++
                }
                mu.Unlock()
                
                time.Sleep(10 * time.Millisecond)
            }
        }
    }
    
    // Start stress test
    var wg sync.WaitGroup
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            worker(workerID)
        }(i)
    }
    
    wg.Wait()
    
    // Calculate metrics
    errorRate := float64(errorCount) / float64(operationCount) * 100
    opsPerSecond := float64(operationCount) / duration.Seconds()
    
    // Assertions
    assert.LessOrEqual(t, errorRate, 1.0, "Error rate should be under 1%")
    assert.GreaterOrEqual(t, opsPerSecond, 100.0, "Should handle at least 100 ops/sec")
    
    t.Logf("Stress Test Results:")
    t.Logf("- Total Operations: %d", operationCount)
    t.Logf("- Error Rate: %.2f%%", errorRate)
    t.Logf("- Operations/Second: %.2f", opsPerSecond)
}
```

## Security Testing

### Authentication Tests

```go
// security/auth/auth_security_test.go
package auth

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "blog-api/tests/testhelpers"
)

type AuthSecurityTestSuite struct {
    suite.Suite
    testServer *testhelpers.TestServer
    website    *models.Website
}

func (suite *AuthSecurityTestSuite) SetupSuite() {
    suite.testServer = testhelpers.NewTestServer()
    suite.website = suite.testServer.CreateTestWebsite("Security Test Website")
}

func (suite *AuthSecurityTestSuite) TestPasswordSecurity() {
    // Test weak password rejection
    weakPasswords := []string{
        "123456",
        "password",
        "abc123",
        "12345678",
        "qwerty",
    }
    
    for _, password := range weakPasswords {
        request := map[string]interface{}{
            "name":     "Test User",
            "email":    "<EMAIL>",
            "password": password,
        }
        
        response := suite.testServer.POST("/api/cms/v1/auth/register", request, suite.website.ID)
        assert.Equal(suite.T(), http.StatusBadRequest, response.Code, "Weak password should be rejected: %s", password)
    }
}

func (suite *AuthSecurityTestSuite) TestBruteForceProtection() {
    // Create a test user first
    user := suite.testServer.CreateTestUser("<EMAIL>", "password123", suite.website.ID)
    
    // Attempt multiple failed logins
    for i := 0; i < 10; i++ {
        request := map[string]interface{}{
            "email":    user.Email,
            "password": "wrongpassword",
        }
        
        response := suite.testServer.POST("/api/cms/v1/auth/login", request, suite.website.ID)
        
        if i < 5 {
            assert.Equal(suite.T(), http.StatusUnauthorized, response.Code)
        } else {
            // After 5 failed attempts, should be rate limited
            assert.Equal(suite.T(), http.StatusTooManyRequests, response.Code)
        }
    }
}

func (suite *AuthSecurityTestSuite) TestJWTSecurity() {
    // Create user and get token
    user := suite.testServer.CreateTestUser("<EMAIL>", "password123", suite.website.ID)
    token := suite.testServer.LoginUser(user.Email, "password123", suite.website.ID)
    
    // Test token tampering
    tamperedToken := token[:len(token)-5] + "XXXXX"
    response := suite.testServer.GETWithAuth("/api/cms/v1/auth/profile", suite.website.ID, tamperedToken)
    assert.Equal(suite.T(), http.StatusUnauthorized, response.Code)
    
    // Test expired token (mock expiration)
    expiredToken := suite.testServer.CreateExpiredToken(user.ID)
    response = suite.testServer.GETWithAuth("/api/cms/v1/auth/profile", suite.website.ID, expiredToken)
    assert.Equal(suite.T(), http.StatusUnauthorized, response.Code)
}

func TestAuthSecurityTestSuite(t *testing.T) {
    suite.Run(t, new(AuthSecurityTestSuite))
}
```

### SQL Injection Tests

```go
// security/vulnerability/sql_injection_test.go
package vulnerability

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "blog-api/tests/testhelpers"
)

func TestSQLInjectionProtection(t *testing.T) {
    testServer := testhelpers.NewTestServer()
    defer testServer.Close()
    
    website := testServer.CreateTestWebsite("SQL Injection Test")
    
    // SQL injection payloads
    sqlInjectionPayloads := []string{
        "' OR '1'='1",
        "'; DROP TABLE users; --",
        "' UNION SELECT * FROM users --",
        "admin'--",
        "' OR 1=1 --",
        "'; INSERT INTO users (email) VALUES ('<EMAIL>'); --",
    }
    
    for _, payload := range sqlInjectionPayloads {
        // Test in email field
        request := map[string]interface{}{
            "email":    payload,
            "password": "password123",
        }
        
        response := testServer.POST("/api/cms/v1/auth/login", request, website.ID)
        
        // Should not return 500 (SQL error) or 200 (successful injection)
        assert.NotEqual(t, http.StatusInternalServerError, response.Code, "SQL injection payload caused server error: %s", payload)
        assert.NotEqual(t, http.StatusOK, response.Code, "SQL injection payload was successful: %s", payload)
        
        // Test in search queries
        searchURL := fmt.Sprintf("/api/cms/v1/users?search=%s", url.QueryEscape(payload))
        searchResponse := testServer.GET(searchURL, website.ID)
        assert.NotEqual(t, http.StatusInternalServerError, searchResponse.Code, "SQL injection in search caused server error: %s", payload)
    }
}
```

## Test Helpers

### Test Server Helper

```go
// testhelpers/test_server.go
package testhelpers

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "net/http/httptest"
    "github.com/gin-gonic/gin"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
    "blog-api/internal/models"
    "blog-api/internal/routes"
)

type TestServer struct {
    DB     *gorm.DB
    Router *gin.Engine
    Server *httptest.Server
}

type APIResponse struct {
    Success bool        `json:"success"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func NewTestServer() *TestServer {
    gin.SetMode(gin.TestMode)
    
    // Setup test database
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    if err != nil {
        panic("Failed to connect to test database")
    }
    
    // Auto-migrate models
    db.AutoMigrate(&models.Website{}, &models.User{}, &models.Post{}, &models.Comment{})
    
    // Setup router
    router := gin.New()
    routes.SetupRoutes(router, db)
    
    // Create test server
    server := httptest.NewServer(router)
    
    return &TestServer{
        DB:     db,
        Router: router,
        Server: server,
    }
}

func (ts *TestServer) Close() {
    ts.Server.Close()
}

func (ts *TestServer) CleanDatabase() {
    ts.DB.Exec("DELETE FROM users")
    ts.DB.Exec("DELETE FROM posts")
    ts.DB.Exec("DELETE FROM comments")
}

func (ts *TestServer) CreateTestWebsite(name string) *models.Website {
    website := &models.Website{
        Name:   name,
        Domain: fmt.Sprintf("%s.com", strings.ToLower(name)),
    }
    ts.DB.Create(website)
    return website
}

func (ts *TestServer) CreateTestUser(email, password string, websiteID uint) *models.User {
    user := &models.User{
        WebsiteID: websiteID,
        Name:      "Test User",
        Email:     email,
        Password:  hashPassword(password),
    }
    ts.DB.Create(user)
    return user
}

func (ts *TestServer) POST(path string, data interface{}, websiteID uint) *httptest.ResponseRecorder {
    jsonData, _ := json.Marshal(data)
    req, _ := http.NewRequest("POST", ts.Server.URL+path, bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Website-ID", fmt.Sprintf("%d", websiteID))
    
    w := httptest.NewRecorder()
    ts.Router.ServeHTTP(w, req)
    return w
}

func (ts *TestServer) GET(path string, websiteID uint) *httptest.ResponseRecorder {
    req, _ := http.NewRequest("GET", ts.Server.URL+path, nil)
    req.Header.Set("X-Website-ID", fmt.Sprintf("%d", websiteID))
    
    w := httptest.NewRecorder()
    ts.Router.ServeHTTP(w, req)
    return w
}

func (ts *TestServer) POSTWithAuth(path string, data interface{}, websiteID uint, token string) *httptest.ResponseRecorder {
    jsonData, _ := json.Marshal(data)
    req, _ := http.NewRequest("POST", ts.Server.URL+path, bytes.NewBuffer(jsonData))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Website-ID", fmt.Sprintf("%d", websiteID))
    req.Header.Set("Authorization", "Bearer "+token)
    
    w := httptest.NewRecorder()
    ts.Router.ServeHTTP(w, req)
    return w
}

func (ts *TestServer) GETWithAuth(path string, websiteID uint, token string) *httptest.ResponseRecorder {
    req, _ := http.NewRequest("GET", ts.Server.URL+path, nil)
    req.Header.Set("X-Website-ID", fmt.Sprintf("%d", websiteID))
    req.Header.Set("Authorization", "Bearer "+token)
    
    w := httptest.NewRecorder()
    ts.Router.ServeHTTP(w, req)
    return w
}
```

## Testing Configuration

### Test Configuration

```yaml
# config/test.yaml
database:
  driver: "sqlite"
  dsn: ":memory:"
  
redis:
  address: "localhost:6379"
  password: ""
  db: 1
  
jwt:
  secret: "test-secret"
  access_token_ttl: "1h"
  refresh_token_ttl: "24h"
  
logging:
  level: "error"
  format: "json"
  
testing:
  cleanup_after_test: true
  parallel_tests: false
  test_timeout: "30s"
```

### Makefile for Testing

```makefile
# Test commands
.PHONY: test test-unit test-integration test-e2e test-performance test-security test-coverage

test: test-unit test-integration

test-unit:
	go test -v ./tests/unit/... -timeout=30s

test-integration:
	go test -v ./tests/integration/... -timeout=60s

test-e2e:
	go test -v ./tests/e2e/... -timeout=120s

test-performance:
	go test -v ./tests/performance/... -timeout=300s

test-security:
	go test -v ./tests/security/... -timeout=60s

test-coverage:
	go test -v ./... -coverprofile=coverage.out -covermode=atomic
	go tool cover -html=coverage.out -o coverage.html

test-race:
	go test -race -v ./...

test-bench:
	go test -bench=. -benchmem ./...

# Test database setup
test-db-setup:
	docker run -d --name blog-test-db -p 5433:5432 -e POSTGRES_DB=blog_test -e POSTGRES_USER=test -e POSTGRES_PASSWORD=test postgres:13

test-db-teardown:
	docker stop blog-test-db && docker rm blog-test-db

# Mock generation
generate-mocks:
	go generate ./...

# Lint tests
lint-tests:
	golangci-lint run ./tests/...
```

## Best Practices

### Testing Best Practices

1. **Test Isolation**: Each test should be independent
2. **Test Data**: Use factories and fixtures for test data
3. **Mock External Dependencies**: Mock external services and APIs
4. **Clear Test Names**: Use descriptive test names
5. **Test Coverage**: Aim for high test coverage
6. **Performance Testing**: Include performance tests for critical paths
7. **Security Testing**: Test for common vulnerabilities
8. **Cleanup**: Always cleanup test data and resources

### Code Quality

```go
// Good test structure
func TestUserService_CreateUser_Success(t *testing.T) {
    // Given (Setup)
    // When (Action)
    // Then (Assertion)
}

// Use table-driven tests for multiple scenarios
func TestUserValidation(t *testing.T) {
    tests := []struct {
        name        string
        user        User
        expectError bool
        errorMsg    string
    }{
        {
            name:        "valid user",
            user:        User{Name: "John", Email: "<EMAIL>"},
            expectError: false,
        },
        {
            name:        "invalid email",
            user:        User{Name: "John", Email: "invalid-email"},
            expectError: true,
            errorMsg:    "invalid email format",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := validateUser(tt.user)
            if tt.expectError {
                assert.Error(t, err)
                assert.Contains(t, err.Error(), tt.errorMsg)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_DB: blog_test
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
    
    - name: Install dependencies
      run: go mod download
    
    - name: Generate mocks
      run: make generate-mocks
    
    - name: Run unit tests
      run: make test-unit
    
    - name: Run integration tests
      run: make test-integration
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_USER: test
        DB_PASSWORD: test
        DB_NAME: blog_test
        REDIS_URL: redis://localhost:6379
    
    - name: Run security tests
      run: make test-security
    
    - name: Generate coverage report
      run: make test-coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
```

## Tài liệu liên quan

- [API Documentation](../api/overview.md)
- [Development Setup](./setup.md)
- [Security Best Practices](../best-practices/security.md)
- [Performance Guidelines](../best-practices/performance.md)
- [CI/CD Pipeline](../deployment/ci-cd.md)