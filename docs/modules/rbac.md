# Module RBAC - Tài liệu Tiếng Việt

## Tổng quan

Module RBAC (Role-Based Access Control) cung cấp hệ thống phân quyền linh hoạt và mạnh mẽ cho ứng dụng blog, cho phép quản lý vai trò, quyền hạn và kiểm soát truy cập chi tiết đến từng tài nguyên.

## Mục tiêu

- **Flexible Permission System**: Hệ thống phân quyền linh hoạt
- **Role Management**: Quản lý vai trò người dùng
- **Resource-based Access**: Kiểm soát truy cập theo tài nguyên
- **Hierarchical Roles**: <PERSON>ai trò có thứ bậc
- **Dynamic Permissions**: Quyền động theo context
- **Audit Trail**: <PERSON> dõ<PERSON> lịch sử truy cập

## T<PERSON>h năng chính

- **Role Definition**: <PERSON><PERSON><PERSON> nghĩa vai trò tùy chỉnh
- **Permission Assignment**: <PERSON><PERSON> quyền cho vai trò
- **User Role Mapping**: <PERSON><PERSON> vai trò cho người dùng
- **Resource Protection**: Bảo vệ tài nguyên API
- **Permission Inheritance**: Kế thừa quyền từ vai trò cha
- **Conditional Access**: Truy cập có điều kiện
- **Temporary Permissions**: Quyền tạm thời
- **Permission Caching**: Cache quyền để tối ưu performance

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/rbac/
├── models/                   # Các model RBAC
│   ├── role.go              # Model vai trò
│   ├── permission.go        # Model quyền
│   ├── resource.go          # Model tài nguyên
│   ├── policy.go            # Model chính sách
│   └── assignment.go        # Model phân quyền
├── services/                # Logic nghiệp vụ
│   ├── role_service.go      # Dịch vụ vai trò
│   ├── permission_service.go # Dịch vụ quyền
│   ├── policy_service.go    # Dịch vụ chính sách
│   ├── enforcement_service.go # Dịch vụ thực thi
│   └── cache_service.go     # Dịch vụ cache quyền
├── handlers/                # HTTP handlers
├── repositories/            # Truy cập dữ liệu
├── middleware/              # RBAC middleware
│   ├── permission_middleware.go # Kiểm tra quyền
│   └── role_middleware.go   # Kiểm tra vai trò
├── policies/                # Policy definitions
│   ├── blog_policies.go     # Chính sách blog
│   ├── user_policies.go     # Chính sách user
│   └── admin_policies.go    # Chính sách admin
└── utils/                   # Utilities
    ├── permission_checker.go # Kiểm tra quyền
    └── role_hierarchy.go    # Thứ bậc vai trò
```

## Mô hình dữ liệu

### Role (Vai trò)
- **ID**: Định danh vai trò
- **TenantID**: ID tenant
- **WebsiteID**: ID website (cho website-specific roles)
- **Name**: Tên vai trò
- **DisplayName**: Tên hiển thị
- **Description**: Mô tả vai trò
- **Level**: Cấp độ vai trò (hierarchy)
- **ParentID**: ID vai trò cha
- **IsSystem**: Vai trò hệ thống hay custom
- **Status**: Trạng thái (active, inactive)

### Permission (Quyền)
- **ID**: Định danh quyền
- **Name**: Tên quyền
- **Resource**: Tài nguyên được bảo vệ
- **Action**: Hành động (create, read, update, delete)
- **Scope**: Phạm vi (own, team, all)
- **Conditions**: Điều kiện áp dụng (JSON)
- **Description**: Mô tả quyền

### RolePermission (Gán quyền cho vai trò)
- **RoleID**: ID vai trò
- **PermissionID**: ID quyền
- **GrantedAt**: Thời gian cấp quyền
- **GrantedBy**: Người cấp quyền
- **ExpiresAt**: Thời gian hết hạn
- **Conditions**: Điều kiện bổ sung

### UserRole (Gán vai trò cho user)
- **UserID**: ID người dùng
- **RoleID**: ID vai trò
- **TenantID**: ID tenant
- **WebsiteID**: ID website (cho website-specific roles)
- **AssignedAt**: Thời gian gán
- **AssignedBy**: Người gán vai trò
- **ExpiresAt**: Thời gian hết hạn
- **Context**: Ngữ cảnh gán vai trò

### Resource (Tài nguyên)
- **ID**: Định danh tài nguyên
- **Name**: Tên tài nguyên
- **Type**: Loại tài nguyên (api, ui, data)
- **Pattern**: Pattern matching
- **Attributes**: Thuộc tính tài nguyên
- **Protected**: Có được bảo vệ không

## Phân cấp vai trò

### Hierarchy Structure

```mermaid
graph TD
    A[Super Admin] --> B[Tenant Admin]
    B --> C[Content Manager]
    B --> D[User Manager]
    C --> E[Editor]
    C --> F[Author]
    D --> G[Moderator]
    E --> H[Contributor]
    F --> H
    G --> I[Member]
    H --> I
    I --> J[Guest]
```

### Role Definitions

#### Super Admin
- Quản lý toàn bộ hệ thống
- Tạo và quản lý tenant
- Cấu hình hệ thống global

#### Tenant Admin  
- Quản lý tenant của mình
- Cấu hình tenant settings
- Quản lý users trong tenant

#### Content Manager
- Quản lý toàn bộ nội dung
- Approve/reject posts
- Quản lý categories và tags

#### Editor
- Chỉnh sửa tất cả bài viết
- Publish/unpublish posts
- Quản lý comments

#### Author
- Tạo và quản lý bài viết của mình
- Chỉnh sửa bài viết của mình
- Trả lời comments

#### Moderator
- Quản lý comments
- Moderate user-generated content
- Handle reports

#### Member
- Đọc nội dung
- Comment trên bài viết
- Like và share

#### Guest
- Chỉ đọc nội dung public
- Không thể tương tác

## Luồng hoạt động

### 1. Kiểm tra quyền truy cập

```mermaid
sequenceDiagram
    participant User as User
    participant Middleware as RBAC Middleware
    participant Cache as Permission Cache
    participant Service as RBAC Service
    participant DB as Database
    
    User->>Middleware: API Request
    Middleware->>Cache: Check cached permissions
    Cache->>Middleware: Cache miss
    Middleware->>Service: Get user permissions
    Service->>DB: Query user roles & permissions
    DB->>Service: Return permission data
    Service->>Cache: Cache permissions
    Service->>Middleware: Return permission result
    Middleware->>User: Allow/Deny access
```

### 2. Gán vai trò cho người dùng

```mermaid
flowchart TD
    A[Admin assigns role] --> B[Validate permission]
    B --> C{Admin has permission?}
    C -->|No| D[Deny assignment]
    C -->|Yes| E[Check role hierarchy]
    E --> F{Valid hierarchy?}
    F -->|No| G[Invalid assignment]
    F -->|Yes| H[Create assignment]
    H --> I[Clear user cache]
    I --> J[Send notification]
    J --> K[Log assignment]
```

### 3. Dynamic Permission Evaluation

```mermaid
flowchart TD
    A[Permission Check] --> B[Get Base Permissions]
    B --> C[Apply Role Hierarchy]
    C --> D[Check Context Conditions]
    D --> E{Conditions Met?}
    E -->|No| F[Deny Access]
    E -->|Yes| G[Check Resource Ownership]
    G --> H{Owns Resource?}
    H -->|No| I[Check Scope Permissions]
    H -->|Yes| J[Allow Access]
    I --> K{Has Scope Access?}
    K -->|No| F
    K -->|Yes| J
```

## Permission System

### Permission Granularity

```mermaid
graph LR
    A[Resource] --> B[Blog Posts]
    A --> C[Comments]
    A --> D[Users]
    A --> E[Settings]
    
    B --> B1[posts:create]
    B --> B2[posts:read]
    B --> B3[posts:update]
    B --> B4[posts:delete]
    B --> B5[posts:publish]
    
    C --> C1[comments:create]
    C --> C2[comments:moderate]
    C --> C3[comments:delete]
    
    D --> D1[users:read]
    D --> D2[users:update]
    D --> D3[users:delete]
    D --> D4[users:assign_roles]
```

### Scope-based Permissions

#### Own Scope
```yaml
permission: "posts:update"
scope: "own"
description: "Can only update their own posts"
conditions:
  - "user.id == resource.author_id"
```

#### Team Scope  
```yaml
permission: "posts:read"
scope: "team"
description: "Can read posts from team members"
conditions:
  - "user.team_id == resource.author.team_id"
```

#### All Scope
```yaml
permission: "posts:delete"
scope: "all" 
description: "Can delete any posts"
conditions: []
```

### Conditional Permissions

```yaml
# Time-based permission
permission: "posts:publish"
conditions:
  time_range:
    start: "09:00"
    end: "17:00"
  days: ["monday", "tuesday", "wednesday", "thursday", "friday"]

# Resource-based permission  
permission: "comments:moderate"
conditions:
  resource_attributes:
    - "post.category != 'sensitive'"
    - "comment.reports_count >= 3"

# Context-based permission
permission: "users:update"
conditions:
  context:
    - "request.ip in whitelist"
    - "user.mfa_enabled == true"
```

## Policy Engine

### Policy Definition

```go
type Policy struct {
    ID          string
    Name        string
    Description string
    Rules       []PolicyRule
    Effect      string // "allow" or "deny"
    Priority    int
}

type PolicyRule struct {
    Resource   string
    Action     string
    Conditions map[string]interface{}
}
```

### Policy Examples

#### Blog Author Policy
```yaml
name: "blog_author_policy"
description: "Permissions for blog authors"
rules:
  - resource: "posts"
    actions: ["create", "read", "update"]
    conditions:
      scope: "own"
  - resource: "comments"
    actions: ["read", "reply"]
    conditions:
      post_owner: "self"
effect: "allow"
priority: 100
```

#### Content Moderator Policy
```yaml
name: "content_moderator_policy"
description: "Permissions for content moderators"
rules:
  - resource: "posts"
    actions: ["read", "update", "delete"]
    conditions:
      status: ["pending", "flagged"]
  - resource: "comments"
    actions: ["read", "moderate", "delete"]
    conditions:
      reported: true
effect: "allow"
priority: 200
```

## Security Features

### Permission Inheritance

```mermaid
graph TD
    A[Parent Role: Editor] --> B[Child Role: Senior Editor]
    A --> C[Permissions: posts:read, posts:update]
    B --> D[Additional: posts:publish, posts:feature]
    C --> E[Inherited Permissions]
    D --> E
    E --> F[Final Permission Set]
```

### Permission Conflicts Resolution

```mermaid
flowchart TD
    A[Multiple Permissions] --> B[Check Priority]
    B --> C[Higher Priority Wins]
    C --> D[Explicit Deny]
    D --> E{Has Deny?}
    E -->|Yes| F[DENY Access]
    E -->|No| G[Check Allow]
    G --> H{Has Allow?}
    H -->|Yes| I[ALLOW Access]
    H -->|No| J[Default DENY]
```

### Audit Trail

```mermaid
sequenceDiagram
    participant User as User
    participant System as System
    participant Audit as Audit Service
    participant DB as Database
    
    User->>System: Perform action
    System->>Audit: Log permission check
    Audit->>DB: Store audit record
    System->>Audit: Log action result
    Audit->>DB: Store action record
    
    Note over Audit,DB: Complete audit trail maintained
```

## API Endpoints

### Role Management
- `GET /api/v1/rbac/roles` - Danh sách vai trò
- `POST /api/v1/rbac/roles` - Tạo vai trò mới
- `PUT /api/v1/rbac/roles/{id}` - Cập nhật vai trò
- `DELETE /api/v1/rbac/roles/{id}` - Xóa vai trò

### Permission Management
- `GET /api/v1/rbac/permissions` - Danh sách quyền
- `POST /api/v1/rbac/permissions` - Tạo quyền mới
- `PUT /api/v1/rbac/permissions/{id}` - Cập nhật quyền

### User Role Assignment
- `GET /api/v1/rbac/users/{id}/roles` - Vai trò của user
- `POST /api/v1/rbac/users/{id}/roles` - Gán vai trò
- `DELETE /api/v1/rbac/users/{id}/roles/{role_id}` - Xóa vai trò

### Permission Check
- `POST /api/v1/rbac/check` - Kiểm tra quyền
- `GET /api/v1/rbac/users/{id}/permissions` - Quyền của user
- `POST /api/v1/rbac/evaluate` - Đánh giá policy

## Multi-Tenancy & Website Isolation

### Website-scoped RBAC

```mermaid
flowchart TD
    A[User Request] --> B[Extract Website Context]
    B --> C[Load Website-specific Roles]
    C --> D[Check Website Permissions]
    D --> E{Has Permission?}
    E -->|Yes| F[Allow Access]
    E -->|No| G[Check Tenant Permissions]
    G --> H{Has Tenant Permission?}
    H -->|Yes| I[Allow Limited Access]
    H -->|No| J[Deny Access]
```

### RBAC Repository with Website Isolation

```go
type RBACRepository struct {
    db *gorm.DB
    websiteID uint
    tenantID uint
}

func (r *RBACRepository) GetUserRoles(userID uint) ([]Role, error) {
    var roles []Role
    query := r.db.Table("user_roles ur").
        Joins("JOIN roles r ON ur.role_id = r.id").
        Where("ur.user_id = ?", userID)
    
    // Website-specific roles have priority
    if r.websiteID > 0 {
        query = query.Where("(r.website_id = ? OR r.website_id IS NULL)", r.websiteID)
    }
    
    // Tenant-level roles
    query = query.Where("r.tenant_id = ?", r.tenantID)
    
    err := query.Find(&roles).Error
    return roles, err
}
```

### Permission Hierarchy

```yaml
permission_hierarchy:
  levels:
    - global: # System-wide permissions
        scope: "all_tenants"
        priority: 1
    - tenant: # Tenant-wide permissions
        scope: "single_tenant"
        priority: 2
    - website: # Website-specific permissions
        scope: "single_website"
        priority: 3
        
  resolution_order:
    - "website_specific"
    - "tenant_level"
    - "global_fallback"
```

## Performance Optimization

### Permission Caching with Website Context

```mermaid
flowchart LR
    A[User Login] --> B[Load Permissions]
    B --> C[Cache with Website Key]
    C --> D[Set TTL: 1 hour]
    
    E[Permission Check] --> F[Check Website Cache]
    F --> G{Cache Hit?}
    G -->|Yes| H[Return Cached]
    G -->|No| I[Load from DB]
    I --> J[Update Website Cache]
    J --> H
```

### Cache Strategy with Website Isolation

```yaml
cache_keys:
  user_permissions: "website:{website_id}:user:{user_id}:permissions"
  role_permissions: "website:{website_id}:role:{role_id}:permissions"
  permission_check: "website:{website_id}:check:{user_id}:{resource}:{action}"
```

### Hierarchical Caching

```mermaid
graph TD
    A[User Permissions] --> B[Role Permissions]
    B --> C[Base Permissions]
    
    A --> D[Cache Level 1]
    B --> E[Cache Level 2] 
    C --> F[Cache Level 3]
    
    D --> G[TTL: 30 min]
    E --> H[TTL: 1 hour]
    F --> I[TTL: 4 hours]
```

## Monitoring và Analytics

### RBAC Metrics

```mermaid
graph LR
    A[RBAC Analytics] --> B[Permission Checks]
    A --> C[Role Usage]
    A --> D[Access Denials]
    A --> E[Policy Violations]
    A --> F[Performance Metrics]
    
    B --> B1[Checks per second]
    B --> B2[Success rate]
    
    C --> C1[Active roles]
    C --> C2[Role assignments]
    
    D --> D1[Denial reasons]
    D --> D2[Most denied resources]
    
    E --> E1[Policy conflicts]
    E --> E2[Escalation attempts]
    
    F --> F1[Cache hit rate]
    F --> F2[Response times]
```

### Security Dashboard
- **Failed Access Attempts**: Thống kê truy cập bị từ chối
- **Permission Usage**: Thống kê sử dụng quyền
- **Role Distribution**: Phân bố vai trò
- **Policy Effectiveness**: Hiệu quả của policies

## Integration Testing

### Permission Test Scenarios

```yaml
test_scenarios:
  - name: "Author can edit own posts"
    user_role: "author"
    resource: "posts/123"
    action: "update"
    conditions:
      author_id: user.id
    expected: "allow"
    
  - name: "Author cannot edit others posts"
    user_role: "author" 
    resource: "posts/456"
    action: "update"
    conditions:
      author_id: other_user.id
    expected: "deny"
    
  - name: "Editor can edit any posts"
    user_role: "editor"
    resource: "posts/789"
    action: "update"
    conditions: {}
    expected: "allow"
```

## Website-based Permission Examples

### Website Editor Role
```yaml
role:
  name: "website_editor"
  scope: "website"
  permissions:
    - "posts:create"
    - "posts:update:own"
    - "posts:publish"
    - "media:upload"
    - "categories:manage"
  restrictions:
    - "website_id_required: true"
    - "cross_website_access: false"
```

### Multi-Website Manager Role
```yaml
role:
  name: "multi_website_manager"
  scope: "tenant"
  permissions:
    - "websites:list"
    - "websites:switch"
    - "posts:read:all_websites"
    - "analytics:view:all_websites"
  restrictions:
    - "tenant_id_required: true"
    - "website_list_access: true"
```

## Tích hợp với các module khác

### Auth Module
- **Role Assignment**: Gán vai trò khi đăng ký với website context
- **Permission Check**: Kiểm tra quyền trong JWT với website_id
- **Session Management**: Quản lý quyền trong session theo website

### Blog Module
- **Content Access**: Kiểm soát truy cập nội dung theo website
- **Publishing Rights**: Quyền publish bài viết trong website cụ thể
- **Moderation**: Quyền kiểm duyệt theo website

### Tenant Module
- **Tenant Isolation**: Phân quyền theo tenant và website
- **Cross-website Access**: Quyền truy cập cross-website trong tenant
- **Website Admin**: Vai trò admin cho website cụ thể

### API Gateway
- **Route Protection**: Bảo vệ API endpoints với website context
- **Rate Limiting**: Giới hạn theo vai trò và website
- **API Key Management**: Quản lý API key theo quyền website

## Best Practices

### Role Design
- **Principle of Least Privilege**: Cấp quyền tối thiểu
- **Role Separation**: Tách biệt vai trò rõ ràng theo website
- **Regular Review**: Xem xét quyền định kỳ
- **Clear Naming**: Đặt tên vai trò rõ ràng với website context
- **Website Isolation**: Đảm bảo vai trò không cross-website

### Permission Management
- **Granular Permissions**: Quyền chi tiết theo website
- **Logical Grouping**: Nhóm quyền logic theo scope
- **Default Deny**: Mặc định từ chối
- **Explicit Allow**: Cho phép rõ ràng với website context
- **Website Validation**: Luôn validate website access

### Performance
- **Cache Strategy**: Chiến lược cache hiệu quả với website keys
- **Lazy Loading**: Load quyền khi cần với website context
- **Batch Operations**: Xử lý hàng loạt theo website
- **Index Optimization**: Tối ưu database index với website_id

### Security
- **Website Isolation**: Đảm bảo không leak data cross-website
- **Permission Validation**: Validate quyền với website context
- **Audit Logging**: Log tất cả operations với website_id
- **Regular Audits**: Kiểm tra quyền cross-website định kỳ

### Example Implementation
```go
type WebsiteRBACService struct {
    repo RBACRepository
    cache CacheService
    websiteID uint
}

func (s *WebsiteRBACService) HasPermission(userID uint, resource, action string) bool {
    // Check cache first
    cacheKey := fmt.Sprintf("website:%d:check:%d:%s:%s", s.websiteID, userID, resource, action)
    if cached, err := s.cache.Get(cacheKey); err == nil {
        return cached.(bool)
    }
    
    // Load permissions with website context
    permissions := s.repo.GetUserPermissions(userID, s.websiteID)
    hasPermission := s.evaluatePermission(permissions, resource, action)
    
    // Cache result
    s.cache.Set(cacheKey, hasPermission, 1800) // 30 minutes
    return hasPermission
}
```

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Auth Module](./auth.md)
- [Tenant Module](./tenant.md)
- [Security Best Practices](../best-practices/security.md)
- [Performance Optimization](../best-practices/performance.md)