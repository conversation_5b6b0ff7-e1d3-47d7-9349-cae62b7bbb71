# Notification Module - <PERSON><PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Notification Module cung cấp hệ thống thông báo đa kênh toàn diện cho Blog API v3, tích hợp email templates, real-time socket notifications, và centralized notification management cho multi-tenant architecture. Module được thiết kế để xử lý high-volume notifications với reliability và scalability.

## Mục tiêu

- **Multi-channel Delivery**: Email, Socket.IO, Push, SMS, In-app notifications
- **Template Management**: Reusable email và notification templates
- **Real-time Delivery**: Instant notifications qua WebSocket
- **Tenant Isolation**: Complete notification isolation cho tenants
- **Delivery Tracking**: Track delivery status và user engagement
- **Batch Processing**: Efficient bulk notification handling

## Kiến trúc hệ thống

### Notification Architecture Overview

```mermaid
flowchart TD
    A[Notification Module] --> B[Notification Engine]
    A --> C[Template System]
    A --> D[Delivery Channels]
    A --> E[Queue System]
    A --> F[Tracking System]
    A --> G[Tenant Manager]
    
    B --> B1[Event Processor]
    B --> B2[Rule Engine]
    B --> B3[Priority Manager]
    B --> B4[Batch Processor]
    
    C --> C1[Email Templates]
    C --> C2[Push Templates]
    C --> C3[SMS Templates]
    C --> C4[In-app Templates]
    
    D --> D1[Email Channel]
    D --> D2[Socket Channel]
    D --> D3[Push Channel]
    D --> D4[SMS Channel]
    
    E --> E1[Priority Queue]
    E --> E2[Retry Queue]
    E --> E3[Batch Queue]
    E --> E4[Schedule Queue]
    
    F --> F1[Delivery Status]
    F --> F2[Read Status]
    F --> F3[Analytics]
    F --> F4[User Preferences]
    
    G --> G1[Tenant Config]
    G --> G2[Tenant Templates]
    G --> G3[Tenant Channels]
    G --> G4[Tenant Analytics]
```

### Components

#### Notification Engine
- **Event Processing**: Listen và process notification events
- **Rule Engine**: Apply business rules cho notification routing
- **Priority Management**: Handle urgent vs normal notifications
- **Batch Processing**: Group notifications cho efficiency

#### Template System
- **Multi-language**: Support multiple languages per template
- **Variable Interpolation**: Dynamic content insertion
- **Template Inheritance**: Base templates với overrides
- **Version Control**: Template versioning và rollback

#### Delivery Channels
- **Email**: SMTP với retry logic và bounce handling
- **Socket.IO**: Real-time WebSocket delivery
- **Push**: Mobile push notifications (FCM/APNS)
- **SMS**: SMS gateway integration

## Model Structures

### Notification Models

```mermaid
erDiagram
    NOTIFICATION {
        uint id PK
        uint tenant_id FK
        string type
        string channel
        string status
        string priority
        json data
        datetime scheduled_at
        datetime sent_at
        datetime created_at
        datetime updated_at
    }
    
    NOTIFICATION_RECIPIENT {
        uint id PK
        uint notification_id FK
        uint user_id FK
        string channel_address
        string status
        datetime sent_at
        datetime delivered_at
        datetime read_at
        json delivery_info
    }
    
    NOTIFICATION_TEMPLATE {
        uint id PK
        uint tenant_id FK
        string code UK
        string type
        string channel
        bool is_active
        json config
        datetime created_at
        datetime updated_at
    }
    
    NOTIFICATION_TEMPLATE_VERSION {
        uint id PK
        uint template_id FK
        string version
        string language
        string subject
        text body_html
        text body_text
        json variables
        datetime created_at
    }
    
    NOTIFICATION_PREFERENCE {
        uint id PK
        uint user_id FK
        uint tenant_id FK
        string channel
        string type
        bool enabled
        json config
        datetime updated_at
    }
    
    NOTIFICATION_LOG {
        uint id PK
        uint notification_id FK
        uint recipient_id FK
        string event_type
        json event_data
        datetime created_at
    }
    
    TENANT_NOTIFICATION_CONFIG {
        uint id PK
        uint tenant_id FK
        string channel
        json credentials
        json settings
        bool is_active
        datetime created_at
        datetime updated_at
    }
    
    NOTIFICATION ||--o{ NOTIFICATION_RECIPIENT : "has"
    NOTIFICATION_TEMPLATE ||--o{ NOTIFICATION_TEMPLATE_VERSION : "has"
    NOTIFICATION_RECIPIENT ||--o{ NOTIFICATION_LOG : "has"
    NOTIFICATION }o--|| TENANT_NOTIFICATION_CONFIG : "uses"
```

## Email Template System

### 1. Template Architecture

```mermaid
flowchart TD
    A[Email Template System] --> B[Template Engine]
    A --> C[Template Storage]
    A --> D[Template Builder]
    A --> E[Preview System]
    
    B --> B1[Handlebars Engine]
    B --> B2[Variable Processing]
    B --> B3[Layout System]
    B --> B4[Asset Management]
    
    C --> C1[Database Storage]
    C --> C2[File Storage]
    C --> C3[Version Control]
    C --> C4[Template Cache]
    
    D --> D1[Visual Editor]
    D --> D2[Code Editor]
    D --> D3[Component Library]
    D --> D4[Preview Mode]
    
    E --> E1[Device Preview]
    E --> E2[Email Client Preview]
    E --> E3[Variable Testing]
    E --> E4[Send Test Email]
```

### 2. Template Structure

#### Base Email Layout
```html
<!DOCTYPE html>
<html lang="{{language}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        /* Reset styles */
        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
        table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
        img { -ms-interpolation-mode: bicubic; border: 0; outline: none; }
        
        /* Template styles */
        .email-container { max-width: 600px; margin: 0 auto; }
        .header { background-color: {{brand.primary_color}}; padding: 20px; }
        .content { padding: 30px; background-color: #ffffff; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <img src="{{brand.logo_url}}" alt="{{brand.name}}" height="40">
        </div>
        
        <!-- Content -->
        <div class="content">
            {{> content}}
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>© {{current_year}} {{brand.name}}. All rights reserved.</p>
            <p>
                <a href="{{unsubscribe_url}}">Unsubscribe</a> | 
                <a href="{{preferences_url}}">Update Preferences</a>
            </p>
        </div>
    </div>
</body>
</html>
```

#### Welcome Email Template
```yaml
template:
  code: "user_welcome"
  type: "email"
  channel: "email"
  versions:
    - language: "en"
      subject: "Welcome to {{brand.name}}, {{user.name}}!"
      variables:
        - user.name
        - user.email
        - activation_url
        - brand.name
        - brand.support_email
      body_html: |
        <h1>Welcome, {{user.name}}!</h1>
        <p>Thank you for joining {{brand.name}}. We're excited to have you on board!</p>
        
        <p>To get started, please verify your email address:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{activation_url}}" style="background-color: {{brand.primary_color}}; 
             color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
            Verify Email Address
          </a>
        </div>
        
        <p>If you have any questions, feel free to contact us at {{brand.support_email}}.</p>
        
        <p>Best regards,<br>The {{brand.name}} Team</p>
      
    - language: "vi"
      subject: "Chào mừng đến với {{brand.name}}, {{user.name}}!"
      body_html: |
        <h1>Xin chào, {{user.name}}!</h1>
        <p>Cảm ơn bạn đã tham gia {{brand.name}}. Chúng tôi rất vui khi có bạn!</p>
        
        <p>Để bắt đầu, vui lòng xác minh địa chỉ email của bạn:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="{{activation_url}}" style="background-color: {{brand.primary_color}}; 
             color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
            Xác minh Email
          </a>
        </div>
```

### 3. Template Management Flow

```mermaid
sequenceDiagram
    participant Admin as Tenant Admin
    participant CMS as CMS Interface
    participant API as Template API
    participant Engine as Template Engine
    participant Storage as Template Storage
    participant Preview as Preview Service
    
    Admin->>CMS: Create/Edit template
    CMS->>API: Save template draft
    API->>Storage: Store template version
    
    Admin->>CMS: Preview template
    CMS->>Preview: Request preview
    Preview->>Engine: Render with test data
    Engine->>Preview: Return rendered HTML
    Preview->>CMS: Display preview
    
    Admin->>CMS: Send test email
    CMS->>API: Send test request
    API->>Engine: Render template
    Engine->>API: Return rendered email
    API->>Admin: Send test email
    
    Admin->>CMS: Activate template
    CMS->>API: Update template status
    API->>Storage: Mark as active
```

## Socket Notification System

### 1. Real-time Architecture

```mermaid
flowchart TD
    A[Socket Notification System] --> B[Socket.IO Server]
    A --> C[Redis Adapter]
    A --> D[Room Management]
    A --> E[Event Router]
    
    B --> B1[Connection Handler]
    B --> B2[Authentication]
    B --> B3[Namespace Management]
    B --> B4[Middleware]
    
    C --> C1[Pub/Sub]
    C --> C2[Session Store]
    C --> C3[Presence Tracking]
    C --> C4[Message Queue]
    
    D --> D1[User Rooms]
    D --> D2[Tenant Rooms]
    D --> D3[Topic Rooms]
    D --> D4[Private Rooms]
    
    E --> E1[Event Listeners]
    E --> E2[Event Emitters]
    E --> E3[Event Filters]
    E --> E4[Event Analytics]
```

### 2. Socket Connection Flow

```mermaid
sequenceDiagram
    participant Client as Client App
    participant Socket as Socket.IO Server
    participant Auth as Auth Service
    participant Redis as Redis PubSub
    participant Queue as Notification Queue
    
    Client->>Socket: Connect with JWT token
    Socket->>Auth: Validate token
    Auth->>Socket: User authenticated
    
    Socket->>Socket: Join user room: user:{user_id}
    Socket->>Socket: Join tenant room: tenant:{tenant_id}
    Socket->>Redis: Subscribe to user channel
    
    Queue->>Redis: Publish notification
    Redis->>Socket: Receive notification
    Socket->>Socket: Check user online status
    Socket->>Client: Emit notification event
    
    Client->>Socket: Acknowledge receipt
    Socket->>Queue: Update delivery status
```

### 3. Socket Event Types

```javascript
// Notification Events
const NotificationEvents = {
  // Server -> Client
  NEW_NOTIFICATION: 'notification:new',
  NOTIFICATION_UPDATE: 'notification:update',
  NOTIFICATION_DELETE: 'notification:delete',
  NOTIFICATION_BATCH: 'notification:batch',
  
  // Client -> Server
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_READ_ALL: 'notification:read_all',
  NOTIFICATION_DELETE_CLIENT: 'notification:delete',
  
  // System Events
  CONNECTION_STATUS: 'connection:status',
  NOTIFICATION_STATS: 'notification:stats',
  SYNC_REQUEST: 'notification:sync'
};

// Notification Payload
interface NotificationPayload {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'system';
  title: string;
  message: string;
  data?: any;
  actions?: NotificationAction[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: string;
  expires_at?: string;
  tenant_id: string;
  user_id: string;
}

// Notification Action
interface NotificationAction {
  id: string;
  label: string;
  type: 'link' | 'button' | 'api_call';
  url?: string;
  method?: string;
  payload?: any;
}
```

### 4. Client Implementation

```javascript
// Socket.IO Client Connection
class NotificationClient {
  constructor(token) {
    this.socket = io('https://api.yourblog.com', {
      auth: { token },
      transports: ['websocket'],
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5
    });
    
    this.setupEventListeners();
  }
  
  setupEventListeners() {
    // Connection events
    this.socket.on('connect', () => {
      console.log('Connected to notification service');
      this.syncNotifications();
    });
    
    // Notification events
    this.socket.on('notification:new', (notification) => {
      this.handleNewNotification(notification);
    });
    
    this.socket.on('notification:batch', (notifications) => {
      notifications.forEach(n => this.handleNewNotification(n));
    });
    
    // Error handling
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }
  
  handleNewNotification(notification) {
    // Display notification in UI
    this.showNotification(notification);
    
    // Update notification badge
    this.updateBadgeCount();
    
    // Play notification sound if enabled
    if (this.soundEnabled && notification.priority === 'high') {
      this.playNotificationSound();
    }
  }
  
  markAsRead(notificationId) {
    this.socket.emit('notification:read', { id: notificationId });
  }
  
  markAllAsRead() {
    this.socket.emit('notification:read_all');
  }
  
  syncNotifications() {
    this.socket.emit('notification:sync', { 
      last_sync: localStorage.getItem('last_notification_sync') 
    });
  }
}
```

## Multi-Tenancy & Website Isolation

### Website-based Notification Management

```mermaid
flowchart TD
    A[Website-specific Notifications] --> B[Website Detection]
    A --> C[Template Inheritance]
    A --> D[Channel Configuration]
    A --> E[Analytics Isolation]
    
    B --> B1[Domain-based Detection]
    B --> B2[Context Setting]
    B --> B3[User Website Mapping]
    B --> B4[Fallback Rules]
    
    C --> C1[Website Templates]
    C --> C2[Tenant Templates]
    C --> C3[Global Templates]
    C --> C4[Override Hierarchy]
    
    D --> D1[Website-specific SMTP]
    D --> D2[Website Branding]
    D --> D3[Channel Overrides]
    D --> D4[Domain-based Sending]
    
    E --> E1[Website Analytics]
    E --> E2[Tenant Aggregation]
    E --> E3[Cross-website Comparison]
    E --> E4[Isolated Metrics]
```

### Notification Repository with Website Isolation

```go
type NotificationRepository struct {
    db *gorm.DB
    websiteID uint
}

func (r *NotificationRepository) GetNotifications(filters NotificationFilters) ([]Notification, error) {
    query := r.db.Where("website_id = ?", r.websiteID)
    
    if filters.Type != "" {
        query = query.Where("type = ?", filters.Type)
    }
    
    var notifications []Notification
    err := query.Find(&notifications).Error
    return notifications, err
}

func (r *NotificationRepository) CreateNotification(notification *Notification) error {
    notification.WebsiteID = r.websiteID
    return r.db.Create(notification).Error
}
```

## Tenant Notification Management

### 1. Tenant CMS Interface

```mermaid
flowchart TD
    A[Tenant CMS] --> B[Notification Dashboard]
    A --> C[Template Manager]
    A --> D[Channel Config]
    A --> E[Analytics]
    
    B --> B1[Notification List]
    B --> B2[Filters & Search]
    B --> B3[Bulk Actions]
    B --> B4[Export]
    
    C --> C1[Template CRUD]
    C --> C2[Template Testing]
    C --> C3[Variable Manager]
    C --> C4[Version Control]
    
    D --> D1[Email Config]
    D --> D2[SMS Config]
    D --> D3[Push Config]
    D --> D4[Channel Rules]
    
    E --> E1[Delivery Stats]
    E --> E2[Engagement Metrics]
    E --> E3[Channel Performance]
    E --> E4[User Preferences]
```

### 2. Tenant Notification API

#### List Notifications
```http
GET /cms/v1/notifications?cursor=abc123&limit=50&status=sent&channel=email&date_from=2024-07-01
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Notifications retrieved successfully",
    "success": true,
    "path": "/cms/v1/notifications",
    "timestamp": "2024-07-15T10:30:00Z"
  },
  "data": [
    {
      "id": "notif_abc123",
      "type": "user_welcome",
      "channel": "email",
      "status": "delivered",
      "priority": "normal",
      "recipients": {
        "total": 1,
        "delivered": 1,
        "read": 1,
        "failed": 0
      },
      "data": {
        "user_name": "John Doe",
        "user_email": "<EMAIL>"
      },
      "scheduled_at": null,
      "sent_at": "2024-07-15T10:00:00Z",
      "created_at": "2024-07-15T09:59:00Z"
    }
  ],
  "meta": {
    "next_cursor": "xyz789",
    "has_more": true,
    "total_count": 1250
  }
}
```

#### Get Notification Details
```http
GET /cms/v1/notifications/{notification_id}
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Notification details retrieved",
    "success": true
  },
  "data": {
    "id": "notif_abc123",
    "type": "user_welcome",
    "channel": "email",
    "template_id": "tmpl_welcome_v2",
    "recipients": [
      {
        "user_id": 123,
        "email": "<EMAIL>",
        "status": "delivered",
        "sent_at": "2024-07-15T10:00:00Z",
        "delivered_at": "2024-07-15T10:00:15Z",
        "read_at": "2024-07-15T10:05:00Z",
        "delivery_info": {
          "smtp_response": "250 OK",
          "message_id": "<<EMAIL>>"
        }
      }
    ],
    "events": [
      {
        "type": "queued",
        "timestamp": "2024-07-15T09:59:00Z"
      },
      {
        "type": "sent",
        "timestamp": "2024-07-15T10:00:00Z"
      },
      {
        "type": "delivered",
        "timestamp": "2024-07-15T10:00:15Z"
      },
      {
        "type": "opened",
        "timestamp": "2024-07-15T10:05:00Z"
      }
    ]
  }
}
```

#### Create Notification
```http
POST /cms/v1/notifications
Content-Type: application/json

{
  "type": "custom_announcement",
  "channel": "multi",
  "channels": ["email", "socket", "push"],
  "priority": "high",
  "template_id": "tmpl_announcement",
  "recipients": {
    "type": "segment",
    "segment_id": "active_users"
  },
  "data": {
    "title": "New Feature Launch",
    "message": "Check out our new analytics dashboard!",
    "cta_url": "https://app.yourblog.com/analytics",
    "cta_text": "View Dashboard"
  },
  "scheduled_at": "2024-07-16T14:00:00Z"
}
```

### 3. Tenant Configuration

```yaml
tenant_notification_config:
  tenant_id: 123
  
  channels:
    email:
      enabled: true
      provider: "smtp"
      settings:
        host: "smtp.tenant-domain.com"
        port: 587
        username: "<EMAIL>"
        password: "encrypted_password"
        from_email: "<EMAIL>"
        from_name: "Tenant Brand Name"
      
    socket:
      enabled: true
      namespace: "/tenant-123"
      
    push:
      enabled: true
      fcm:
        server_key: "tenant_fcm_key"
      apns:
        cert_path: "/certs/tenant_123_apns.p12"
        
    sms:
      enabled: false
      provider: "twilio"
      settings:
        account_sid: "tenant_twilio_sid"
        auth_token: "tenant_twilio_token"
        from_number: "+**********"
  
  preferences:
    batch_size: 100
    retry_attempts: 3
    retry_delay: 300 # seconds
    
  templates:
    allow_custom: true
    require_approval: false
    variable_whitelist: ["user", "tenant", "brand", "data"]
    
  analytics:
    track_opens: true
    track_clicks: true
    retention_days: 90
```

## Notification Processing Flow

### 1. Complete Notification Lifecycle

```mermaid
sequenceDiagram
    participant App as Application
    participant API as Notification API
    participant Queue as Message Queue
    participant Processor as Notification Processor
    participant Template as Template Engine
    participant Channel as Delivery Channel
    participant Socket as Socket Server
    participant User as User Device
    
    App->>API: Trigger notification event
    API->>API: Validate request
    API->>Queue: Queue notification job
    
    Queue->>Processor: Process notification
    Processor->>Processor: Load user preferences
    Processor->>Template: Render template
    Template->>Processor: Return rendered content
    
    alt Email Channel
        Processor->>Channel: Send email
        Channel->>User: Deliver email
    else Socket Channel
        Processor->>Socket: Emit notification
        Socket->>User: Real-time delivery
    else Push Channel
        Processor->>Channel: Send push
        Channel->>User: Mobile notification
    end
    
    User->>API: Track interaction
    API->>API: Update analytics
```

### 2. Batch Notification Processing

```mermaid
flowchart TD
    A[Batch Request] --> B{Validate Batch}
    B -->|Valid| C[Split into Chunks]
    B -->|Invalid| D[Return Error]
    
    C --> E[Queue Chunks]
    E --> F[Process Chunk 1]
    E --> G[Process Chunk 2]
    E --> H[Process Chunk N]
    
    F --> I[Aggregate Results]
    G --> I
    H --> I
    
    I --> J[Generate Report]
    J --> K[Notify Completion]
    
    style A fill:#e3f2fd
    style K fill:#e8f5e8
    style D fill:#ffebee
```

## Best Practices & Design Decisions

### 1. Architecture Decisions

#### Multi-tenant Isolation
```yaml
isolation_strategy:
  data_level:
    - Separate notification tables per tenant (for large tenants)
    - Shared tables with tenant_id (for small/medium tenants)
    
  template_level:
    - Base templates shared across tenants
    - Tenant-specific template overrides
    - Custom template approval workflow
    
  channel_level:
    - Tenant-specific SMTP configurations
    - Isolated Socket.IO namespaces
    - Separate push notification certificates
    
  queue_level:
    - Priority queues per tenant
    - Rate limiting per tenant
    - Separate retry policies
```

#### Scalability Considerations
```yaml
scalability:
  horizontal_scaling:
    - Multiple notification processors
    - Redis cluster for queue management
    - Load-balanced Socket.IO servers
    
  performance_optimization:
    - Template caching với Redis
    - Batch processing for bulk notifications
    - Async processing với worker pools
    
  reliability:
    - Retry mechanism với exponential backoff
    - Dead letter queue cho failed notifications
    - Delivery status tracking
```

### 2. Template Best Practices

#### Template Design
- **Mobile First**: Design for mobile email clients
- **Inline CSS**: Use inline styles for compatibility
- **Fallback Text**: Always include text version
- **Preheader Text**: Optimize preview text
- **Alt Text**: Include alt text for images

#### Variable Management
```javascript
// Safe variable access với defaults
const templateHelpers = {
  safe: (value, defaultValue = '') => value || defaultValue,
  
  formatDate: (date, format = 'YYYY-MM-DD') => {
    return moment(date).format(format);
  },
  
  formatCurrency: (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  },
  
  truncate: (text, length = 100) => {
    return text.length > length 
      ? text.substring(0, length) + '...' 
      : text;
  }
};
```

### 3. Socket Optimization

#### Connection Management
```javascript
// Implement connection pooling
class SocketConnectionPool {
  constructor(maxConnections = 1000) {
    this.connections = new Map();
    this.maxConnections = maxConnections;
  }
  
  addConnection(userId, socket) {
    if (this.connections.size >= this.maxConnections) {
      this.removeOldestConnection();
    }
    
    this.connections.set(userId, {
      socket,
      lastActivity: Date.now()
    });
  }
  
  removeOldestConnection() {
    let oldest = null;
    let oldestTime = Date.now();
    
    for (const [userId, conn] of this.connections) {
      if (conn.lastActivity < oldestTime) {
        oldest = userId;
        oldestTime = conn.lastActivity;
      }
    }
    
    if (oldest) {
      this.connections.get(oldest).socket.disconnect();
      this.connections.delete(oldest);
    }
  }
}
```

#### Event Batching
```javascript
// Batch multiple notifications
class NotificationBatcher {
  constructor(batchSize = 10, flushInterval = 1000) {
    this.batch = [];
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.timer = null;
  }
  
  add(notification) {
    this.batch.push(notification);
    
    if (this.batch.length >= this.batchSize) {
      this.flush();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.flush(), this.flushInterval);
    }
  }
  
  flush() {
    if (this.batch.length > 0) {
      this.socket.emit('notification:batch', this.batch);
      this.batch = [];
    }
    
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}
```

## API Endpoints

### Template Management

#### List Templates
```http
GET /cms/v1/notification-templates?channel=email&active=true
```

#### Create Template
```http
POST /cms/v1/notification-templates
Content-Type: application/json

{
  "code": "order_confirmation",
  "type": "transactional",
  "channel": "email",
  "versions": [
    {
      "language": "en",
      "subject": "Order #{{order.number}} Confirmed",
      "body_html": "<html>...</html>",
      "body_text": "Plain text version...",
      "variables": ["order", "user", "items"]
    }
  ]
}
```

#### Test Template
```http
POST /cms/v1/notification-templates/{template_id}/test
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "language": "en",
  "test_data": {
    "order": {
      "number": "TEST-12345",
      "total": 99.99
    },
    "user": {
      "name": "Test User"
    }
  }
}
```

### Channel Configuration

#### Update Channel Config
```http
PUT /cms/v1/notification-channels/email
Content-Type: application/json

{
  "enabled": true,
  "provider": "smtp",
  "settings": {
    "host": "smtp.example.com",
    "port": 587,
    "username": "<EMAIL>",
    "from_email": "<EMAIL>",
    "from_name": "Your Brand"
  }
}
```

### Analytics

#### Get Notification Analytics
```http
GET /cms/v1/notifications/analytics?period=30d&channel=email
```

**Response:**
```json
{
  "status": {
    "code": 200,
    "message": "Analytics retrieved successfully",
    "success": true
  },
  "data": {
    "summary": {
      "total_sent": 15420,
      "total_delivered": 15201,
      "total_opened": 8234,
      "total_clicked": 3421,
      "delivery_rate": 98.58,
      "open_rate": 54.15,
      "click_rate": 22.51
    },
    "by_channel": {
      "email": {
        "sent": 10234,
        "delivered": 10100,
        "opened": 6234,
        "clicked": 2834
      },
      "socket": {
        "sent": 3421,
        "delivered": 3401,
        "read": 2987
      },
      "push": {
        "sent": 1765,
        "delivered": 1700,
        "opened": 1013
      }
    },
    "by_type": {
      "user_welcome": {
        "sent": 342,
        "delivery_rate": 99.1,
        "open_rate": 67.8
      },
      "order_confirmation": {
        "sent": 1523,
        "delivery_rate": 98.9,
        "open_rate": 82.3
      }
    },
    "trends": [
      {
        "date": "2024-07-01",
        "sent": 523,
        "delivered": 515,
        "opened": 287
      }
    ]
  }
}
```

## Configuration

### Module Configuration
```yaml
notification:
  enabled: true
  
  channels:
    email:
      enabled: true
      default_provider: "smtp"
      providers:
        smtp:
          pool: true
          pool_size: 5
          rate_limit: 100/minute
        sendgrid:
          api_key: "${SENDGRID_API_KEY}"
          
    socket:
      enabled: true
      adapter: "redis"
      namespaces:
        - "/notifications"
        - "/tenant-notifications"
        
    push:
      enabled: true
      providers:
        fcm:
          enabled: true
        apns:
          enabled: true
          
  queue:
    provider: "redis"
    prefix: "notification:"
    priorities:
      urgent: 1
      high: 2
      normal: 3
      low: 4
      
  batch:
    enabled: true
    max_size: 1000
    timeout: 5000
    
  retry:
    max_attempts: 3
    backoff: "exponential"
    delays: [1000, 5000, 15000]
    
  tracking:
    open_tracking: true
    click_tracking: true
    pixel_url: "https://track.yourdomain.com/pixel"
    
  storage:
    retention_days: 90
    archive_after_days: 30
    
  templates:
    cache_ttl: 3600
    preview_data: "fixtures/preview-data.json"
```

## Security Considerations

### Template Security
- **XSS Prevention**: Escape all user input
- **CSRF Protection**: Use tokens for actions
- **Content Security**: Validate template content
- **Variable Whitelisting**: Only allow safe variables

### Channel Security
- **Authentication**: Secure all channel credentials
- **Encryption**: TLS for all communications
- **Rate Limiting**: Prevent notification spam
- **Access Control**: Tenant-based permissions

### Data Protection
- **PII Handling**: Minimize personal data in notifications
- **Encryption at Rest**: Encrypt sensitive notification data
- **Audit Logging**: Track all notification operations
- **GDPR Compliance**: Right to erasure support

## Performance Optimization

### Caching Strategy
```yaml
caching:
  templates:
    provider: "redis"
    ttl: 3600
    key_pattern: "template:{tenant_id}:{template_id}:{version}"
    
  user_preferences:
    provider: "redis"
    ttl: 7200
    key_pattern: "pref:{user_id}:{channel}"
    
  delivery_status:
    provider: "memory"
    ttl: 300
    max_entries: 10000
```

### Queue Optimization
- **Priority Queues**: Separate queues by priority
- **Worker Pools**: Scale workers based on load
- **Batch Processing**: Group similar notifications
- **Circuit Breaker**: Prevent cascade failures

## Monitoring & Alerts

### Key Metrics
```yaml
metrics:
  delivery:
    - delivery_rate
    - bounce_rate
    - complaint_rate
    
  engagement:
    - open_rate
    - click_rate
    - unsubscribe_rate
    
  performance:
    - queue_depth
    - processing_time
    - error_rate
    
  alerts:
    - delivery_rate < 95%
    - bounce_rate > 5%
    - queue_depth > 10000
    - error_rate > 1%
```

## Webhook & Integration System

### 1. Webhook Architecture

```mermaid
flowchart TD
    A[Webhook System] --> B[Event Manager]
    A --> C[Endpoint Registry]
    A --> D[Delivery Engine]
    A --> E[Integration Hub]
    
    B --> B1[Event Types]
    B --> B2[Event Filtering]
    B --> B3[Event Transformation]
    B --> B4[Event History]
    
    C --> C1[Tenant Endpoints]
    C --> C2[Third-party Services]
    C --> C3[Custom Webhooks]
    C --> C4[Endpoint Validation]
    
    D --> D1[HTTP Delivery]
    D --> D2[Retry Logic]
    D --> D3[Circuit Breaker]
    D --> D4[Delivery Monitoring]
    
    E --> E1[Slack Integration]
    E --> E2[Discord Integration]
    E --> E3[Teams Integration]
    E --> E4[Custom Integrations]
```

### 2. Webhook Event Types

```yaml
webhook_events:
  notification:
    - notification.created
    - notification.queued
    - notification.sent
    - notification.delivered
    - notification.failed
    - notification.opened
    - notification.clicked
    - notification.unsubscribed
    
  email:
    - email.sent
    - email.delivered
    - email.bounced
    - email.complained
    - email.opened
    - email.clicked
    
  push:
    - push.sent
    - push.delivered
    - push.failed
    - push.opened
    
  sms:
    - sms.sent
    - sms.delivered
    - sms.failed
```

### 3. Webhook Endpoint Configuration

```go
type WebhookEndpoint struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    Name            string    `gorm:"not null" json:"name"`
    URL             string    `gorm:"not null" json:"url"`
    Secret          string    `json:"-"` // For HMAC-SHA256 signature
    Events          []string  `gorm:"type:json" json:"events"`
    Headers         JSON      `gorm:"type:json" json:"headers"`
    
    // Retry configuration
    MaxRetries      int       `gorm:"default:3" json:"max_retries"`
    TimeoutSeconds  int       `gorm:"default:30" json:"timeout_seconds"`
    RetryStrategy   string    `gorm:"default:'exponential'" json:"retry_strategy"`
    
    // Circuit breaker
    FailureThreshold int      `gorm:"default:5" json:"failure_threshold"`
    RecoveryTimeout  int      `gorm:"default:300" json:"recovery_timeout"`
    
    // Status
    IsActive        bool      `gorm:"default:true" json:"is_active"`
    LastError       string    `json:"last_error,omitempty"`
    LastSuccessAt   *time.Time `json:"last_success_at,omitempty"`
    LastFailureAt   *time.Time `json:"last_failure_at,omitempty"`
    
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

### 4. Webhook Delivery Flow

```mermaid
sequenceDiagram
    participant Event as Event Source
    participant Manager as Webhook Manager
    participant Queue as Delivery Queue
    participant Delivery as Delivery Service
    participant Endpoint as External Endpoint
    participant Monitor as Monitoring
    
    Event->>Manager: Trigger event
    Manager->>Manager: Filter subscribed endpoints
    Manager->>Queue: Queue delivery jobs
    
    Queue->>Delivery: Process delivery
    Delivery->>Delivery: Generate HMAC signature
    Delivery->>Endpoint: POST webhook payload
    
    alt Success
        Endpoint->>Delivery: 200 OK
        Delivery->>Monitor: Record success
    else Failure
        Endpoint->>Delivery: Error response
        Delivery->>Queue: Retry with backoff
        Delivery->>Monitor: Record failure
        
        alt Max retries reached
            Delivery->>Manager: Mark endpoint unhealthy
            Manager->>Manager: Activate circuit breaker
        end
    end
```

### 5. Third-party Integrations

#### Slack Integration
```go
type SlackIntegration struct {
    WebhookURL string
    Channel    string
    Username   string
    IconURL    string
}

func (s *SlackService) SendNotification(notification *Notification) error {
    payload := SlackPayload{
        Channel: s.Channel,
        Username: s.Username,
        IconURL: s.IconURL,
        Attachments: []SlackAttachment{{
            Color: s.getColorByType(notification.Type),
            Title: notification.Title,
            Text: notification.Message,
            Fields: []SlackField{
                {Title: "Type", Value: notification.Type, Short: true},
                {Title: "Priority", Value: notification.Priority, Short: true},
            },
            Timestamp: notification.CreatedAt.Unix(),
        }},
    }
    
    return s.httpClient.PostJSON(s.WebhookURL, payload)
}
```

#### Discord Integration
```go
type DiscordIntegration struct {
    WebhookURL string
    Username   string
    AvatarURL  string
}

func (d *DiscordService) SendNotification(notification *Notification) error {
    embed := DiscordEmbed{
        Title:       notification.Title,
        Description: notification.Message,
        Color:       d.getColorByType(notification.Type),
        Timestamp:   notification.CreatedAt.Format(time.RFC3339),
        Fields: []DiscordField{
            {Name: "Type", Value: notification.Type, Inline: true},
            {Name: "Priority", Value: notification.Priority, Inline: true},
        },
    }
    
    payload := DiscordPayload{
        Username:  d.Username,
        AvatarURL: d.AvatarURL,
        Embeds:    []DiscordEmbed{embed},
    }
    
    return d.httpClient.PostJSON(d.WebhookURL, payload)
}
```

#### Microsoft Teams Integration
```go
type TeamsIntegration struct {
    WebhookURL string
}

func (t *TeamsService) SendNotification(notification *Notification) error {
    card := TeamsAdaptiveCard{
        Type: "AdaptiveCard",
        Version: "1.2",
        Body: []TeamsCardElement{
            {
                Type: "TextBlock",
                Text: notification.Title,
                Size: "Large",
                Weight: "Bolder",
            },
            {
                Type: "TextBlock",
                Text: notification.Message,
                Wrap: true,
            },
        },
        Actions: t.buildActions(notification),
    }
    
    payload := TeamsPayload{
        Type: "message",
        Attachments: []TeamsAttachment{{
            ContentType: "application/vnd.microsoft.card.adaptive",
            Content: card,
        }},
    }
    
    return t.httpClient.PostJSON(t.WebhookURL, payload)
}
```

### 6. Webhook Security

#### HMAC Signature Generation
```go
func (w *WebhookService) generateSignature(payload []byte, secret string) string {
    h := hmac.New(sha256.New, []byte(secret))
    h.Write(payload)
    return hex.EncodeToString(h.Sum(nil))
}

func (w *WebhookService) deliverWebhook(endpoint *WebhookEndpoint, event *WebhookEvent) error {
    payload, _ := json.Marshal(event)
    signature := w.generateSignature(payload, endpoint.Secret)
    
    headers := map[string]string{
        "Content-Type": "application/json",
        "X-Webhook-Signature": signature,
        "X-Webhook-Event": event.Type,
        "X-Webhook-Delivery": uuid.New().String(),
    }
    
    // Merge custom headers
    for k, v := range endpoint.Headers {
        headers[k] = v.(string)
    }
    
    return w.httpClient.PostWithHeaders(endpoint.URL, payload, headers)
}
```

### 7. Retry Strategy

```go
type RetryStrategy interface {
    NextDelay(attempt int) time.Duration
}

type ExponentialBackoff struct {
    BaseDelay   time.Duration
    MaxDelay    time.Duration
    Multiplier  float64
}

func (e *ExponentialBackoff) NextDelay(attempt int) time.Duration {
    delay := float64(e.BaseDelay) * math.Pow(e.Multiplier, float64(attempt-1))
    if delay > float64(e.MaxDelay) {
        return e.MaxDelay
    }
    
    // Add jitter to prevent thundering herd
    jitter := rand.Float64() * 0.1 * delay
    return time.Duration(delay + jitter)
}

// Usage in webhook delivery
func (w *WebhookService) deliverWithRetry(endpoint *WebhookEndpoint, event *WebhookEvent) error {
    strategy := &ExponentialBackoff{
        BaseDelay:  time.Second,
        MaxDelay:   time.Minute * 5,
        Multiplier: 2.0,
    }
    
    var lastErr error
    for attempt := 1; attempt <= endpoint.MaxRetries; attempt++ {
        err := w.deliverWebhook(endpoint, event)
        if err == nil {
            return nil
        }
        
        lastErr = err
        if attempt < endpoint.MaxRetries {
            delay := strategy.NextDelay(attempt)
            time.Sleep(delay)
        }
    }
    
    return fmt.Errorf("webhook delivery failed after %d attempts: %w", 
        endpoint.MaxRetries, lastErr)
}
```

## Enhanced Socket.IO Features

### 1. Advanced Presence System

```mermaid
flowchart TD
    A[Presence System] --> B[Connection Tracking]
    A --> C[Device Management]
    A --> D[Status Broadcasting]
    A --> E[Activity Monitoring]
    
    B --> B1[Online Status]
    B --> B2[Last Seen]
    B --> B3[Connection Quality]
    B --> B4[Session Duration]
    
    C --> C1[Multi-device Support]
    C --> C2[Device Prioritization]
    C --> C3[Device Sync]
    C --> C4[Device Limits]
    
    D --> D1[Presence Updates]
    D --> D2[Typing Indicators]
    D --> D3[Read Receipts]
    D --> D4[Custom Status]
    
    E --> E1[Active Time Tracking]
    E --> E2[Idle Detection]
    E --> E3[Activity Patterns]
    E --> E4[Engagement Metrics]
```

### 2. Presence Tracking Implementation

```go
type UserPresence struct {
    UserID        uint                `json:"user_id"`
    Status        string              `json:"status"` // online, away, offline
    LastSeenAt    time.Time           `json:"last_seen_at"`
    Devices       []DevicePresence    `json:"devices"`
    CustomStatus  string              `json:"custom_status,omitempty"`
    Activities    []UserActivity      `json:"activities,omitempty"`
}

type DevicePresence struct {
    DeviceID      string    `json:"device_id"`
    DeviceType    string    `json:"device_type"` // web, mobile, desktop
    SocketID      string    `json:"socket_id"`
    ConnectedAt   time.Time `json:"connected_at"`
    LastActiveAt  time.Time `json:"last_active_at"`
    IsActive      bool      `json:"is_active"`
    Platform      string    `json:"platform"`
    AppVersion    string    `json:"app_version"`
}

type PresenceManager struct {
    redis         *redis.Client
    presenceCache map[uint]*UserPresence
    mu            sync.RWMutex
}

func (p *PresenceManager) UpdatePresence(userID uint, deviceID string, status string) error {
    p.mu.Lock()
    defer p.mu.Unlock()
    
    presence, exists := p.presenceCache[userID]
    if !exists {
        presence = &UserPresence{
            UserID:     userID,
            Status:     status,
            LastSeenAt: time.Now(),
            Devices:    []DevicePresence{},
        }
    }
    
    // Update device presence
    deviceFound := false
    for i, device := range presence.Devices {
        if device.DeviceID == deviceID {
            presence.Devices[i].LastActiveAt = time.Now()
            presence.Devices[i].IsActive = (status != "offline")
            deviceFound = true
            break
        }
    }
    
    if !deviceFound && status != "offline" {
        presence.Devices = append(presence.Devices, DevicePresence{
            DeviceID:     deviceID,
            ConnectedAt:  time.Now(),
            LastActiveAt: time.Now(),
            IsActive:     true,
        })
    }
    
    // Update overall status
    presence.Status = p.calculateOverallStatus(presence.Devices)
    presence.LastSeenAt = time.Now()
    
    // Store in Redis for cross-server sync
    return p.syncToRedis(userID, presence)
}
```

### 3. Cross-Device Notification Sync

```mermaid
sequenceDiagram
    participant Device1 as Device 1
    participant Server1 as Socket Server 1
    participant Redis as Redis PubSub
    participant Server2 as Socket Server 2
    participant Device2 as Device 2
    
    Device1->>Server1: Mark notification as read
    Server1->>Redis: Publish sync event
    Redis->>Server2: Broadcast sync event
    Server2->>Device2: Update notification status
    
    Device2->>Server2: Acknowledge sync
    Server2->>Redis: Publish acknowledgment
    Redis->>Server1: Broadcast acknowledgment
    Server1->>Device1: Confirm sync complete
```

### 4. Notification Sync Implementation

```go
type NotificationSyncService struct {
    redis    *redis.Client
    sockets  map[string]*SocketConnection
    mu       sync.RWMutex
}

type SyncEvent struct {
    Type      string    `json:"type"` // read, delete, update
    UserID    uint      `json:"user_id"`
    DeviceID  string    `json:"device_id"`
    Timestamp time.Time `json:"timestamp"`
    Data      JSON      `json:"data"`
}

func (s *NotificationSyncService) SyncAcrossDevices(userID uint, event *SyncEvent) error {
    // Get all user's devices
    devices := s.getUserDevices(userID)
    
    // Publish to Redis for cross-server sync
    channel := fmt.Sprintf("user:%d:sync", userID)
    eventData, _ := json.Marshal(event)
    
    err := s.redis.Publish(context.Background(), channel, eventData).Err()
    if err != nil {
        return err
    }
    
    // Local delivery to connected devices
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    for _, device := range devices {
        if socket, exists := s.sockets[device.SocketID]; exists {
            socket.Emit("notification:sync", event)
        }
    }
    
    return nil
}

// Handle notification read on one device
func (s *NotificationSyncService) HandleNotificationRead(
    userID uint, 
    deviceID string, 
    notificationIDs []string,
) error {
    event := &SyncEvent{
        Type:      "read",
        UserID:    userID,
        DeviceID:  deviceID,
        Timestamp: time.Now(),
        Data: map[string]interface{}{
            "notification_ids": notificationIDs,
        },
    }
    
    return s.SyncAcrossDevices(userID, event)
}
```

### 5. Typing Indicators

```go
type TypingIndicator struct {
    UserID    uint      `json:"user_id"`
    ChannelID string    `json:"channel_id"`
    IsTyping  bool      `json:"is_typing"`
    Timestamp time.Time `json:"timestamp"`
}

func (s *SocketService) HandleTypingIndicator(socket *Socket, data *TypingIndicator) {
    // Set TTL for typing indicator (e.g., 5 seconds)
    ttl := 5 * time.Second
    key := fmt.Sprintf("typing:%s:%d", data.ChannelID, data.UserID)
    
    if data.IsTyping {
        s.redis.Set(context.Background(), key, "1", ttl)
        
        // Broadcast to channel members
        s.BroadcastToChannel(data.ChannelID, "user:typing", map[string]interface{}{
            "user_id":   data.UserID,
            "is_typing": true,
        })
    } else {
        s.redis.Del(context.Background(), key)
        
        s.BroadcastToChannel(data.ChannelID, "user:typing", map[string]interface{}{
            "user_id":   data.UserID,
            "is_typing": false,
        })
    }
}

// Auto-clear typing indicator after timeout
func (s *SocketService) StartTypingMonitor() {
    ticker := time.NewTicker(1 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        // Check and clear expired typing indicators
        pattern := "typing:*"
        keys, _ := s.redis.Keys(context.Background(), pattern).Result()
        
        for _, key := range keys {
            ttl, _ := s.redis.TTL(context.Background(), key).Result()
            if ttl <= 0 {
                // Extract channel and user from key
                parts := strings.Split(key, ":")
                if len(parts) >= 3 {
                    channelID := parts[1]
                    userID, _ := strconv.Atoi(parts[2])
                    
                    s.BroadcastToChannel(channelID, "user:typing", map[string]interface{}{
                        "user_id":   userID,
                        "is_typing": false,
                    })
                }
            }
        }
    }
}
```

## Performance Optimization

### 1. Intelligent Caching Architecture

```mermaid
flowchart TD
    A[Caching System] --> B[Template Cache]
    A --> C[Content Cache]
    A --> D[User Cache]
    A --> E[Analytics Cache]
    
    B --> B1[Compiled Templates]
    B --> B2[Template Predictions]
    B --> B3[Variable Cache]
    B --> B4[Asset Cache]
    
    C --> C1[Rendered Content]
    C --> C2[Personalization Cache]
    C --> C3[Localization Cache]
    C --> C4[Media Cache]
    
    D --> D1[User Preferences]
    D --> D2[Device Info]
    D --> D3[Notification History]
    D --> D4[Engagement Patterns]
    
    E --> E1[Aggregate Metrics]
    E --> E2[Real-time Stats]
    E --> E3[Report Cache]
    E --> E4[Trend Analysis]
```

### 2. Predictive Template Caching

```go
type TemplateCacheManager struct {
    redis           *redis.Client
    predictor       *TemplatePredictor
    preloadQueue    chan string
    cacheStats      *CacheStatistics
}

type TemplatePredictor struct {
    usageHistory    map[string][]UsageRecord
    timePatterns    map[string]*TimePattern
    eventCorrelation map[string][]string
}

type UsageRecord struct {
    TemplateID string
    Timestamp  time.Time
    UserCount  int
    Variables  map[string]interface{}
}

func (p *TemplatePredictor) PredictNextTemplates(currentTemplate string) []string {
    predictions := make(map[string]float64)
    
    // Time-based prediction
    hour := time.Now().Hour()
    dayOfWeek := time.Now().Weekday()
    
    for templateID, pattern := range p.timePatterns {
        score := pattern.GetProbability(hour, dayOfWeek)
        predictions[templateID] = score
    }
    
    // Event correlation prediction
    if correlated, exists := p.eventCorrelation[currentTemplate]; exists {
        for _, templateID := range correlated {
            predictions[templateID] += 0.5
        }
    }
    
    // Sort by prediction score
    return p.getTopPredictions(predictions, 5)
}

func (c *TemplateCacheManager) PreloadTemplates() {
    ticker := time.NewTicker(5 * time.Minute)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // Get current active templates
            activeTemplates := c.getActiveTemplates()
            
            for _, template := range activeTemplates {
                // Predict next templates
                predictions := c.predictor.PredictNextTemplates(template.ID)
                
                // Preload predicted templates
                for _, predictedID := range predictions {
                    c.preloadQueue <- predictedID
                }
            }
            
        case templateID := <-c.preloadQueue:
            // Preload template if not cached
            if !c.isCached(templateID) {
                c.preloadTemplate(templateID)
            }
        }
    }
}
```

### 3. CDN Integration for Email Assets

```go
type CDNAssetManager struct {
    cdnClient    *CDNClient
    assetCache   map[string]*CachedAsset
    uploadQueue  chan *AssetUpload
    mu           sync.RWMutex
}

type CachedAsset struct {
    OriginalURL  string
    CDNURL       string
    ContentType  string
    Size         int64
    Hash         string
    CachedAt     time.Time
    TTL          time.Duration
}

func (c *CDNAssetManager) ProcessEmailTemplate(template *EmailTemplate) (*EmailTemplate, error) {
    // Parse template for asset URLs
    assets := c.extractAssets(template.HTML)
    
    // Process each asset
    for _, asset := range assets {
        cdnURL, err := c.ensureAssetOnCDN(asset)
        if err != nil {
            continue // Skip failed assets
        }
        
        // Replace URL in template
        template.HTML = strings.ReplaceAll(template.HTML, asset.URL, cdnURL)
    }
    
    // Optimize images for email
    template.HTML = c.optimizeImagesForEmail(template.HTML)
    
    return template, nil
}

func (c *CDNAssetManager) ensureAssetOnCDN(asset *Asset) (string, error) {
    c.mu.RLock()
    if cached, exists := c.assetCache[asset.URL]; exists {
        if time.Since(cached.CachedAt) < cached.TTL {
            c.mu.RUnlock()
            return cached.CDNURL, nil
        }
    }
    c.mu.RUnlock()
    
    // Upload to CDN
    cdnURL, err := c.uploadToCDN(asset)
    if err != nil {
        return "", err
    }
    
    // Cache the result
    c.mu.Lock()
    c.assetCache[asset.URL] = &CachedAsset{
        OriginalURL: asset.URL,
        CDNURL:      cdnURL,
        ContentType: asset.ContentType,
        Size:        asset.Size,
        Hash:        asset.Hash,
        CachedAt:    time.Now(),
        TTL:         24 * time.Hour,
    }
    c.mu.Unlock()
    
    return cdnURL, nil
}
```

### 4. Load Testing Framework

```go
type LoadTestScenario struct {
    Name            string
    Duration        time.Duration
    UsersPerSecond  int
    NotificationMix map[string]float64 // notification type -> percentage
    Channels        []string
    TenantIDs       []uint
}

type LoadTester struct {
    scenario        *LoadTestScenario
    metrics         *LoadTestMetrics
    notificationAPI *NotificationAPI
    socketClients   []*SocketClient
}

func (l *LoadTester) RunScenario() (*LoadTestResults, error) {
    ctx, cancel := context.WithTimeout(context.Background(), l.scenario.Duration)
    defer cancel()
    
    // Start metrics collection
    l.metrics.Start()
    defer l.metrics.Stop()
    
    // Create worker pool
    workers := l.scenario.UsersPerSecond * 10
    workQueue := make(chan *LoadTestTask, workers)
    
    // Start workers
    var wg sync.WaitGroup
    for i := 0; i < workers; i++ {
        wg.Add(1)
        go l.worker(ctx, workQueue, &wg)
    }
    
    // Generate load
    ticker := time.NewTicker(time.Second / time.Duration(l.scenario.UsersPerSecond))
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            close(workQueue)
            wg.Wait()
            return l.metrics.GetResults(), nil
            
        case <-ticker.C:
            task := l.generateTask()
            select {
            case workQueue <- task:
            default:
                l.metrics.RecordDropped()
            }
        }
    }
}

func (l *LoadTester) generateTask() *LoadTestTask {
    // Select notification type based on mix
    notifType := l.selectByWeight(l.scenario.NotificationMix)
    
    // Select random tenant
    tenantID := l.scenario.TenantIDs[rand.Intn(len(l.scenario.TenantIDs))]
    
    // Generate realistic notification data
    return &LoadTestTask{
        Type:     notifType,
        TenantID: tenantID,
        Channel:  l.scenario.Channels[rand.Intn(len(l.scenario.Channels))],
        Data:     l.generateTestData(notifType),
    }
}

// Realistic test scenarios
var LoadTestScenarios = map[string]*LoadTestScenario{
    "normal_traffic": {
        Name:           "Normal Traffic Pattern",
        Duration:       10 * time.Minute,
        UsersPerSecond: 100,
        NotificationMix: map[string]float64{
            "user_activity":  0.4,
            "system_alert":   0.2,
            "marketing":      0.2,
            "transactional":  0.2,
        },
        Channels: []string{"email", "socket", "push"},
    },
    "peak_traffic": {
        Name:           "Peak Traffic Pattern",
        Duration:       5 * time.Minute,
        UsersPerSecond: 1000,
        NotificationMix: map[string]float64{
            "flash_sale":     0.6,
            "price_alert":    0.3,
            "system_update":  0.1,
        },
        Channels: []string{"socket", "push"},
    },
    "email_campaign": {
        Name:           "Email Campaign",
        Duration:       30 * time.Minute,
        UsersPerSecond: 500,
        NotificationMix: map[string]float64{
            "newsletter":     0.7,
            "promotional":    0.3,
        },
        Channels: []string{"email"},
    },
}
```

### 5. Auto-scaling Policies

```yaml
auto_scaling:
  notification_workers:
    min_instances: 2
    max_instances: 20
    target_metrics:
      - type: queue_depth
        target: 1000
        scale_up_threshold: 1200
        scale_down_threshold: 800
      - type: processing_time
        target: 500ms
        scale_up_threshold: 1000ms
        scale_down_threshold: 200ms
    scale_up:
      increment: 2
      cooldown: 60s
    scale_down:
      decrement: 1
      cooldown: 300s
      
  socket_servers:
    min_instances: 2
    max_instances: 10
    target_metrics:
      - type: connections_per_server
        target: 5000
        scale_up_threshold: 6000
        scale_down_threshold: 3000
      - type: cpu_usage
        target: 60%
        scale_up_threshold: 80%
        scale_down_threshold: 30%
    scale_up:
      increment: 1
      cooldown: 120s
    scale_down:
      decrement: 1
      cooldown: 600s
      
  redis_cluster:
    min_nodes: 3
    max_nodes: 9
    target_metrics:
      - type: memory_usage
        target: 70%
        scale_up_threshold: 85%
      - type: ops_per_second
        target: 100000
        scale_up_threshold: 120000
    rebalancing:
      strategy: "consistent_hashing"
      min_replicas: 2
```

### 6. Performance Monitoring

```go
type PerformanceMonitor struct {
    metrics     *MetricsCollector
    alerts      *AlertManager
    dashboards  *DashboardService
}

func (p *PerformanceMonitor) CollectMetrics() {
    // Template performance
    p.metrics.RecordHistogram("template.render_time", 
        []string{"template_id", "language"})
    p.metrics.RecordCounter("template.cache_hits", 
        []string{"template_id"})
    
    // Delivery performance  
    p.metrics.RecordHistogram("notification.delivery_time",
        []string{"channel", "priority"})
    p.metrics.RecordGauge("notification.queue_depth",
        []string{"queue_name"})
    
    // Socket performance
    p.metrics.RecordGauge("socket.active_connections",
        []string{"server_id"})
    p.metrics.RecordHistogram("socket.message_latency",
        []string{"event_type"})
    
    // System performance
    p.metrics.RecordGauge("system.cpu_usage",
        []string{"service"})
    p.metrics.RecordGauge("system.memory_usage",
        []string{"service"})
}

// Alert rules
func (p *PerformanceMonitor) SetupAlerts() {
    p.alerts.AddRule(&AlertRule{
        Name: "high_delivery_latency",
        Query: `avg(notification_delivery_time) > 5000`,
        Duration: 5 * time.Minute,
        Severity: "warning",
        Actions: []string{"slack", "pagerduty"},
    })
    
    p.alerts.AddRule(&AlertRule{
        Name: "template_cache_miss_rate",
        Query: `rate(template_cache_hits[5m]) < 0.8`,
        Duration: 10 * time.Minute,
        Severity: "info",
        Actions: []string{"email"},
    })
}
```

## Tài liệu liên quan

- [Socket Module](./socket.md)
- [Email Integration](../integrations/email.md)
- [Queue System](../architecture/queue-system.md)
- [Template Engine](../features/template-engine.md)
- [Multi-tenancy](./tenant.md)
- [Analytics](../features/analytics.md)
- [Security Best Practices](../best-practices/security.md)
- [Performance Monitoring](../best-practices/performance.md)
- [Load Testing Guide](../testing/load-testing.md)