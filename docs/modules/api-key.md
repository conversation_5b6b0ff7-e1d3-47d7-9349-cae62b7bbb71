# API Key Module - <PERSON><PERSON>i liệu Tiếng Việt

## Tổng quan

API Key Module cung cấp hệ thống quản lý API keys toàn diện cho Blog API v3, cho phép mỗi website tạo và quản lý nhiều API keys với phân quyền chi tiết. Module hỗ trợ rate limiting, scoping, expiration, và audit logging cho từng API key.

## Mục tiêu

- **Multi-key Management**: Mỗi website có thể tạo nhiều API keys
- **Granular Permissions**: Phân quyền chi tiết cho từng API key
- **Rate Limiting**: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> requests theo từng key
- **Scoping**: Giới hạn phạm vi truy cập của API key
- **Security**: Mã hóa an toàn và rotation keys
- **Audit Trail**: Theo dõi sử dụng API key
- **Multi-tenancy**: Hoàn toàn tách biệt giữa các websites

## Kiến trúc hệ thống

### API Key Architecture

```mermaid
flowchart TD
    A[API Key Module] --> B[Key Management]
    A --> C[Authentication]
    A --> D[Authorization]
    A --> E[Rate Limiting]
    A --> F[Usage Tracking]
    A --> G[Key Rotation]
    
    B --> B1[Key Generation]
    B --> B2[Key Storage]
    B --> B3[Key Lifecycle]
    B --> B4[Key Metadata]
    
    C --> C1[Key Validation]
    C --> C2[Signature Verification]
    C --> C3[Expiration Check]
    C --> C4[Status Check]
    
    D --> D1[Permission Scopes]
    D --> D2[Resource Access]
    D --> D3[Action Permissions]
    D --> D4[IP Restrictions]
    
    E --> E1[Request Counting]
    E --> E2[Rate Enforcement]
    E --> E3[Quota Management]
    E --> E4[Burst Handling]
    
    F --> F1[Request Logging]
    F --> F2[Usage Analytics]
    F --> F3[Error Tracking]
    F --> F4[Performance Metrics]
    
    G --> G1[Key Renewal]
    G --> G2[Key Revocation]
    G --> G3[Key History]
    G --> G4[Emergency Rotation]
```

## Model Structures

### Database Schema

```mermaid
erDiagram
    API_KEY {
        uint id PK
        uint website_id FK
        string key_hash UK
        string key_prefix
        string name
        string description
        string status
        json permissions
        json scopes
        json ip_whitelist
        int rate_limit
        int rate_window
        datetime expires_at
        datetime last_used_at
        uint created_by
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    API_KEY_PERMISSION {
        uint id PK
        uint api_key_id FK
        string resource
        string action
        json conditions
        datetime created_at
    }
    
    API_KEY_USAGE {
        uint id PK
        uint website_id FK
        uint api_key_id FK
        string endpoint
        string method
        int response_code
        int response_time
        string ip_address
        string user_agent
        json metadata
        datetime created_at
    }
    
    API_KEY_ROTATION {
        uint id PK
        uint website_id FK
        uint api_key_id FK
        string old_key_hash
        string new_key_hash
        string reason
        uint rotated_by
        datetime rotated_at
    }
    
    WEBSITE ||--o{ API_KEY : "has"
    API_KEY ||--o{ API_KEY_PERMISSION : "has"
    API_KEY ||--o{ API_KEY_USAGE : "tracks"
    API_KEY ||--o{ API_KEY_ROTATION : "history"
```

### Models

```go
type APIKey struct {
    ID           uint                   `gorm:"primarykey" json:"id"`
    WebsiteID    uint                   `gorm:"not null;index" json:"website_id"`
    KeyHash      string                 `gorm:"uniqueIndex;not null" json:"-"`
    KeyPrefix    string                 `gorm:"size:10;index" json:"key_prefix"`
    Name         string                 `gorm:"size:100;not null" json:"name"`
    Description  string                 `gorm:"size:500" json:"description"`
    Status       string                 `gorm:"size:20;default:'active'" json:"status"`
    Permissions  datatypes.JSON         `gorm:"type:json" json:"permissions"`
    Scopes       datatypes.JSON         `gorm:"type:json" json:"scopes"`
    IPWhitelist  datatypes.JSON         `gorm:"type:json" json:"ip_whitelist"`
    RateLimit    int                    `gorm:"default:1000" json:"rate_limit"`
    RateWindow   int                    `gorm:"default:3600" json:"rate_window"` // seconds
    ExpiresAt    *time.Time             `json:"expires_at"`
    LastUsedAt   *time.Time             `json:"last_used_at"`
    CreatedBy    uint                   `gorm:"not null" json:"created_by"`
    CreatedAt    time.Time              `json:"created_at"`
    UpdatedAt    time.Time              `json:"updated_at"`
    DeletedAt    gorm.DeletedAt         `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relations
    Website      Website                `json:"website,omitempty"`
    Permissions  []APIKeyPermission     `json:"permissions_detail,omitempty"`
    Usage        []APIKeyUsage          `json:"usage,omitempty"`
}

type APIKeyPermission struct {
    ID          uint           `gorm:"primarykey" json:"id"`
    APIKeyID    uint           `gorm:"not null;index" json:"api_key_id"`
    Resource    string         `gorm:"size:100;not null" json:"resource"`
    Action      string         `gorm:"size:50;not null" json:"action"`
    Conditions  datatypes.JSON `gorm:"type:json" json:"conditions"`
    CreatedAt   time.Time      `json:"created_at"`
    
    // Relations
    APIKey      APIKey         `json:"api_key,omitempty"`
}

type APIKeyUsage struct {
    ID           uint           `gorm:"primarykey" json:"id"`
    WebsiteID    uint           `gorm:"not null;index" json:"website_id"`
    APIKeyID     uint           `gorm:"not null;index" json:"api_key_id"`
    Endpoint     string         `gorm:"size:255;not null" json:"endpoint"`
    Method       string         `gorm:"size:10;not null" json:"method"`
    ResponseCode int            `json:"response_code"`
    ResponseTime int            `json:"response_time"` // milliseconds
    IPAddress    string         `gorm:"size:45" json:"ip_address"`
    UserAgent    string         `gorm:"size:500" json:"user_agent"`
    Metadata     datatypes.JSON `gorm:"type:json" json:"metadata"`
    CreatedAt    time.Time      `json:"created_at"`
}
```

## Permission System

### Permission Scopes

```go
// Predefined scopes
const (
    ScopeReadPosts      = "posts:read"
    ScopeWritePosts     = "posts:write"
    ScopeDeletePosts    = "posts:delete"
    ScopeReadUsers      = "users:read"
    ScopeWriteUsers     = "users:write"
    ScopeReadComments   = "comments:read"
    ScopeWriteComments  = "comments:write"
    ScopeReadMedia      = "media:read"
    ScopeWriteMedia     = "media:write"
    ScopeReadAnalytics  = "analytics:read"
    ScopeWebhooks       = "webhooks:manage"
    ScopeAdmin          = "admin:full"
)

// Permission structure
type Permission struct {
    Resource   string                 `json:"resource"`
    Actions    []string               `json:"actions"`
    Conditions map[string]interface{} `json:"conditions,omitempty"`
}

// Example permissions configuration
var APIKeyPermissionPresets = map[string][]Permission{
    "read-only": {
        {Resource: "posts", Actions: []string{"read", "list"}},
        {Resource: "users", Actions: []string{"read", "list"}},
        {Resource: "comments", Actions: []string{"read", "list"}},
        {Resource: "media", Actions: []string{"read"}},
    },
    "content-manager": {
        {Resource: "posts", Actions: []string{"read", "list", "create", "update"}},
        {Resource: "media", Actions: []string{"read", "upload"}},
        {Resource: "comments", Actions: []string{"read", "list", "moderate"}},
    },
    "full-access": {
        {Resource: "*", Actions: []string{"*"}},
    },
}
```

### Permission Checking

```go
type PermissionChecker struct {
    cache *redis.Client
}

func (pc *PermissionChecker) CheckAPIKeyPermission(apiKey *APIKey, resource string, action string) bool {
    // Check if key is active
    if apiKey.Status != "active" {
        return false
    }
    
    // Check expiration
    if apiKey.ExpiresAt != nil && apiKey.ExpiresAt.Before(time.Now()) {
        return false
    }
    
    // Check permissions
    var permissions []Permission
    if err := json.Unmarshal(apiKey.Permissions, &permissions); err != nil {
        return false
    }
    
    for _, perm := range permissions {
        // Wildcard resource
        if perm.Resource == "*" || perm.Resource == resource {
            // Wildcard action
            if contains(perm.Actions, "*") || contains(perm.Actions, action) {
                // Check conditions if any
                if perm.Conditions != nil {
                    return pc.evaluateConditions(perm.Conditions)
                }
                return true
            }
        }
    }
    
    return false
}
```

## API Key Lifecycle

### 1. Key Generation Flow

```mermaid
sequenceDiagram
    participant Admin as Website Admin
    participant API as API Key Service
    participant DB as Database
    participant Cache as Redis Cache
    participant Audit as Audit Service
    
    Admin->>API: POST /api/cms/v1/api-keys
    Note over Admin,API: {name, description, permissions, expires_in}
    
    API->>API: Generate secure key
    API->>API: Hash key for storage
    API->>DB: Store key metadata
    DB->>API: Key saved
    
    API->>Cache: Cache key permissions
    API->>Audit: Log key creation
    
    API->>Admin: Return key (shown once)
    Note over Admin,API: {key: "sk_live_abc123...", id, prefix}
```

### 2. Key Authentication Flow

```mermaid
sequenceDiagram
    participant Client as API Client
    participant Gateway as API Gateway
    participant Auth as Auth Service
    participant Cache as Redis Cache
    participant DB as Database
    participant RateLimit as Rate Limiter
    
    Client->>Gateway: API Request
    Note over Client,Gateway: Header: X-API-Key: sk_live_abc123...
    
    Gateway->>Auth: Validate API key
    Auth->>Cache: Check cached key data
    
    alt Cache miss
        Auth->>DB: Get key from database
        DB->>Auth: Key data
        Auth->>Cache: Cache key data
    end
    
    Auth->>Auth: Verify key hash
    Auth->>Auth: Check expiration
    Auth->>Auth: Check IP whitelist
    Auth->>RateLimit: Check rate limit
    
    alt Rate limit exceeded
        Auth->>Gateway: 429 Too Many Requests
        Gateway->>Client: Rate limit error
    else
        Auth->>Gateway: Key valid + permissions
        Gateway->>Client: Process request
    end
```

### 3. Key Rotation Flow

```mermaid
stateDiagram-v2
    [*] --> Active : Key created
    Active --> Rotating : Initiate rotation
    Rotating --> GracePeriod : New key generated
    GracePeriod --> Deprecated : Grace period ends
    Deprecated --> Revoked : Old key revoked
    Revoked --> [*] : Key deleted
    
    Active --> Compromised : Security incident
    Compromised --> Revoked : Immediate revocation
    
    Active --> Expired : Expiration reached
    Expired --> Revoked : Auto-revoke
```

## Implementation Examples

### API Key Service

```go
type APIKeyService struct {
    db         *gorm.DB
    cache      *redis.Client
    hasher     *HashService
    generator  *KeyGenerator
}

// CreateAPIKey creates a new API key for a website
func (s *APIKeyService) CreateAPIKey(websiteID uint, req *CreateAPIKeyRequest) (*APIKeyResponse, error) {
    // Generate secure random key
    rawKey := s.generator.GenerateKey()
    keyPrefix := s.generator.ExtractPrefix(rawKey)
    keyHash := s.hasher.HashKey(rawKey)
    
    // Create API key record
    apiKey := &APIKey{
        WebsiteID:   websiteID,
        KeyHash:     keyHash,
        KeyPrefix:   keyPrefix,
        Name:        req.Name,
        Description: req.Description,
        Permissions: req.Permissions,
        Scopes:      req.Scopes,
        IPWhitelist: req.IPWhitelist,
        RateLimit:   req.RateLimit,
        RateWindow:  req.RateWindow,
        CreatedBy:   req.CreatedByUserID,
    }
    
    if req.ExpiresIn > 0 {
        expiresAt := time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
        apiKey.ExpiresAt = &expiresAt
    }
    
    // Save to database
    if err := s.db.Create(apiKey).Error; err != nil {
        return nil, err
    }
    
    // Cache permissions
    s.cacheAPIKeyData(apiKey)
    
    // Return response with raw key (shown once)
    return &APIKeyResponse{
        ID:          apiKey.ID,
        Key:         rawKey, // Only returned on creation
        KeyPrefix:   keyPrefix,
        Name:        apiKey.Name,
        Permissions: apiKey.Permissions,
        ExpiresAt:   apiKey.ExpiresAt,
    }, nil
}

// ValidateAPIKey validates an API key and returns its permissions
func (s *APIKeyService) ValidateAPIKey(rawKey string) (*APIKey, error) {
    keyHash := s.hasher.HashKey(rawKey)
    cacheKey := fmt.Sprintf("apikey:%s", keyHash)
    
    // Check cache first
    cached, err := s.cache.Get(cacheKey).Result()
    if err == nil {
        var apiKey APIKey
        if err := json.Unmarshal([]byte(cached), &apiKey); err == nil {
            return &apiKey, nil
        }
    }
    
    // Load from database
    var apiKey APIKey
    err = s.db.Where("key_hash = ? AND status = ?", keyHash, "active").
        First(&apiKey).Error
    if err != nil {
        return nil, errors.New("invalid api key")
    }
    
    // Check expiration
    if apiKey.ExpiresAt != nil && apiKey.ExpiresAt.Before(time.Now()) {
        return nil, errors.New("api key expired")
    }
    
    // Update last used
    s.db.Model(&apiKey).Update("last_used_at", time.Now())
    
    // Cache the key data
    s.cacheAPIKeyData(&apiKey)
    
    return &apiKey, nil
}
```

### Key Generator

```go
type KeyGenerator struct {
    prefix string
}

func NewKeyGenerator(environment string) *KeyGenerator {
    prefix := "sk_test_"
    if environment == "production" {
        prefix = "sk_live_"
    }
    return &KeyGenerator{prefix: prefix}
}

func (g *KeyGenerator) GenerateKey() string {
    // Generate 32 bytes of random data
    b := make([]byte, 32)
    if _, err := rand.Read(b); err != nil {
        panic(err)
    }
    
    // Encode to base64 URL-safe
    encoded := base64.URLEncoding.EncodeToString(b)
    
    // Add prefix
    return g.prefix + encoded
}

func (g *KeyGenerator) ExtractPrefix(key string) string {
    if len(key) < 10 {
        return ""
    }
    return key[:10]
}
```

### Rate Limiting

```go
type APIKeyRateLimiter struct {
    redis *redis.Client
}

func (rl *APIKeyRateLimiter) CheckRateLimit(apiKey *APIKey) (bool, *RateLimitInfo, error) {
    key := fmt.Sprintf("ratelimit:apikey:%d", apiKey.ID)
    
    // Sliding window rate limiting
    now := time.Now()
    window := time.Duration(apiKey.RateWindow) * time.Second
    windowStart := now.Add(-window)
    
    pipe := rl.redis.Pipeline()
    
    // Remove old entries
    pipe.ZRemRangeByScore(key, "0", fmt.Sprintf("%d", windowStart.Unix()))
    
    // Count requests in window
    pipe.ZCard(key)
    
    // Add current request
    pipe.ZAdd(key, &redis.Z{
        Score:  float64(now.Unix()),
        Member: fmt.Sprintf("%d", now.UnixNano()),
    })
    
    // Set expiration
    pipe.Expire(key, window+time.Minute)
    
    results, err := pipe.Exec()
    if err != nil {
        return false, nil, err
    }
    
    count := results[1].(*redis.IntCmd).Val()
    
    info := &RateLimitInfo{
        Limit:     apiKey.RateLimit,
        Remaining: apiKey.RateLimit - int(count),
        Reset:     now.Add(window).Unix(),
    }
    
    return count < int64(apiKey.RateLimit), info, nil
}
```

### Middleware

```go
func APIKeyAuthMiddleware(keyService *APIKeyService, rateLimiter *APIKeyRateLimiter) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get API key from header
        apiKey := c.GetHeader("X-API-Key")
        if apiKey == "" {
            apiKey = c.Query("api_key")
        }
        
        if apiKey == "" {
            c.JSON(401, gin.H{"error": "missing api key"})
            c.Abort()
            return
        }
        
        // Validate API key
        key, err := keyService.ValidateAPIKey(apiKey)
        if err != nil {
            c.JSON(401, gin.H{"error": "invalid api key"})
            c.Abort()
            return
        }
        
        // Check IP whitelist
        if key.IPWhitelist != nil {
            clientIP := c.ClientIP()
            if !checkIPWhitelist(clientIP, key.IPWhitelist) {
                c.JSON(403, gin.H{"error": "ip not allowed"})
                c.Abort()
                return
            }
        }
        
        // Check rate limit
        allowed, limitInfo, err := rateLimiter.CheckRateLimit(key)
        if err != nil || !allowed {
            c.Header("X-RateLimit-Limit", strconv.Itoa(limitInfo.Limit))
            c.Header("X-RateLimit-Remaining", strconv.Itoa(limitInfo.Remaining))
            c.Header("X-RateLimit-Reset", strconv.FormatInt(limitInfo.Reset, 10))
            
            c.JSON(429, gin.H{"error": "rate limit exceeded"})
            c.Abort()
            return
        }
        
        // Set context
        c.Set("api_key", key)
        c.Set("website_id", key.WebsiteID)
        c.Set("api_key_id", key.ID)
        
        // Track usage asynchronously
        go keyService.TrackUsage(key.ID, c.Request.URL.Path, c.Request.Method)
        
        c.Next()
    }
}
```

## API Endpoints

### API Key Management

#### Create API Key
```http
POST /api/cms/v1/api-keys
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Production API Key",
  "description": "Key for production mobile app",
  "permissions": [
    {
      "resource": "posts",
      "actions": ["read", "list"],
      "conditions": {
        "status": "published"
      }
    },
    {
      "resource": "users",
      "actions": ["read"]
    }
  ],
  "scopes": ["posts:read", "users:read"],
  "ip_whitelist": ["***********/24", "********"],
  "rate_limit": 10000,
  "rate_window": 3600,
  "expires_in": 31536000
}
```

**Response:**
```json
{
  "id": 1,
  "key": "sk_live_abcdefghijklmnopqrstuvwxyz123456",
  "key_prefix": "sk_live_ab",
  "name": "Production API Key",
  "permissions": [...],
  "expires_at": "2025-07-14T10:00:00Z",
  "created_at": "2024-07-14T10:00:00Z"
}
```

#### List API Keys
```http
GET /api/cms/v1/api-keys?status=active&cursor=abc123&limit=20
Authorization: Bearer {admin_token}
```

#### Get API Key Details
```http
GET /api/cms/v1/api-keys/{key_id}
Authorization: Bearer {admin_token}
```

#### Update API Key
```http
PUT /api/cms/v1/api-keys/{key_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Updated Key Name",
  "description": "Updated description",
  "permissions": [...],
  "ip_whitelist": ["***********/24"],
  "rate_limit": 5000
}
```

#### Rotate API Key
```http
POST /api/cms/v1/api-keys/{key_id}/rotate
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "reason": "Regular rotation",
  "grace_period": 86400
}
```

#### Revoke API Key
```http
POST /api/cms/v1/api-keys/{key_id}/revoke
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "reason": "No longer needed"
}
```

### Usage Analytics

#### Get API Key Usage
```http
GET /api/cms/v1/api-keys/{key_id}/usage?period=7d
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "summary": {
    "total_requests": 15420,
    "successful_requests": 15000,
    "failed_requests": 420,
    "average_response_time": 145
  },
  "by_endpoint": [
    {
      "endpoint": "/api/cms/v1/posts",
      "method": "GET",
      "count": 8500,
      "avg_response_time": 120
    }
  ],
  "by_status_code": {
    "200": 14500,
    "404": 300,
    "429": 120,
    "500": 0
  },
  "timeline": [
    {
      "timestamp": "2024-07-14T00:00:00Z",
      "requests": 2100,
      "errors": 50
    }
  ]
}
```

## Security Best Practices

### Key Security
- **Secure Generation**: Use cryptographically secure random generation
- **One-time Display**: Show keys only once during creation
- **Secure Storage**: Store only hashed versions of keys
- **Regular Rotation**: Implement key rotation policies
- **Audit Logging**: Log all key operations

### Permission Security
- **Least Privilege**: Grant minimum required permissions
- **Resource Scoping**: Limit access to specific resources
- **Action Control**: Define allowed actions explicitly
- **Condition Validation**: Implement conditional access rules
- **Regular Review**: Periodically review key permissions

### Implementation Security
```go
// Secure key storage
func hashAPIKey(key string) string {
    hash := sha256.Sum256([]byte(key))
    return hex.EncodeToString(hash[:])
}

// IP whitelist validation
func checkIPWhitelist(clientIP string, whitelist []string) bool {
    ip := net.ParseIP(clientIP)
    if ip == nil {
        return false
    }
    
    for _, allowed := range whitelist {
        if strings.Contains(allowed, "/") {
            _, ipnet, err := net.ParseCIDR(allowed)
            if err == nil && ipnet.Contains(ip) {
                return true
            }
        } else if allowed == clientIP {
            return true
        }
    }
    
    return false
}
```

## Configuration

### API Key Module Configuration
```yaml
api_key:
  enabled: true
  
  generation:
    key_length: 32
    prefix_production: "sk_live_"
    prefix_development: "sk_test_"
    
  security:
    max_keys_per_website: 50
    default_rate_limit: 1000
    default_rate_window: 3600
    max_expiration_days: 365
    
  rotation:
    enabled: true
    grace_period: 86400 # 24 hours
    notify_before_expiry: 604800 # 7 days
    
  usage_tracking:
    enabled: true
    retention_days: 90
    
  rate_limiting:
    algorithm: "sliding_window"
    redis_prefix: "ratelimit:apikey:"
```

## Monitoring & Alerts

### Key Metrics
- **Usage Patterns**: Track API key usage trends
- **Error Rates**: Monitor authentication failures
- **Rate Limit Hits**: Track rate limit violations
- **Performance**: API key validation latency
- **Security Events**: Suspicious activity detection

### Alert Conditions
```yaml
alerts:
  - name: "High API Key Failure Rate"
    condition: "failure_rate > 10%"
    window: "5m"
    severity: "warning"
    
  - name: "API Key Approaching Expiration"
    condition: "expires_in < 7d"
    severity: "info"
    
  - name: "Suspicious API Key Usage"
    condition: "requests_from_new_ip > 100"
    window: "1h"
    severity: "critical"
```

## Best Practices

### Management Best Practices
- **Descriptive Names**: Use clear, descriptive key names
- **Documentation**: Document key purpose and usage
- **Regular Audits**: Review active keys periodically
- **Cleanup**: Remove unused or expired keys
- **Tenant Isolation**: Ensure complete separation between websites

### Security Best Practices
- **Environment Separation**: Different keys for dev/staging/prod
- **Rotation Policy**: Regular key rotation schedule
- **Monitoring**: Active monitoring of key usage
- **Incident Response**: Quick revocation procedures
- **Access Control**: Limit who can create/manage keys

### Integration Best Practices
- **Error Handling**: Graceful handling of key errors
- **Retry Logic**: Implement exponential backoff
- **Circuit Breakers**: Prevent cascading failures
- **Caching**: Cache key validation results
- **Documentation**: Provide clear API documentation

## Tài liệu liên quan

- [Auth Module](./auth.md)
- [RBAC Module](./rbac.md)
- [Tenant Module](./tenant.md)
- [Security Best Practices](../best-practices/security.md)
- [API Documentation](../api/overview.md)