# Module Socket - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Module Socket cung cấp hệ thống real-time communication cho ứng dụng blog, bao gồm thông báo real-time, chat trự<PERSON> tuyến, và các tương tác real-time khác thông qua WebSocket connections.

## Mục tiêu

- **Real-time Notifications**: Thông báo tức thì cho người dùng
- **Live Chat**: Hệ thống chat trực tuyến giữa users
- **Live Comments**: Comment real-time trên bài viết
- **Presence Status**: Hiển thị trạng thái online/offline
- **Live Updates**: Cập nhật nội dung real-time
- **Collaborative Features**: Tính năng cộng tác real-time

## Tính năng chính

- **WebSocket Management**: <PERSON>u<PERSON>n lý kết nối WebSocket
- **Room-based Communication**: <PERSON><PERSON><PERSON> tiếp theo room/channel
- **Message Broadcasting**: <PERSON><PERSON><PERSON> tin nhắn đến nhiều user
- **Private Messaging**: Tin nhắn riêng tư 1-1
- **Group Chat**: Chat nhóm
- **Typing Indicators**: Hiển thị đang gõ
- **Message History**: Lịch sử tin nhắn
- **File Sharing**: Chia sẻ file trong chat
- **Message Reactions**: Reaction tin nhắn

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/socket/
├── models/                   # Các model socket
│   ├── connection.go        # Model kết nối
│   ├── room.go              # Model room/channel
│   ├── message.go           # Model tin nhắn
│   └── presence.go          # Model trạng thái online
├── services/                # Logic nghiệp vụ
│   ├── socket_service.go    # Dịch vụ WebSocket
│   ├── room_service.go      # Dịch vụ room management
│   ├── message_service.go   # Dịch vụ tin nhắn
│   ├── presence_service.go  # Dịch vụ presence
│   └── broadcast_service.go # Dịch vụ broadcast
├── handlers/                # WebSocket handlers
│   ├── socket_handler.go    # Handler chính
│   ├── chat_handler.go      # Handler chat
│   └── notification_handler.go # Handler thông báo
├── hub/                     # Connection hub
│   ├── connection_hub.go    # Quản lý connections
│   ├── room_hub.go          # Quản lý rooms
│   └── broadcast_hub.go     # Broadcast messages
├── events/                  # Event types
│   ├── chat_events.go       # Event chat
│   ├── notification_events.go # Event thông báo
│   └── presence_events.go   # Event presence
└── middleware/              # WebSocket middleware
    ├── auth_middleware.go   # Xác thực WebSocket
    └── rate_limit_middleware.go # Rate limiting
```

## Mô hình dữ liệu

### Connection
- **ID**: Định danh kết nối
- **UserID**: ID người dùng
- **TenantID**: ID tenant
- **WebsiteID**: ID website
- **SocketID**: ID WebSocket connection
- **Status**: Trạng thái (connected, disconnected)
- **LastSeen**: Lần cuối online
- **UserAgent**: Thông tin browser/device
- **IPAddress**: Địa chỉ IP

### Room
- **ID**: Định danh room
- **TenantID**: ID tenant
- **WebsiteID**: ID website
- **Name**: Tên room
- **Type**: Loại room (chat, notification, post_comments)
- **IsPrivate**: Room riêng tư hay công khai
- **MaxMembers**: Số thành viên tối đa
- **CreatedBy**: ID người tạo
- **Settings**: Cài đặt room (JSON)

### ChatMessage
- **ID**: Định danh tin nhắn
- **RoomID**: ID room
- **WebsiteID**: ID website
- **UserID**: ID người gửi
- **Content**: Nội dung tin nhắn
- **Type**: Loại tin nhắn (text, image, file, system)
- **ReplyToID**: ID tin nhắn được reply
- **EditedAt**: Thời gian chỉnh sửa
- **Reactions**: Reactions (JSON)
- **Status**: Trạng thái (sent, delivered, read)

### RoomMember
- **RoomID**: ID room
- **UserID**: ID thành viên
- **Role**: Vai trò (member, moderator, admin)
- **JoinedAt**: Thời gian tham gia
- **LastReadAt**: Lần cuối đọc tin nhắn
- **Notifications**: Cài đặt thông báo

## Luồng hoạt động chính

### 1. Kết nối WebSocket

```mermaid
sequenceDiagram
    participant Client as Client Browser
    participant Gateway as WebSocket Gateway
    participant Auth as Auth Service
    participant Hub as Connection Hub
    participant DB as Database
    
    Client->>Gateway: WebSocket connection
    Gateway->>Auth: Validate JWT token
    Auth->>Gateway: User authenticated
    Gateway->>Hub: Register connection
    Hub->>DB: Store connection info
    Hub->>Gateway: Connection registered
    Gateway->>Client: Connection established
    
    Note over Client,DB: User now online and ready for real-time communication
```

### 2. Tham gia Room

```mermaid
flowchart TD
    A[User request join room] --> B{Room exists?}
    B -->|Không| C[Create room]
    B -->|Có| D{User has permission?}
    C --> E[Add user as member]
    D -->|Không| F[Reject request]
    D -->|Có| G{Already member?}
    G -->|Có| H[Return room info]
    G -->|Không| E
    E --> I[Update member list]
    I --> J[Broadcast user joined]
    J --> K[Send room history]
    F --> L[Send error message]
    H --> M[Send current status]
    K --> N[User in room]
    M --> N
```

### 3. Gửi tin nhắn Chat

```mermaid
sequenceDiagram
    participant User as User A
    participant Gateway as WebSocket Gateway
    participant MessageService as Message Service
    participant RoomHub as Room Hub
    participant DB as Database
    participant Users as Other Users
    
    User->>Gateway: Send message
    Gateway->>MessageService: Process message
    MessageService->>DB: Save message
    MessageService->>RoomHub: Broadcast to room
    RoomHub->>Users: Deliver message
    MessageService->>Gateway: Confirm sent
    Gateway->>User: Message sent confirmation
    
    Note over Users: Real-time message delivery
```

### 4. Real-time Notifications

```mermaid
flowchart LR
    A[Event Trigger] --> B[Notification Service]
    B --> C[User Preferences]
    C --> D{WebSocket Connected?}
    D -->|Có| E[Send Real-time]
    D -->|Không| F[Queue for later]
    E --> G[Display Notification]
    F --> H[Send when online]
    H --> G
```

## Các loại Event

### Chat Events
- **message.send**: Gửi tin nhắn
- **message.edit**: Chỉnh sửa tin nhắn
- **message.delete**: Xóa tin nhắn
- **message.react**: Reaction tin nhắn
- **typing.start**: Bắt đầu gõ
- **typing.stop**: Dừng gõ

### Room Events
- **room.join**: Tham gia room
- **room.leave**: Rời room
- **room.create**: Tạo room mới
- **room.update**: Cập nhật room
- **member.add**: Thêm thành viên
- **member.remove**: Xóa thành viên

### Presence Events
- **user.online**: User online
- **user.offline**: User offline
- **user.away**: User away
- **user.busy**: User busy

### Notification Events
- **notification.new**: Thông báo mới
- **notification.read**: Đã đọc thông báo
- **notification.clear**: Xóa thông báo

## WebSocket Protocol

### Connection Authentication
```json
{
  "type": "auth",
  "data": {
    "token": "jwt_token_here",
    "user_id": 123
  }
}
```

### Message Format
```json
{
  "id": "msg_uuid",
  "type": "message.send",
  "room_id": "room_123",
  "data": {
    "content": "Hello everyone!",
    "message_type": "text",
    "reply_to": null
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Event Broadcasting
```json
{
  "type": "message.received",
  "room_id": "room_123", 
  "data": {
    "id": "msg_456",
    "user": {
      "id": 123,
      "name": "John Doe",
      "avatar": "https://example.com/avatar.jpg"
    },
    "content": "Hello everyone!",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## Chat System

### Private Chat (1-1)

```mermaid
sequenceDiagram
    participant UserA as User A
    participant System as Chat System
    participant UserB as User B
    
    UserA->>System: Start chat with User B
    System->>System: Create private room
    System->>UserA: Room created
    System->>UserB: Chat invitation
    UserB->>System: Accept invitation
    System->>UserA: User B joined
    
    UserA->>System: Send message
    System->>UserB: Deliver message
    UserB->>System: Send reply
    System->>UserA: Deliver reply
```

### Group Chat

```mermaid
flowchart TD
    A[Create Group Chat] --> B[Invite Members]
    B --> C[Members Join]
    C --> D[Chat Active]
    
    D --> E[Send Message]
    E --> F[Broadcast to All]
    F --> G[Message History]
    
    D --> H[Add Member]
    H --> I[Update Member List]
    I --> F
    
    D --> J[Remove Member]
    J --> K[Update Permissions]
    K --> F
```

### Typing Indicators

```mermaid
sequenceDiagram
    participant UserA as User A (Typing)
    participant System as System
    participant UserB as User B
    participant UserC as User C
    
    UserA->>System: typing.start
    System->>UserB: User A is typing...
    System->>UserC: User A is typing...
    
    Note over UserA: User continues typing
    
    UserA->>System: typing.stop
    System->>UserB: User A stopped typing
    System->>UserC: User A stopped typing
    
    UserA->>System: Send message
    System->>UserB: Message from User A
    System->>UserC: Message from User A
```

## Notification System

### Real-time Notification Delivery

```mermaid
flowchart TD
    A[Event Occurs] --> B[Notification Service]
    B --> C[Get User Preferences]
    C --> D{User Online?}
    
    D -->|Online| E[Check WebSocket]
    D -->|Offline| F[Store for Later]
    
    E --> G{Socket Connected?}
    G -->|Yes| H[Send Real-time]
    G -->|No| I[Send Push/Email]
    
    H --> J[Mark as Delivered]
    I --> K[Queue for Retry]
    F --> L[Send when Online]
```

### Notification Types

#### Comment Notifications
```json
{
  "type": "notification.comment",
  "data": {
    "post_id": 123,
    "post_title": "My Blog Post",
    "commenter": {
      "id": 456,
      "name": "Jane Doe"
    },
    "comment": "Great article!",
    "url": "/posts/my-blog-post#comment-789"
  }
}
```

#### Follow Notifications
```json
{
  "type": "notification.follow",
  "data": {
    "follower": {
      "id": 789,
      "name": "New Follower",
      "avatar": "https://example.com/avatar.jpg"
    },
    "message": "started following you"
  }
}
```

## Presence System

### User Presence States

```mermaid
stateDiagram-v2
    [*] --> Offline
    Offline --> Online: Connect
    Online --> Away: Idle timeout
    Online --> Busy: Manual set
    Online --> Offline: Disconnect
    Away --> Online: Activity detected
    Away --> Offline: Disconnect
    Busy --> Online: Manual unset
    Busy --> Offline: Disconnect
```

### Presence Broadcasting

```mermaid
flowchart LR
    A[User Status Change] --> B[Presence Service]
    B --> C[Update Database]
    C --> D[Get Friends List]
    D --> E[Broadcast to Friends]
    E --> F[Update UI Status]
```

## Performance Optimization

### Connection Scaling

```mermaid
flowchart TD
    A[Load Balancer] --> B[WebSocket Server 1]
    A --> C[WebSocket Server 2]
    A --> D[WebSocket Server 3]
    
    B --> E[Redis Pub/Sub]
    C --> E
    D --> E
    
    E --> F[Message Broadcasting]
    F --> G[Cross-server Communication]
```

### Message Queuing

```mermaid
flowchart LR
    A[High Traffic] --> B[Message Queue]
    B --> C[Worker 1]
    B --> D[Worker 2] 
    B --> E[Worker 3]
    
    C --> F[Process Messages]
    D --> F
    E --> F
    
    F --> G[Deliver to Users]
```

## Security Features

### Authentication & Authorization

```mermaid
flowchart TD
    A[WebSocket Connect] --> B[Validate JWT]
    B --> C{Token Valid?}
    C -->|No| D[Reject Connection]
    C -->|Yes| E[Check Permissions]
    E --> F{Has Permission?}
    F -->|No| G[Limited Access]
    F -->|Yes| H[Full Access]
    
    H --> I[Join Authorized Rooms]
    G --> J[Join Public Rooms Only]
```

### Rate Limiting

```mermaid
flowchart LR
    A[Message Sent] --> B[Rate Limiter]
    B --> C{Within Limit?}
    C -->|Yes| D[Process Message]
    C -->|No| E[Drop Message]
    E --> F[Send Rate Limit Warning]
    D --> G[Deliver Message]
```

## API Endpoints

### HTTP Endpoints (REST fallback)
- `GET /api/v1/socket/rooms` - Danh sách rooms
- `POST /api/v1/socket/rooms` - Tạo room mới
- `GET /api/v1/socket/rooms/{id}/messages` - Lịch sử tin nhắn
- `POST /api/v1/socket/rooms/{id}/join` - Tham gia room
- `DELETE /api/v1/socket/rooms/{id}/leave` - Rời room

### WebSocket Events
- `message.send` - Gửi tin nhắn
- `room.join` - Tham gia room
- `room.leave` - Rời room
- `typing.start` - Bắt đầu gõ
- `typing.stop` - Dừng gõ
- `presence.update` - Cập nhật trạng thái

## Monitoring và Analytics

### Connection Metrics

```mermaid
graph LR
    A[Socket Metrics] --> B[Active Connections]
    A --> C[Message Volume]
    A --> D[Room Activity]
    A --> E[User Engagement]
    A --> F[Error Rates]
    
    B --> B1[Current Online]
    B --> B2[Peak Concurrent]
    
    C --> C1[Messages/sec]
    C --> C2[Daily Volume]
    
    D --> D1[Active Rooms]
    D --> D2[Room Participation]
    
    E --> E1[Session Duration]
    E --> E2[Return Rate]
    
    F --> F1[Connection Errors]
    F --> F2[Message Failures]
```

### Performance Monitoring
- **Connection Latency**: Độ trễ kết nối
- **Message Delivery Time**: Thời gian gửi tin nhắn
- **Room Join/Leave Rate**: Tỷ lệ vào/ra room
- **Memory Usage**: Sử dụng bộ nhớ
- **CPU Usage**: Sử dụng CPU
- **Website Isolation**: Monitor cross-website access attempts

## Deployment Architecture

### Single Server Setup
```mermaid
flowchart TD
    A[Client Browsers] --> B[Nginx Load Balancer]
    B --> C[Go WebSocket Server]
    C --> D[Redis Pub/Sub]
    C --> E[MySQL Database]
    C --> F[File Storage]
```

### Multi-Server Setup
```mermaid
flowchart TD
    A[Client Browsers] --> B[Load Balancer]
    B --> C[WebSocket Server 1]
    B --> D[WebSocket Server 2]
    B --> E[WebSocket Server 3]
    
    C --> F[Redis Cluster]
    D --> F
    E --> F
    
    F --> G[Message Synchronization]
    
    C --> H[Database Cluster]
    D --> H
    E --> H
```

## Multi-Tenancy & Website Isolation

### Website-based Socket Management

```mermaid
flowchart TD
    A[Socket Connection] --> B[Extract Website Context]
    B --> C[Validate Website Access]
    C --> D[Join Website-specific Rooms]
    D --> E[Set Website Permissions]
    E --> F[Enable Website Communication]
    
    G[Message Broadcast] --> H[Check Website Scope]
    H --> I[Filter Recipients by Website]
    I --> J[Send to Website Users Only]
```

### Socket Service với Website Isolation

```go
type WebsiteSocketService struct {
    hub       *SocketHub
    websiteID uint
    userPerms UserPermissionService
}

func (s *WebsiteSocketService) JoinRoom(userID uint, roomName string) error {
    // Validate user has access to website
    if !s.userPerms.HasWebsiteAccess(userID, s.websiteID) {
        return errors.New("access denied to website")
    }
    
    // Create website-scoped room name
    scopedRoomName := fmt.Sprintf("website:%d:%s", s.websiteID, roomName)
    
    // Join the room
    return s.hub.JoinRoom(userID, scopedRoomName)
}

func (s *WebsiteSocketService) BroadcastToWebsite(message *Message) error {
    // Ensure message is scoped to website
    message.WebsiteID = s.websiteID
    
    // Broadcast to all website users
    roomName := fmt.Sprintf("website:%d:broadcast", s.websiteID)
    return s.hub.BroadcastToRoom(roomName, message)
}
```

### Website-specific Namespaces

```javascript
// Client-side connection with website context
const socket = io('https://api.yourblog.com', {
  query: {
    website_id: websiteId,
    user_id: userId,
    token: authToken
  },
  transports: ['websocket']
});

// Auto-join website-specific rooms
socket.on('connect', () => {
  socket.emit('join_website_room', {
    website_id: websiteId,
    room_type: 'general'
  });
});
```

## Tích hợp với các module khác

### Blog Module
- **Live Comments**: Comment real-time trên bài viết per website
- **Post Notifications**: Thông báo bài viết mới trong website
- **Collaborative Editing**: Chỉnh sửa bài viết cùng lúc với website context

### Auth Module
- **Presence Status**: Trạng thái online của users per website
- **Permission Checking**: Kiểm tra quyền real-time với website scope
- **Session Management**: Quản lý phiên WebSocket per website

### Notification Module
- **Real-time Delivery**: Gửi thông báo tức thì per website
- **Read Status Sync**: Đồng bộ trạng thái đã đọc per website
- **Preference Updates**: Cập nhật cài đặt real-time per website

### Tenant Module
- **Website Isolation**: Rooms riêng cho mỗi website
- **Website Notifications**: Thông báo theo website
- **Resource Limits**: Giới hạn kết nối theo website plan

## Best Practices

### Connection Management
- **Graceful Disconnection**: Xử lý ngắt kết nối mượt mà
- **Reconnection Logic**: Tự động kết nối lại với website context
- **Heartbeat Mechanism**: Kiểm tra kết nối định kỳ
- **Connection Pooling**: Quản lý pool kết nối per website

### Message Handling
- **Message Queuing**: Xếp hàng tin nhắn với website context
- **Delivery Guarantee**: Đảm bảo gửi tin nhắn trong website scope
- **Duplicate Prevention**: Tránh tin nhắn trùng per website
- **Message Ordering**: Đảm bảo thứ tự tin nhắn per website

### Scalability
- **Horizontal Scaling**: Mở rộng theo chiều ngang với website isolation
- **Load Distribution**: Phân tải đều theo website
- **State Management**: Quản lý state phân tán per website
- **Cache Strategy**: Chiến lược cache hiệu quả với website keys

### Website Isolation
- **Namespace Separation**: Tách biệt namespaces per website
- **Room Scoping**: Rooms luôn có website prefix
- **Message Filtering**: Lọc tin nhắn theo website
- **Permission Validation**: Kiểm tra quyền website trong real-time

### Example Implementation
```go
type WebsiteSocketHandler struct {
    hub       *SocketHub
    websiteID uint
}

func (h *WebsiteSocketHandler) HandleConnection(conn *websocket.Conn, userID uint) {
    // Validate website access
    if !h.validateWebsiteAccess(userID, h.websiteID) {
        conn.Close()
        return
    }
    
    // Create website-scoped connection
    client := &SocketClient{
        ID:        generateID(),
        UserID:    userID,
        WebsiteID: h.websiteID,
        Conn:      conn,
        Send:      make(chan []byte, 256),
    }
    
    // Register client
    h.hub.Register <- client
    
    // Auto-join website room
    websiteRoom := fmt.Sprintf("website:%d", h.websiteID)
    h.hub.JoinRoom(client.ID, websiteRoom)
    
    // Start goroutines
    go client.writePump()
    go client.readPump(h.hub)
}
```

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Notification Module](./notification.md)
- [Auth Module](./auth.md)
- [Performance Best Practices](../best-practices/performance.md)
- [Security Guidelines](../best-practices/security.md)