# SEO Module - <PERSON><PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

SEO Module cung cấp các tính năng tối ưu hóa công cụ tìm kiếm (Search Engine Optimization) cho Blog API v3, bao gồm quản lý meta tags, sitemap generation, structured data, và các công cụ phân tích SEO.

## Mục tiêu

- **Search Engine Optimization**: Tối ưu hóa content cho search engines
- **Meta Tag Management**: Quản lý meta tags cho tất cả content
- **Sitemap Generation**: Tự động tạo XML sitemaps
- **Structured Data**: Schema.org markup cho rich snippets
- **SEO Analytics**: Phân tích hiệu suất SEO
- **URL Optimization**: Friendly URLs và redirects management

## Kiến trúc hệ thống

### SEO Architecture Overview

```mermaid
flowchart TD
    A[SEO Module] --> B[Meta Management]
    A --> C[Sitemap Generator]
    A --> D[Structured Data]
    A --> E[URL Management]
    A --> F[SEO Analytics]
    A --> G[Content Analysis]
    
    B --> B1[Page Meta]
    B --> B2[Open Graph]
    B --> B3[Twitter Cards]
    B --> B4[Custom Meta]
    
    C --> C1[XML Sitemap]
    C --> C2[Image Sitemap]
    C --> C3[News Sitemap]
    C --> C4[Video Sitemap]
    
    D --> D1[Article Schema]
    D --> D2[Organization Schema]
    D --> D3[Breadcrumb Schema]
    D --> D4[FAQ Schema]
    
    E --> E1[URL Rewrites]
    E --> E2[Redirects]
    E --> E3[Canonical URLs]
    E --> E4[Pretty URLs]
```

### Components

#### Meta Management
- **Page Meta Tags**: Title, description, keywords
- **Open Graph**: Facebook sharing optimization
- **Twitter Cards**: Twitter sharing optimization
- **Custom Meta**: Module-specific meta tags

#### Sitemap Generation
- **XML Sitemaps**: Standard website structure
- **Image Sitemaps**: Image content indexing
- **News Sitemaps**: News content optimization
- **Video Sitemaps**: Video content indexing

#### Structured Data
- **Schema.org Markup**: Rich snippets support
- **JSON-LD**: JavaScript-based structured data
- **Microdata**: HTML-embedded structured data
- **RDFa**: Resource Description Framework

## Model Structures

### SEO Meta Model

```go
type SEOMeta struct {
    ID               uint      `gorm:"primarykey" json:"id"`
    ContentType      string    `gorm:"size:50;not null" json:"content_type"` // post, page, category
    ContentID        uint      `gorm:"not null;index" json:"content_id"`
    
    // Basic Meta
    MetaTitle        string    `gorm:"size:70" json:"meta_title"`
    MetaDescription  string    `gorm:"size:160" json:"meta_description"`
    MetaKeywords     string    `gorm:"size:255" json:"meta_keywords"`
    CanonicalURL     string    `gorm:"size:255" json:"canonical_url"`
    
    // Open Graph
    OGTitle          string    `gorm:"size:95" json:"og_title"`
    OGDescription    string    `gorm:"size:297" json:"og_description"`
    OGImage          string    `gorm:"size:255" json:"og_image"`
    OGType           string    `gorm:"size:50" json:"og_type"`
    OGLocale         string    `gorm:"size:10" json:"og_locale"`
    
    // Twitter Cards
    TwitterCard      string    `gorm:"size:50" json:"twitter_card"`
    TwitterTitle     string    `gorm:"size:70" json:"twitter_title"`
    TwitterDescription string  `gorm:"size:200" json:"twitter_description"`
    TwitterImage     string    `gorm:"size:255" json:"twitter_image"`
    TwitterCreator   string    `gorm:"size:50" json:"twitter_creator"`
    
    // SEO Settings
    NoIndex          bool      `gorm:"default:false" json:"no_index"`
    NoFollow         bool      `gorm:"default:false" json:"no_follow"`
    NoSnippet        bool      `gorm:"default:false" json:"no_snippet"`
    NoArchive        bool      `gorm:"default:false" json:"no_archive"`
    
    // Advanced
    CustomMeta       JSON      `gorm:"type:json" json:"custom_meta"`
    StructuredData   JSON      `gorm:"type:json" json:"structured_data"`
    
    CreatedAt        time.Time `json:"created_at"`
    UpdatedAt        time.Time `json:"updated_at"`
}
```

### URL Redirect Model

```go
type URLRedirect struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    FromURL     string    `gorm:"size:255;not null;uniqueIndex" json:"from_url"`
    ToURL       string    `gorm:"size:255;not null" json:"to_url"`
    StatusCode  int       `gorm:"default:301" json:"status_code"` // 301, 302, 307, 308
    Type        string    `gorm:"size:20;default:'manual'" json:"type"` // manual, auto, temporary
    HitCount    uint      `gorm:"default:0" json:"hit_count"`
    LastHit     *time.Time `json:"last_hit"`
    IsActive    bool      `gorm:"default:true" json:"is_active"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### SEO Audit Model

```go
type SEOAudit struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ContentType string    `gorm:"size:50;not null" json:"content_type"`
    ContentID   uint      `gorm:"not null;index" json:"content_id"`
    URL         string    `gorm:"size:255;not null" json:"url"`
    
    // SEO Scores
    OverallScore    float64   `json:"overall_score"`
    TitleScore      float64   `json:"title_score"`
    DescriptionScore float64  `json:"description_score"`
    ContentScore    float64   `json:"content_score"`
    TechnicalScore  float64   `json:"technical_score"`
    
    // Issues
    Issues      JSON      `gorm:"type:json" json:"issues"`
    Suggestions JSON      `gorm:"type:json" json:"suggestions"`
    
    // Content Analysis
    WordCount       int       `json:"word_count"`
    ReadingTime     int       `json:"reading_time"` // in minutes
    ReadabilityScore float64  `json:"readability_score"`
    KeywordDensity  JSON      `gorm:"type:json" json:"keyword_density"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

## SEO Features

### 1. Meta Tag Management

#### Automatic Meta Generation
```mermaid
sequenceDiagram
    participant Content as Content Creation
    participant SEO as SEO Module
    participant AI as AI Generator
    participant DB as Database
    
    Content->>SEO: New content created
    SEO->>AI: Generate meta suggestions
    AI->>SEO: Return meta recommendations
    SEO->>DB: Save default meta
    SEO->>Content: Return meta data
```

#### Meta Tag Types
- **Basic Meta**: Title, description, keywords, canonical
- **Open Graph**: Facebook sharing optimization
- **Twitter Cards**: Twitter sharing enhancement
- **Custom Meta**: Module-specific meta tags
- **Multilingual Meta**: Language-specific meta tags

### 2. Sitemap Generation

#### XML Sitemap Structure
```mermaid
flowchart TD
    A[Main Sitemap Index] --> B[Posts Sitemap]
    A --> C[Pages Sitemap]
    A --> D[Categories Sitemap]
    A --> E[Tags Sitemap]
    A --> F[Authors Sitemap]
    A --> G[Images Sitemap]
    
    B --> B1[Recent Posts]
    B --> B2[Archived Posts]
    
    C --> C1[Static Pages]
    C --> C2[Dynamic Pages]
    
    G --> G1[Featured Images]
    G --> G2[Gallery Images]
    G --> G3[Content Images]
```

#### Sitemap Features
- **Automatic Generation**: Auto-update khi có content mới
- **Priority Calculation**: Automatic priority based on content importance
- **Change Frequency**: Dynamic frequency based on update patterns
- **Multi-language Support**: Hreflang annotations
- **Image/Video Sitemaps**: Media-specific sitemaps

### 3. Structured Data

#### Schema.org Implementation
```yaml
supported_schemas:
  article:
    - headline
    - datePublished
    - dateModified
    - author
    - publisher
    - image
    - articleBody
    
  organization:
    - name
    - url
    - logo
    - contactPoint
    - sameAs
    
  breadcrumb:
    - itemListElement
    - position
    - name
    - item
    
  faq:
    - mainEntity
    - question
    - acceptedAnswer
    
  review:
    - itemReviewed
    - reviewRating
    - author
    - datePublished
```

### 4. URL Management

#### Pretty URL Generation
```mermaid
flowchart LR
    A[Original URL] --> B[URL Processor]
    B --> C[Slug Generation]
    B --> D[Conflict Resolution]
    B --> E[SEO Optimization]
    
    C --> F[Clean URL]
    D --> F
    E --> F
    
    F --> G[Database Storage]
    F --> H[Redirect Setup]
```

#### URL Features
- **Clean URLs**: SEO-friendly URL structures
- **Automatic Redirects**: Handle URL changes gracefully
- **Canonical URLs**: Prevent duplicate content issues
- **URL Validation**: Ensure URL best practices

### 5. Content Analysis

#### SEO Content Scoring
```mermaid
flowchart TD
    A[Content Input] --> B[Text Analysis]
    A --> C[Structure Analysis]
    A --> D[Meta Analysis]
    
    B --> E[Keyword Density]
    B --> F[Readability Score]
    B --> G[Word Count]
    
    C --> H[Heading Structure]
    C --> I[Internal Links]
    C --> J[Image Alt Tags]
    
    D --> K[Title Optimization]
    D --> L[Description Quality]
    
    E --> M[SEO Score]
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
```

#### Analysis Metrics
- **Keyword Optimization**: Density, placement, variations
- **Readability**: Flesch score, sentence length, complexity
- **Structure**: Heading hierarchy, internal linking
- **Technical SEO**: Meta completeness, image optimization

## API Endpoints

### Meta Management

#### Get Content Meta
```http
GET /api/v1/seo/meta/{content_type}/{content_id}
```

#### Update Content Meta
```http
PUT /api/v1/seo/meta/{content_type}/{content_id}
Content-Type: application/json

{
  "meta_title": "Optimized Title for SEO",
  "meta_description": "Compelling description under 160 chars",
  "meta_keywords": "keyword1, keyword2, keyword3",
  "canonical_url": "https://yourblog.com/canonical-url",
  "og_title": "Social Media Optimized Title",
  "og_description": "Social media description",
  "og_image": "https://cdn.yourblog.com/social-image.jpg",
  "twitter_card": "summary_large_image",
  "no_index": false,
  "no_follow": false,
  "structured_data": {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "Article Title",
    "author": {
      "@type": "Person",
      "name": "Author Name"
    }
  }
}
```

### Sitemap Management

#### Generate Sitemap
```http
POST /api/v1/seo/sitemap/generate
```

#### Get Sitemap
```http
GET /api/v1/seo/sitemap/{type}  # main, posts, pages, images
```

#### Sitemap Status
```http
GET /api/v1/seo/sitemap/status
```

### URL Management

#### Create Redirect
```http
POST /api/v1/seo/redirects
Content-Type: application/json

{
  "from_url": "/old-url",
  "to_url": "/new-url",
  "status_code": 301,
  "type": "manual"
}
```

#### List Redirects
```http
GET /api/v1/seo/redirects?cursor=abc123&limit=20
```

### SEO Analysis

#### Analyze Content
```http
POST /api/v1/seo/analyze
Content-Type: application/json

{
  "content_type": "post",
  "content_id": 123,
  "target_keywords": ["go programming", "web development"]
}
```

#### Get SEO Report
```http
GET /api/v1/seo/reports/{content_type}/{content_id}
```

#### Bulk SEO Audit
```http
POST /api/v1/seo/audit/bulk
Content-Type: application/json

{
  "content_type": "post",
  "filters": {
    "status": "published",
    "date_from": "2024-01-01",
    "date_to": "2024-12-31"
  }
}
```

## SEO Automation

### Auto-optimization Features

#### Meta Auto-generation
```mermaid
flowchart TD
    A[Content Created/Updated] --> B{Has Custom Meta?}
    B -->|No| C[Generate Auto Meta]
    B -->|Yes| D[Validate Existing Meta]
    
    C --> E[AI Title Generation]
    C --> F[AI Description Generation]
    C --> G[Keyword Extraction]
    
    E --> H[Save Generated Meta]
    F --> H
    G --> H
    
    D --> I{Meta Quality Check}
    I -->|Poor| C
    I -->|Good| J[Keep Existing Meta]
```

#### Sitemap Auto-update
- **Real-time Updates**: Sitemap updated khi có content changes
- **Scheduled Generation**: Daily full regeneration
- **Cache Management**: Cached sitemaps với TTL
- **Ping Search Engines**: Automatic ping khi có updates

### SEO Rules Engine

#### Content Quality Rules
```yaml
seo_rules:
  title:
    min_length: 30
    max_length: 60
    required_keywords: true
    avoid_duplicates: true
    
  description:
    min_length: 120
    max_length: 160
    unique_per_page: true
    call_to_action: recommended
    
  content:
    min_words: 300
    heading_structure: required
    internal_links: 2
    external_links: 1
    image_alt_text: required
    
  url:
    max_length: 75
    lowercase: true
    no_special_chars: true
    include_keywords: true
```

### Performance Monitoring

#### SEO Metrics Tracking
```mermaid
flowchart LR
    A[Content Published] --> B[SEO Metrics Collection]
    B --> C[Search Rankings]
    B --> D[Click-through Rate]
    B --> E[Organic Traffic]
    B --> F[Social Shares]
    
    C --> G[SEO Dashboard]
    D --> G
    E --> G
    F --> G
    
    G --> H[Insights & Recommendations]
```

#### Key Performance Indicators
- **Search Rankings**: Keyword position tracking
- **Organic Traffic**: Search engine traffic analysis
- **Click-through Rate**: SERP performance metrics
- **Social Engagement**: Social media sharing metrics
- **Technical SEO**: Site speed, mobile friendliness

## Search Console Integration

### Google Search Console
- **Property Verification**: Automatic verification setup
- **Sitemap Submission**: Auto-submit sitemaps
- **Performance Data**: Rankings và click data
- **Coverage Reports**: Indexing status monitoring
- **Core Web Vitals**: Performance metrics tracking

### Bing Webmaster Tools
- **Site Verification**: Bing webmaster integration
- **URL Submission**: Bulk URL submission
- **Keyword Research**: Bing keyword insights
- **Crawl Data**: Crawling statistics

## Advanced Features

### AI-Powered SEO

#### Content Optimization
```mermaid
flowchart TD
    A[Content Input] --> B[AI Analysis Engine]
    B --> C[Keyword Research]
    B --> D[Content Gap Analysis]
    B --> E[Competitor Analysis]
    
    C --> F[Keyword Suggestions]
    D --> G[Content Recommendations]
    E --> H[Optimization Tips]
    
    F --> I[Auto-optimization]
    G --> I
    H --> I
    
    I --> J[Optimized Content]
```

#### Features
- **Keyword Research**: AI-powered keyword suggestions
- **Content Scoring**: Automatic content quality assessment
- **Competitor Analysis**: Analyze top-ranking competitors
- **Optimization Suggestions**: Actionable improvement recommendations

### Schema Markup Automation

#### Dynamic Schema Generation
- **Article Schema**: Auto-generate article markup
- **Organization Schema**: Company/brand information
- **Breadcrumb Schema**: Navigation structure
- **FAQ Schema**: Frequently asked questions
- **Review Schema**: User reviews và ratings

### Local SEO Support

#### Business Information
- **NAP Consistency**: Name, Address, Phone consistency
- **Local Business Schema**: Local business markup
- **Google My Business**: Integration với GMB API
- **Local Citations**: Business directory submissions

## Configuration

### SEO Settings với Website Isolation
```yaml
seo:
  enabled: true
  auto_meta_generation: true
  sitemap_generation: true
  website_isolation: true
  
  meta_defaults:
    title_suffix: " | {website_name}"
    og_type: "article"
    twitter_card: "summary_large_image"
    
  sitemap:
    max_urls: 50000
    change_frequency: "weekly"
    priority: 0.8
    per_website: true
    
  redirects:
    max_redirects: 10000
    auto_cleanup: true
    cleanup_after_days: 365
    per_website: true
    
  analysis:
    enabled: true
    auto_analyze: true
    target_score: 80
    per_website: true
    
  # Website-specific caching
  cache:
    enabled: true
    website_prefix: true
    ttl: 3600
    keys:
      sitemap: "website:{website_id}:sitemap:{type}"
      meta: "website:{website_id}:meta:{content_type}:{content_id}"
      redirects: "website:{website_id}:redirects"
```

### Search Engine Integration
```yaml
search_engines:
  google:
    search_console_property: "https://yourblog.com"
    analytics_id: "G-XXXXXXXXXX"
    verification_code: "google-verification-code"
    
  bing:
    webmaster_verification: "bing-verification-code"
    api_key: "bing-api-key"
    
  yandex:
    webmaster_verification: "yandex-verification-code"
```

## Best Practices

### Content SEO
- **Keyword Strategy**: Research-based keyword targeting
- **Content Quality**: High-quality, original content
- **User Intent**: Match content với search intent
- **E-A-T**: Expertise, Authoritativeness, Trustworthiness

### Technical SEO
- **Site Speed**: Optimize loading performance
- **Mobile-first**: Mobile-responsive design
- **SSL Certificate**: HTTPS implementation
- **Clean URLs**: SEO-friendly URL structure

### Monitoring & Maintenance
- **Regular Audits**: Monthly SEO audits
- **Performance Tracking**: Monitor key metrics
- **Algorithm Updates**: Stay updated với search algorithm changes
- **Continuous Optimization**: Ongoing improvement process

## Security Considerations

### SEO Security
- **Content Injection**: Prevent malicious content injection
- **Redirect Abuse**: Monitor unauthorized redirects
- **Negative SEO**: Protect against negative SEO attacks
- **Schema Validation**: Validate structured data

### Data Protection
- **Analytics Privacy**: Comply với privacy regulations
- **User Data**: Protect user search behavior data
- **API Security**: Secure third-party integrations

## Multi-Tenancy & Website Isolation

### Website-scoped SEO Management

```mermaid
flowchart TD
    A[SEO Request] --> B[Website Detection]
    B --> C[Load Website SEO Config]
    C --> D[Process SEO Operation]
    D --> E[Update Website-specific Data]
    E --> F[Update Website Cache]
    
    G[Sitemap Generation] --> H[Website Context]
    H --> I[Generate Website Sitemap]
    I --> J[Upload to Website Domain]
    
    K[Meta Management] --> L[Website Context]
    L --> M[Load Website Meta Rules]
    M --> N[Generate Website-specific Meta]
```

### SEO Repository với Website Isolation

```go
type SEORepository struct {
    db *gorm.DB
    websiteID uint
}

func (r *SEORepository) GetSEOMeta(contentType string, contentID uint) (*SEOMeta, error) {
    var meta SEOMeta
    err := r.db.Where("website_id = ? AND content_type = ? AND content_id = ?", 
        r.websiteID, contentType, contentID).First(&meta).Error
    return &meta, err
}

func (r *SEORepository) CreateSEOMeta(meta *SEOMeta) error {
    meta.WebsiteID = r.websiteID
    return r.db.Create(meta).Error
}

func (r *SEORepository) GetRedirects() ([]URLRedirect, error) {
    var redirects []URLRedirect
    err := r.db.Where("website_id = ? AND is_active = ?", r.websiteID, true).Find(&redirects).Error
    return redirects, err
}
```

### Website-specific SEO Features

#### Schema Markup per Website
```go
type WebsiteSchemaConfig struct {
    WebsiteID     uint   `json:"website_id"`
    Organization  struct {
        Name  string `json:"name"`
        URL   string `json:"url"`
        Logo  string `json:"logo"`
    } `json:"organization"`
    Website struct {
        Name        string `json:"name"`
        URL         string `json:"url"`
        Description string `json:"description"`
    } `json:"website"`
}
```

#### Multi-Website Sitemap Management
```go
type WebsiteSitemapService struct {
    websiteID uint
    domain    string
    seoRepo   SEORepository
}

func (s *WebsiteSitemapService) GenerateSitemap() error {
    // Generate website-specific sitemap
    sitemap := s.buildSitemap()
    
    // Save to website-specific path
    sitemapPath := fmt.Sprintf("/websites/%d/sitemap.xml", s.websiteID)
    err := s.saveSitemap(sitemapPath, sitemap)
    if err != nil {
        return err
    }
    
    // Submit to search engines with website domain
    return s.submitToSearchEngines(s.domain + "/sitemap.xml")
}
```

### SEO Best Practices cho Multi-Website

#### Website Isolation
- **Separate Sitemaps**: Mỗi website có sitemap riêng
- **Domain-specific Meta**: Meta tags theo domain của website
- **Isolated Analytics**: SEO analytics riêng per website
- **Website-specific Redirects**: Redirects chỉ áp dụng cho website

#### Performance Optimization
- **Website-scoped Caching**: Cache keys có website prefix
- **Lazy Loading**: Load SEO data khi cần cho website
- **Batch Processing**: Process SEO operations theo website
- **Website-aware Indexing**: Database indexes có website_id

### Example Implementation
```go
type WebsiteSEOService struct {
    repo     SEORepository
    cache    CacheService
    websiteID uint
}

func (s *WebsiteSEOService) GenerateMetaTags(contentType string, contentID uint) (*SEOMeta, error) {
    // Check cache first
    cacheKey := fmt.Sprintf("website:%d:meta:%s:%d", s.websiteID, contentType, contentID)
    if cached, err := s.cache.Get(cacheKey); err == nil {
        return cached.(*SEOMeta), nil
    }
    
    // Get existing meta or create new
    meta, err := s.repo.GetSEOMeta(contentType, contentID)
    if err != nil {
        // Create new meta with website context
        meta = s.generateAutoMeta(contentType, contentID)
        meta.WebsiteID = s.websiteID
        err = s.repo.CreateSEOMeta(meta)
        if err != nil {
            return nil, err
        }
    }
    
    // Cache with website prefix
    s.cache.Set(cacheKey, meta, 3600)
    return meta, nil
}
```

## Tài liệu liên quan

- [Website Module](./website.md)
- [Blog Module](./blog.md)
- [Media Module](./media.md)
- [Tenant Module](./tenant.md)
- [Analytics Integration](../integrations/analytics.md)
- [Performance Optimization](../best-practices/performance.md)