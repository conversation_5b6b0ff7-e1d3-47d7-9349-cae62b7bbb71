# Module Tenant - Tài liệu Tiếng Việt

## Tổng quan

Module Tenant cung cấp hệ thống multi-tenancy cho Blog API v3, cho phép nhiều tổ chức/công ty khác nhau sử dụng cùng một hệ thống với dữ liệu được phân tách hoàn toàn. Mỗi tenant có thể có nhiều websites, với mỗi website có thể có domain riêng và cài đặt riêng biệt.

## Tính năng chính

- **Quản lý Tenant**: Tạo, cập nhật, xóa tenant
- **Quản lý Website**: Mỗi tenant có thể có nhiều websites
- **Phân tách dữ liệu**: Mỗi website có dữ liệu riêng biệt
- **Tùy chỉnh cấu hình**: Cài đặt riêng cho từng website
- **Quản lý tên miền**: Hỗ trợ subdomain và custom domain per website
- **Billing & Subscription**: Quản lý gói dịch vụ theo tenant và website
- **Resource Limits**: Giới hạn tài nguyên theo gói dịch vụ per website

## Kiến trúc Module

### Cấu trúc thư mục
```
internal/modules/tenant/
├── models/                    # Các model liên quan đến tenant
│   ├── tenant.go             # Model tenant chính
│   ├── subscription.go       # Model gói đăng ký
│   └── domain.go            # Model tên miền
├── services/                 # Logic nghiệp vụ
│   ├── tenant_service.go     # Dịch vụ quản lý tenant
│   ├── domain_service.go     # Dịch vụ quản lý domain
│   └── billing_service.go    # Dịch vụ thanh toán
├── handlers/                 # HTTP handlers
├── repositories/             # Truy cập dữ liệu
├── middleware/               # Middleware xử lý tenant
└── validators/               # Validation
```

## Mô hình dữ liệu

### Tenant
- **ID**: Định danh duy nhất
- **Name**: Tên hiển thị của tenant
- **Slug**: Tên rút gọn dùng cho subdomain
- **Status**: Trạng thái (active, suspended, deleted)
- **Plan**: Gói dịch vụ hiện tại
- **Settings**: Cài đặt tùy chỉnh (JSON)
- **Created/Updated**: Thời gian tạo/cập nhật

### Website
- **ID**: Định danh duy nhất website
- **TenantID**: ID của tenant sở hữu
- **Name**: Tên hiển thị của website
- **Slug**: Tên rút gọn dùng cho subdomain
- **Status**: Trạng thái (active, suspended, deleted)
- **Settings**: Cài đặt tùy chỉnh (JSON)
- **Theme**: Theme được sử dụng
- **Created/Updated**: Thời gian tạo/cập nhật

### Subscription
- **TenantID**: ID của tenant
- **WebsiteID**: ID của website (nếu subscription per website)
- **Plan**: Loại gói (free, basic, premium, enterprise)
- **Status**: Trạng thái đăng ký
- **StartDate/EndDate**: Thời gian bắt đầu/kết thúc
- **Limits**: Giới hạn tài nguyên

### Domain
- **TenantID**: ID của tenant
- **WebsiteID**: ID của website
- **Domain**: Tên miền
- **Type**: Loại (subdomain, custom)
- **Status**: Trạng thái (pending, verified, active)
- **SSL**: Cấu hình SSL

## Luồng hoạt động

### 1. Tạo Tenant mới

```mermaid
sequenceDiagram
    participant Admin as Quản trị viên
    participant API as Tenant API
    participant DB as Database
    participant DNS as DNS Service
    
    Admin->>API: Tạo tenant mới
    API->>API: Validate thông tin
    API->>DB: Lưu thông tin tenant
    API->>DNS: Tạo subdomain
    API->>DB: Tạo database schema riêng
    API->>Admin: Trả về thông tin tenant
```

### 2. Xác định Website từ Request

```mermaid
flowchart TD
    A[HTTP Request] --> B{Kiểm tra Header}
    B -->|Có Website-ID| C[Sử dụng Website-ID]
    B -->|Không có| D{Kiểm tra Subdomain}
    D -->|Có subdomain| E[Tìm website từ subdomain]
    D -->|Không có| F{Kiểm tra Custom Domain}
    F -->|Có custom domain| G[Tìm website từ domain]
    F -->|Không có| H[Sử dụng default website]
    C --> I[Set website context]
    E --> I
    G --> I
    H --> I
    I --> J[Set tenant context từ website]
    J --> K[Tiếp tục xử lý request]
```

### 3. Quản lý gói đăng ký

```mermaid
stateDiagram-v2
    [*] --> Trial: Đăng ký mới
    Trial --> Active: Thanh toán thành công
    Trial --> Expired: Hết hạn trial
    Active --> Suspended: Thanh toán thất bại
    Active --> Cancelled: Hủy gói
    Suspended --> Active: Thanh toán lại
    Suspended --> Cancelled: Hết thời gian gia hạn
    Expired --> Active: Nâng cấp gói
    Cancelled --> [*]: Xóa tài khoản
```

## API Endpoints

### Quản lý Tenant
- `GET /api/v1/tenants` - Danh sách tenant
- `POST /api/v1/tenants` - Tạo tenant mới
- `GET /api/v1/tenants/{id}` - Chi tiết tenant
- `PUT /api/v1/tenants/{id}` - Cập nhật tenant
- `DELETE /api/v1/tenants/{id}` - Xóa tenant

### Quản lý Domain
- `GET /api/v1/tenants/{id}/domains` - Danh sách domain
- `POST /api/v1/tenants/{id}/domains` - Thêm domain mới
- `PUT /api/v1/domains/{id}` - Cập nhật domain
- `DELETE /api/v1/domains/{id}` - Xóa domain
- `POST /api/v1/domains/{id}/verify` - Xác thực domain

### Quản lý Subscription
- `GET /api/v1/tenants/{id}/subscription` - Thông tin gói hiện tại
- `POST /api/v1/tenants/{id}/subscription` - Đăng ký gói mới
- `PUT /api/v1/tenants/{id}/subscription` - Nâng cấp/hạ cấp gói

## Cấu hình

### Gói dịch vụ
```yaml
plans:
  free:
    name: "Miễn phí"
    price: 0
    limits:
      users: 3
      posts: 50
      storage: "1GB"
      bandwidth: "10GB"
  
  basic:
    name: "Cơ bản"
    price: 29
    limits:
      users: 10
      posts: 500
      storage: "10GB"
      bandwidth: "100GB"
  
  premium:
    name: "Cao cấp"
    price: 99
    limits:
      users: 50
      posts: 5000
      storage: "100GB"
      bandwidth: "1TB"
```

### Middleware tenant & website
- Xác định website từ request
- Xác định tenant từ website
- Thiết lập database connection cho tenant
- Set website context cho tất cả queries
- Kiểm tra giới hạn tài nguyên per website
- Ghi log hoạt động theo website và tenant

## Bảo mật

### Phân tách dữ liệu
- Mỗi tenant có database schema riêng
- Query luôn được filter theo website_id
- Không thể truy cập dữ liệu của website khác
- Tenant admin có thể truy cập tất cả websites trong tenant

### Xác thực và phân quyền
- System admin có thể quản lý tất cả tenant
- Tenant admin có thể quản lý tất cả websites trong tenant
- Website admin chỉ quản lý website của mình
- User chỉ truy cập dữ liệu trong website của mình

## Monitoring và Analytics

### Theo dõi sử dụng tài nguyên
```mermaid
flowchart LR
    A[Request] --> B[Middleware]
    B --> C[Count Usage per Website]
    C --> D[Check Website Limits]
    D --> E{Vượt giới hạn?}
    E -->|Có| F[Return 429]
    E -->|Không| G[Continue]
    C --> H[Log to Analytics]
    H --> I[Aggregate to Tenant]
```

### Báo cáo
- Số lượng user theo website
- Lưu lượng API theo website
- Dung lượng storage sử dụng per website
- Băng thông sử dụng per website
- Tổng hợp theo tenant

## Tích hợp với các module khác

### Auth Module
- User thuộc về một website cụ thể
- JWT token chứa thông tin website_id và tenant_id
- Permission kiểm tra theo website và tenant

### Blog Module
- Posts, categories, tags được phân tách theo website
- Upload files vào thư mục riêng của website
- Cross-website access chỉ cho tenant admin

### Notification Module
- Template notification riêng cho mỗi website
- Email gửi từ domain của website
- Fallback đến template tenant nếu không có website template

## Best Practices

### Performance
- Sử dụng connection pooling cho database
- Cache thông tin website và tenant thường dùng
- Optimize query với proper indexing (website_id first)

### Scalability
- Thiết kế để có thể scale horizontal
- Cân nhắc database sharding theo website
- Load balancing theo website

### Maintenance
- Backup dữ liệu theo website
- Migration data khi thay đổi schema
- Monitoring health check cho từng website
- Tenant-level aggregation reporting

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Auth Module](./auth.md)
- [Security Best Practices](../best-practices/security.md)
- [Performance Optimization](../best-practices/performance.md)