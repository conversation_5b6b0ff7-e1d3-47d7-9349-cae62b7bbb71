# Payment Module - Core Payment System

## Tổng quan

Payment Module cung cấp **core payment system** cho Blog API v3, định nghĩa interfaces, business logic, và workflows cho payment processing. Module này **không chứa** gateway-specific implementations - thay vào đó sử dụng **Payment Plugins** để integrate với các payment gateways cụ thể.

> **🔌 Payment Gateways**: Xem [Payment Plugins](../plugins/payment/) để biết implementations cho Stripe, PayPal, VNPay, và các gateways khác.

## Mục tiêu

- **Core Payment Interfaces**: Định nghĩa abstract interfaces cho payment operations
- **Subscription Management**: Business logic cho recurring subscriptions và billing cycles
- **Invoice Generation**: Core invoice system và PDF generation
- **Payment Security**: PCI compliance framework và security interfaces
- **Financial Reporting**: Payment analytics và reporting framework
- **Plugin Management**: Gateway plugin discovery và lifecycle management
- **Webhook Orchestration**: Unified webhook handling cho multiple gateways

## Kiến trú<PERSON> hệ thống

### Payment Architecture Overview (Core)

```mermaid
flowchart TD
    A[Payment Module Core] --> B[Plugin Manager]
    A --> C[Subscription Engine]
    A --> D[Invoice System]
    A --> E[Transaction Manager]
    A --> F[Webhook Orchestrator]
    A --> G[Payment Analytics]

    B --> B1[Plugin Discovery]
    B --> B2[Gateway Selection]
    B --> B3[Failover Logic]
    B --> B4[Load Balancing]

    C --> C1[Plan Management]
    C --> C2[Billing Cycles]
    C --> C3[Subscription Status]
    C --> C4[Trial Management]

    D --> D1[Invoice Generation]
    D --> D2[Tax Calculation]
    D --> D3[PDF Generation]
    D --> D4[Email Delivery]

    E --> E1[Payment Processing Interface]
    E --> E2[Refund Management Interface]
    E --> E3[Chargeback Handling Interface]
    E --> E4[Payment Methods Interface]

    F --> F1[Webhook Routing]
    F --> F2[Status Updates]
    F --> F3[Event Broadcasting]

    G --> G1[Revenue Reports]
    G --> G2[Payment Analytics]
    G --> G3[Subscription Metrics]

    subgraph "Payment Plugins (External)"
        H[Stripe Plugin]
        I[PayPal Plugin]
        J[VNPay Plugin]
        K[Bank Transfer Plugin]
    end

    B -.-> H
    B -.-> I
    B -.-> J
    B -.-> K
```

## Core Interfaces

### Payment Service Interface

Payment Module định nghĩa abstract interfaces mà các Payment Plugins phải implement:

```go
// Core payment service interface
type PaymentService interface {
    // Payment processing
    ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error)
    ProcessRefund(ctx context.Context, refund *RefundRequest) (*RefundResult, error)
    CapturePayment(ctx context.Context, paymentID string, amount *decimal.Decimal) (*PaymentResult, error)
    VoidPayment(ctx context.Context, paymentID string) (*PaymentResult, error)

    // Subscription management
    CreateSubscription(ctx context.Context, subscription *SubscriptionRequest) (*SubscriptionResult, error)
    UpdateSubscription(ctx context.Context, subscriptionID string, updates *SubscriptionUpdate) (*SubscriptionResult, error)
    CancelSubscription(ctx context.Context, subscriptionID string, reason string) (*SubscriptionResult, error)

    // Payment methods
    CreatePaymentMethod(ctx context.Context, method *PaymentMethodRequest) (*PaymentMethodResult, error)
    UpdatePaymentMethod(ctx context.Context, methodID string, updates *PaymentMethodUpdate) (*PaymentMethodResult, error)
    DeletePaymentMethod(ctx context.Context, methodID string) error

    // Webhook handling
    HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error)
    VerifyWebhookSignature(payload []byte, signature string) error

    // Provider info
    GetProviderInfo() *ProviderInfo
    HealthCheck(ctx context.Context) error
}

// Subscription service interface
type SubscriptionService interface {
    CreatePlan(ctx context.Context, plan *SubscriptionPlan) error
    UpdatePlan(ctx context.Context, planID string, updates *PlanUpdate) error
    GetPlan(ctx context.Context, planID string) (*SubscriptionPlan, error)
    ListPlans(ctx context.Context, filters *PlanFilters) ([]*SubscriptionPlan, error)

    CalculateProration(ctx context.Context, subscription *Subscription, newPlan *SubscriptionPlan) (*ProrationResult, error)
    ProcessBilling(ctx context.Context, subscription *Subscription) (*BillingResult, error)
}

// Invoice service interface
type InvoiceService interface {
    GenerateInvoice(ctx context.Context, subscription *Subscription, billing *BillingCycle) (*Invoice, error)
    SendInvoice(ctx context.Context, invoice *Invoice, recipient string) error
    MarkInvoicePaid(ctx context.Context, invoiceID string, payment *Payment) error
    CalculateTax(ctx context.Context, amount decimal.Decimal, location *TaxLocation) (*TaxCalculation, error)
}
```

### Plugin Management Interface

```go
// Payment plugin registry interface
type PaymentPluginRegistry interface {
    RegisterPlugin(name string, plugin PaymentService) error
    GetPlugin(name string) (PaymentService, error)
    GetActivePlugin() (PaymentService, error)
    ListPlugins() []PluginInfo
    SetActivePlugin(name string) error
    GetPluginForCurrency(currency string) (PaymentService, error)
    GetPluginForRegion(region string) (PaymentService, error)
}

// Payment plugin interface
type PaymentPlugin interface {
    PaymentService

    // Plugin lifecycle
    Initialize(config map[string]interface{}) error
    Shutdown() error
    GetInfo() *PluginInfo
    ValidateConfig(config map[string]interface{}) error

    // Plugin capabilities
    GetSupportedCurrencies() []string
    GetSupportedRegions() []string
    GetSupportedPaymentMethods() []PaymentMethodType
}
```

### Components

#### Gateway Manager
- **Multi-Gateway Support**: Abstract interface cho different payment providers
- **Gateway Selection**: Automatic gateway selection based on region/currency
- **Failover Handling**: Automatic failover khi primary gateway fails
- **Fee Optimization**: Choose optimal gateway based on fees

#### Subscription Engine
- **Plan Management**: Flexible subscription plans với multiple tiers
- **Billing Cycles**: Support monthly, yearly, custom billing periods
- **Proration**: Handle mid-cycle plan changes
- **Trial Periods**: Free trial management

#### Invoice System
- **Automatic Generation**: Auto-generate invoices cho subscriptions
- **Tax Calculation**: Automatic tax calculation based on location
- **Multiple Formats**: PDF, HTML, JSON invoice formats
- **Delivery Options**: Email, download, API access

## Model Structures

### Payment Gateway Model

```go
type PaymentGateway struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `gorm:"size:50;not null;uniqueIndex" json:"name"`
    DisplayName string    `gorm:"size:100;not null" json:"display_name"`
    Type        string    `gorm:"size:20;not null" json:"type"` // credit_card, bank_transfer, digital_wallet
    
    // Configuration
    IsActive    bool      `gorm:"default:false" json:"is_active"`
    IsDefault   bool      `gorm:"default:false" json:"is_default"`
    Priority    int       `gorm:"default:100" json:"priority"`
    
    // Supported features
    SupportsSubscription bool `gorm:"default:false" json:"supports_subscription"`
    SupportsRefunds     bool `gorm:"default:false" json:"supports_refunds"`
    SupportsWebhooks    bool `gorm:"default:false" json:"supports_webhooks"`
    
    // Geographic restrictions
    SupportedCountries  JSON `gorm:"type:json" json:"supported_countries"`
    SupportedCurrencies JSON `gorm:"type:json" json:"supported_currencies"`
    
    // Fee structure
    FixedFee            decimal.Decimal `gorm:"type:decimal(10,4);default:0" json:"fixed_fee"`
    PercentageFee       decimal.Decimal `gorm:"type:decimal(5,4);default:0" json:"percentage_fee"`
    
    // API Configuration
    APIConfig           JSON      `gorm:"type:json" json:"api_config"`
    WebhookURL          string    `gorm:"size:255" json:"webhook_url"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### Subscription Plan Model

```go
type SubscriptionPlan struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `gorm:"size:100;not null" json:"name"`
    Slug        string    `gorm:"size:100;not null;uniqueIndex" json:"slug"`
    Description string    `gorm:"type:text" json:"description"`
    
    // Pricing
    Amount      decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"amount"`
    Currency    string          `gorm:"size:3;not null" json:"currency"`
    
    // Billing
    Interval        string `gorm:"size:20;not null" json:"interval"` // month, year, week
    IntervalCount   int    `gorm:"default:1" json:"interval_count"`
    
    // Trial
    TrialDays       int    `gorm:"default:0" json:"trial_days"`
    
    // Features
    Features        JSON   `gorm:"type:json" json:"features"`
    Limits          JSON   `gorm:"type:json" json:"limits"`
    
    // Status
    IsActive        bool   `gorm:"default:true" json:"is_active"`
    IsPublic        bool   `gorm:"default:true" json:"is_public"`
    SortOrder       int    `gorm:"default:0" json:"sort_order"`
    
    // Metadata
    Metadata        JSON      `gorm:"type:json" json:"metadata"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relationships
    Subscriptions   []Subscription `json:"subscriptions,omitempty"`
}
```

### Subscription Model

```go
type Subscription struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    WebsiteID       *uint     `gorm:"index" json:"website_id"` // For website-specific subscriptions
    PlanID          uint      `gorm:"not null;index" json:"plan_id"`
    CustomerID      uint      `gorm:"not null;index" json:"customer_id"`
    
    // External IDs
    GatewayID       string    `gorm:"size:100" json:"gateway_id"`
    GatewaySubscriptionID string `gorm:"size:100" json:"gateway_subscription_id"`
    
    // Status
    Status          string    `gorm:"size:20;not null" json:"status"` // active, canceled, past_due, unpaid
    
    // Billing
    Amount          decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"amount"`
    Currency        string          `gorm:"size:3;not null" json:"currency"`
    
    // Dates
    StartDate       time.Time  `json:"start_date"`
    EndDate         *time.Time `json:"end_date"`
    TrialStart      *time.Time `json:"trial_start"`
    TrialEnd        *time.Time `json:"trial_end"`
    CanceledAt      *time.Time `json:"canceled_at"`
    
    // Next billing
    CurrentPeriodStart time.Time  `json:"current_period_start"`
    CurrentPeriodEnd   time.Time  `json:"current_period_end"`
    NextBillingDate    *time.Time `json:"next_billing_date"`
    
    // Cancellation
    CancelAtPeriodEnd  bool       `gorm:"default:false" json:"cancel_at_period_end"`
    CancellationReason string     `gorm:"size:255" json:"cancellation_reason"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Plan        SubscriptionPlan `json:"plan,omitempty"`
    Customer    Customer         `json:"customer,omitempty"`
    Invoices    []Invoice        `json:"invoices,omitempty"`
    Payments    []Payment        `json:"payments,omitempty"`
}
```

### Payment Model

```go
type Payment struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    WebsiteID       *uint     `gorm:"index" json:"website_id"` // For website-specific payments
    CustomerID      uint      `gorm:"not null;index" json:"customer_id"`
    SubscriptionID  *uint     `gorm:"index" json:"subscription_id"`
    InvoiceID       *uint     `gorm:"index" json:"invoice_id"`
    
    // External IDs
    GatewayID       string    `gorm:"size:100;not null" json:"gateway_id"`
    GatewayPaymentID string   `gorm:"size:100" json:"gateway_payment_id"`
    
    // Payment details
    Amount          decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"amount"`
    Currency        string          `gorm:"size:3;not null" json:"currency"`
    
    // Status
    Status          string    `gorm:"size:20;not null" json:"status"` // pending, succeeded, failed, canceled, refunded
    FailureReason   string    `gorm:"size:255" json:"failure_reason"`
    
    // Payment method
    PaymentMethod   string    `gorm:"size:50" json:"payment_method"` // card, bank_transfer, paypal
    PaymentDetails  JSON      `gorm:"type:json" json:"payment_details"`
    
    // Fees
    GatewayFee      decimal.Decimal `gorm:"type:decimal(10,2);default:0" json:"gateway_fee"`
    ApplicationFee  decimal.Decimal `gorm:"type:decimal(10,2);default:0" json:"application_fee"`
    NetAmount       decimal.Decimal `gorm:"type:decimal(10,2)" json:"net_amount"`
    
    // Dates
    ProcessedAt     *time.Time `json:"processed_at"`
    RefundedAt      *time.Time `json:"refunded_at"`
    
    // Metadata
    Metadata        JSON      `gorm:"type:json" json:"metadata"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relationships
    Customer        Customer    `json:"customer,omitempty"`
    Subscription    *Subscription `json:"subscription,omitempty"`
    Invoice         *Invoice    `json:"invoice,omitempty"`
    Refunds         []Refund    `json:"refunds,omitempty"`
}
```

### Invoice Model

```go
type Invoice struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    InvoiceNumber   string    `gorm:"size:50;not null;uniqueIndex" json:"invoice_number"`
    TenantID        uint      `gorm:"not null;index" json:"tenant_id"`
    WebsiteID       *uint     `gorm:"index" json:"website_id"` // For website-specific invoices
    CustomerID      uint      `gorm:"not null;index" json:"customer_id"`
    SubscriptionID  *uint     `gorm:"index" json:"subscription_id"`
    
    // External IDs
    GatewayInvoiceID string   `gorm:"size:100" json:"gateway_invoice_id"`
    
    // Status
    Status          string    `gorm:"size:20;not null" json:"status"` // draft, open, paid, void, uncollectible
    
    // Amounts
    Subtotal        decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"subtotal"`
    TaxAmount       decimal.Decimal `gorm:"type:decimal(10,2);default:0" json:"tax_amount"`
    DiscountAmount  decimal.Decimal `gorm:"type:decimal(10,2);default:0" json:"discount_amount"`
    Total           decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"total"`
    AmountPaid      decimal.Decimal `gorm:"type:decimal(10,2);default:0" json:"amount_paid"`
    AmountDue       decimal.Decimal `gorm:"type:decimal(10,2)" json:"amount_due"`
    Currency        string          `gorm:"size:3;not null" json:"currency"`
    
    // Dates
    IssueDate       time.Time  `json:"issue_date"`
    DueDate         time.Time  `json:"due_date"`
    PaidAt          *time.Time `json:"paid_at"`
    VoidedAt        *time.Time `json:"voided_at"`
    
    // Billing period
    PeriodStart     *time.Time `json:"period_start"`
    PeriodEnd       *time.Time `json:"period_end"`
    
    // Tax information
    TaxRate         decimal.Decimal `gorm:"type:decimal(5,4);default:0" json:"tax_rate"`
    TaxDescription  string          `gorm:"size:100" json:"tax_description"`
    
    // Notes
    Description     string    `gorm:"type:text" json:"description"`
    Notes           string    `gorm:"type:text" json:"notes"`
    Footer          string    `gorm:"type:text" json:"footer"`
    
    // File URLs
    PDFURL          string    `gorm:"size:255" json:"pdf_url"`
    
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
    
    // Relationships
    Customer        Customer      `json:"customer,omitempty"`
    Subscription    *Subscription `json:"subscription,omitempty"`
    Items           []InvoiceItem `json:"items,omitempty"`
    Payments        []Payment     `json:"payments,omitempty"`
}
```

### Customer Model

```go
type Customer struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    WebsiteID   *uint     `gorm:"index" json:"website_id"` // For website-specific customers
    UserID      *uint     `gorm:"index" json:"user_id"` // Link to user if registered
    
    // External IDs
    StripeCustomerID  string `gorm:"size:100" json:"stripe_customer_id"`
    PayPalCustomerID  string `gorm:"size:100" json:"paypal_customer_id"`
    
    // Contact information
    Email       string    `gorm:"size:255;not null" json:"email"`
    Name        string    `gorm:"size:255;not null" json:"name"`
    Phone       string    `gorm:"size:50" json:"phone"`
    
    // Address
    Address     JSON      `gorm:"type:json" json:"address"`
    
    // Business information
    CompanyName string    `gorm:"size:255" json:"company_name"`
    TaxID       string    `gorm:"size:50" json:"tax_id"`
    
    // Payment preferences
    PreferredGateway    string `gorm:"size:50" json:"preferred_gateway"`
    DefaultPaymentMethod string `gorm:"size:100" json:"default_payment_method"`
    
    // Status
    IsActive    bool      `gorm:"default:true" json:"is_active"`
    
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Subscriptions []Subscription `json:"subscriptions,omitempty"`
    Payments      []Payment      `json:"payments,omitempty"`
    Invoices      []Invoice      `json:"invoices,omitempty"`
}
```

## Payment Gateway Integration

### Gateway Interface
```go
type PaymentGateway interface {
    // Configuration
    Initialize(config map[string]interface{}) error
    GetName() string
    GetSupportedCurrencies() []string
    GetSupportedCountries() []string
    
    // Payment processing
    CreatePayment(request *PaymentRequest) (*PaymentResponse, error)
    CapturePayment(paymentID string) (*PaymentResponse, error)
    RefundPayment(paymentID string, amount decimal.Decimal) (*RefundResponse, error)
    
    // Subscription management
    CreateSubscription(request *SubscriptionRequest) (*SubscriptionResponse, error)
    UpdateSubscription(subscriptionID string, request *UpdateSubscriptionRequest) (*SubscriptionResponse, error)
    CancelSubscription(subscriptionID string) (*SubscriptionResponse, error)
    
    // Customer management
    CreateCustomer(request *CustomerRequest) (*CustomerResponse, error)
    UpdateCustomer(customerID string, request *CustomerRequest) (*CustomerResponse, error)
    
    // Webhook handling
    VerifyWebhook(payload []byte, signature string) error
    ProcessWebhook(payload []byte) (*WebhookEvent, error)
}
```

### Stripe Gateway Implementation
```go
type StripeGateway struct {
    client *stripe.Client
    config StripeConfig
}

type StripeConfig struct {
    SecretKey     string `json:"secret_key"`
    PublicKey     string `json:"public_key"`
    WebhookSecret string `json:"webhook_secret"`
    Environment   string `json:"environment"` // sandbox, production
}

func (sg *StripeGateway) CreatePayment(request *PaymentRequest) (*PaymentResponse, error) {
    params := &stripe.PaymentIntentParams{
        Amount:   stripe.Int64(int64(request.Amount.Mul(decimal.NewFromInt(100)).IntPart())),
        Currency: stripe.String(request.Currency),
        Customer: stripe.String(request.CustomerID),
    }
    
    if request.PaymentMethod != "" {
        params.PaymentMethod = stripe.String(request.PaymentMethod)
        params.Confirm = stripe.Bool(true)
    }
    
    pi, err := paymentintent.New(params)
    if err != nil {
        return nil, err
    }
    
    return &PaymentResponse{
        ID:            pi.ID,
        Status:        string(pi.Status),
        Amount:        decimal.NewFromFloat(float64(pi.Amount) / 100),
        Currency:      string(pi.Currency),
        ClientSecret:  pi.ClientSecret,
    }, nil
}

func (sg *StripeGateway) CreateSubscription(request *SubscriptionRequest) (*SubscriptionResponse, error) {
    params := &stripe.SubscriptionParams{
        Customer: stripe.String(request.CustomerID),
        Items: []*stripe.SubscriptionItemsParams{
            {
                Price: stripe.String(request.PriceID),
            },
        },
    }
    
    if request.TrialPeriodDays > 0 {
        params.TrialPeriodDays = stripe.Int64(int64(request.TrialPeriodDays))
    }
    
    sub, err := subscription.New(params)
    if err != nil {
        return nil, err
    }
    
    return &SubscriptionResponse{
        ID:                 sub.ID,
        Status:             string(sub.Status),
        CurrentPeriodStart: time.Unix(sub.CurrentPeriodStart, 0),
        CurrentPeriodEnd:   time.Unix(sub.CurrentPeriodEnd, 0),
        LatestInvoiceID:    sub.LatestInvoice.ID,
    }, nil
}
```

### VNPay Gateway Implementation
```go
type VNPayGateway struct {
    config VNPayConfig
}

type VNPayConfig struct {
    TMNCode    string `json:"tmn_code"`
    HashSecret string `json:"hash_secret"`
    PayURL     string `json:"pay_url"`
    ReturnURL  string `json:"return_url"`
    Environment string `json:"environment"`
}

func (vg *VNPayGateway) CreatePayment(request *PaymentRequest) (*PaymentResponse, error) {
    // VNPay specific implementation
    params := map[string]string{
        "vnp_Version":    "2.1.0",
        "vnp_Command":    "pay",
        "vnp_TmnCode":    vg.config.TMNCode,
        "vnp_Amount":     fmt.Sprintf("%.0f", request.Amount.Mul(decimal.NewFromInt(100))),
        "vnp_CurrCode":   "VND",
        "vnp_TxnRef":     request.OrderID,
        "vnp_OrderInfo":  request.Description,
        "vnp_OrderType":  "other",
        "vnp_Locale":     "vn",
        "vnp_ReturnUrl":  vg.config.ReturnURL,
        "vnp_IpAddr":     request.CustomerIP,
        "vnp_CreateDate": time.Now().Format("20060102150405"),
    }
    
    // Create secure hash
    secureHash := vg.createSecureHash(params)
    params["vnp_SecureHash"] = secureHash
    
    // Build payment URL
    paymentURL := vg.buildPaymentURL(params)
    
    return &PaymentResponse{
        ID:         request.OrderID,
        Status:     "pending",
        PaymentURL: paymentURL,
    }, nil
}
```

## Subscription Management

### Subscription Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Trial
    [*] --> Active
    
    Trial --> Active : Trial ends, payment succeeds
    Trial --> PastDue : Trial ends, payment fails
    Trial --> Canceled : Canceled during trial
    
    Active --> PastDue : Payment fails
    Active --> Canceled : Canceled by user/admin
    Active --> Active : Successful payment
    
    PastDue --> Active : Payment succeeds
    PastDue --> Canceled : Grace period expires
    
    Canceled --> [*]
```

### Subscription Service
```go
type SubscriptionService struct {
    repo            SubscriptionRepository
    paymentService  PaymentService
    invoiceService  InvoiceService
    eventPublisher  EventPublisher
}

func (s *SubscriptionService) CreateSubscription(req *CreateSubscriptionRequest) (*Subscription, error) {
    // Validate plan
    plan, err := s.planRepo.GetByID(req.PlanID)
    if err != nil {
        return nil, err
    }
    
    // Create customer if not exists
    customer, err := s.customerService.GetOrCreateCustomer(req.CustomerData)
    if err != nil {
        return nil, err
    }
    
    // Calculate dates
    startDate := time.Now()
    var trialEnd *time.Time
    if plan.TrialDays > 0 {
        trialEndDate := startDate.AddDate(0, 0, plan.TrialDays)
        trialEnd = &trialEndDate
    }
    
    // Create subscription in gateway
    gatewayResponse, err := s.gateway.CreateSubscription(&SubscriptionRequest{
        CustomerID:       customer.GatewayCustomerID,
        PlanID:           plan.GatewayPlanID,
        TrialPeriodDays:  plan.TrialDays,
    })
    if err != nil {
        return nil, err
    }
    
    // Create local subscription record
    subscription := &Subscription{
        TenantID:           req.TenantID,
        PlanID:             plan.ID,
        CustomerID:         customer.ID,
        GatewayID:          s.gateway.GetName(),
        GatewaySubscriptionID: gatewayResponse.ID,
        Status:             "active",
        Amount:             plan.Amount,
        Currency:           plan.Currency,
        StartDate:          startDate,
        TrialEnd:           trialEnd,
        CurrentPeriodStart: gatewayResponse.CurrentPeriodStart,
        CurrentPeriodEnd:   gatewayResponse.CurrentPeriodEnd,
    }
    
    if err := s.repo.Create(subscription); err != nil {
        return nil, err
    }
    
    // Publish event
    s.eventPublisher.Publish("subscription.created", subscription)
    
    return subscription, nil
}

func (s *SubscriptionService) ProcessBilling(subscriptionID uint) error {
    subscription, err := s.repo.GetByID(subscriptionID)
    if err != nil {
        return err
    }
    
    // Check if billing is due
    if time.Now().Before(subscription.NextBillingDate) {
        return nil // Not due yet
    }
    
    // Generate invoice
    invoice, err := s.invoiceService.GenerateInvoice(subscription)
    if err != nil {
        return err
    }
    
    // Attempt payment
    payment, err := s.paymentService.ProcessInvoicePayment(invoice)
    if err != nil {
        // Payment failed - update subscription status
        subscription.Status = "past_due"
        s.repo.Update(subscription)
        s.eventPublisher.Publish("subscription.payment_failed", subscription)
        return err
    }
    
    // Payment succeeded - update subscription
    subscription.Status = "active"
    subscription.CurrentPeriodStart = subscription.CurrentPeriodEnd
    subscription.CurrentPeriodEnd = s.calculateNextPeriodEnd(subscription)
    subscription.NextBillingDate = &subscription.CurrentPeriodEnd
    
    s.repo.Update(subscription)
    s.eventPublisher.Publish("subscription.renewed", subscription)
    
    return nil
}
```

## Invoice System

### Invoice Generation

```mermaid
sequenceDiagram
    participant Sub as Subscription
    participant IS as Invoice Service
    participant TS as Tax Service
    participant PDF as PDF Generator
    participant Email as Email Service
    
    Sub->>IS: Billing due
    IS->>IS: Calculate line items
    IS->>TS: Calculate taxes
    TS->>IS: Return tax amount
    IS->>IS: Create invoice record
    IS->>PDF: Generate PDF
    PDF->>IS: Return PDF URL
    IS->>Email: Send invoice email
    Email->>IS: Email sent confirmation
```

### Invoice Service Implementation
```go
type InvoiceService struct {
    repo           InvoiceRepository
    taxService     TaxService
    pdfGenerator   PDFGenerator
    emailService   EmailService
    storageService StorageService
}

func (s *InvoiceService) GenerateInvoice(subscription *Subscription) (*Invoice, error) {
    // Generate invoice number
    invoiceNumber := s.generateInvoiceNumber()
    
    // Calculate amounts
    subtotal := subscription.Amount
    taxAmount, err := s.taxService.CalculateTax(subtotal, subscription.Customer.Address)
    if err != nil {
        return nil, err
    }
    
    total := subtotal.Add(taxAmount)
    
    // Create invoice
    invoice := &Invoice{
        InvoiceNumber:  invoiceNumber,
        TenantID:       subscription.TenantID,
        CustomerID:     subscription.CustomerID,
        SubscriptionID: &subscription.ID,
        Status:         "open",
        Subtotal:       subtotal,
        TaxAmount:      taxAmount,
        Total:          total,
        AmountDue:      total,
        Currency:       subscription.Currency,
        IssueDate:      time.Now(),
        DueDate:        time.Now().AddDate(0, 0, 30), // 30 days payment term
        PeriodStart:    &subscription.CurrentPeriodStart,
        PeriodEnd:      &subscription.CurrentPeriodEnd,
        Description:    fmt.Sprintf("Subscription to %s", subscription.Plan.Name),
    }
    
    // Save invoice
    if err := s.repo.Create(invoice); err != nil {
        return nil, err
    }
    
    // Generate PDF
    pdfURL, err := s.generatePDF(invoice)
    if err != nil {
        s.logger.Error("Failed to generate PDF", "invoice_id", invoice.ID, "error", err)
    } else {
        invoice.PDFURL = pdfURL
        s.repo.Update(invoice)
    }
    
    // Send email
    go s.sendInvoiceEmail(invoice)
    
    return invoice, nil
}

func (s *InvoiceService) generatePDF(invoice *Invoice) (string, error) {
    // Load invoice with related data
    invoice, err := s.repo.GetWithDetails(invoice.ID)
    if err != nil {
        return "", err
    }
    
    // Generate PDF content
    pdfContent, err := s.pdfGenerator.GenerateInvoicePDF(invoice)
    if err != nil {
        return "", err
    }
    
    // Upload to storage
    filename := fmt.Sprintf("invoices/%s.pdf", invoice.InvoiceNumber)
    url, err := s.storageService.Upload(filename, pdfContent, "application/pdf")
    if err != nil {
        return "", err
    }
    
    return url, nil
}
```

### Tax Calculation
```go
type TaxService struct {
    taxRates map[string]TaxRate
}

type TaxRate struct {
    Country     string          `json:"country"`
    State       string          `json:"state"`
    Rate        decimal.Decimal `json:"rate"`
    Type        string          `json:"type"` // vat, sales_tax, gst
    Description string          `json:"description"`
}

func (s *TaxService) CalculateTax(amount decimal.Decimal, address Address) (decimal.Decimal, error) {
    // Determine tax rate based on address
    taxRate, err := s.getTaxRate(address.Country, address.State)
    if err != nil {
        return decimal.Zero, err
    }
    
    // Calculate tax amount
    taxAmount := amount.Mul(taxRate.Rate)
    
    return taxAmount, nil
}

func (s *TaxService) getTaxRate(country, state string) (*TaxRate, error) {
    // Business logic for tax rate determination
    key := fmt.Sprintf("%s-%s", country, state)
    if rate, exists := s.taxRates[key]; exists {
        return &rate, nil
    }
    
    // Fallback to country-level rate
    if rate, exists := s.taxRates[country]; exists {
        return &rate, nil
    }
    
    // No tax applicable
    return &TaxRate{Rate: decimal.Zero}, nil
}
```

## Webhook Management

### Webhook Processing

```mermaid
flowchart TD
    A[Webhook Received] --> B[Verify Signature]
    B --> C{Valid Signature?}
    C -->|No| D[Return 401]
    C -->|Yes| E[Parse Payload]
    E --> F[Identify Event Type]
    F --> G[Process Event]
    G --> H[Update Database]
    H --> I[Publish Internal Event]
    I --> J[Return 200]
    
    G --> K{Event Type}
    K -->|Payment Success| L[Update Payment Status]
    K -->|Payment Failed| M[Handle Payment Failure]
    K -->|Subscription Updated| N[Update Subscription]
    K -->|Invoice Paid| O[Mark Invoice as Paid]
```

### Webhook Handler
```go
type WebhookHandler struct {
    gatewayManager  GatewayManager
    paymentService  PaymentService
    subscriptionService SubscriptionService
    invoiceService  InvoiceService
    eventPublisher  EventPublisher
}

func (h *WebhookHandler) HandleWebhook(c *gin.Context) {
    gatewayName := c.Param("gateway")
    
    // Get gateway
    gateway, err := h.gatewayManager.GetGateway(gatewayName)
    if err != nil {
        c.JSON(404, gin.H{"error": "Gateway not found"})
        return
    }
    
    // Read payload
    payload, err := io.ReadAll(c.Request.Body)
    if err != nil {
        c.JSON(400, gin.H{"error": "Invalid payload"})
        return
    }
    
    // Verify webhook signature
    signature := c.GetHeader("Stripe-Signature") // Gateway-specific header
    if err := gateway.VerifyWebhook(payload, signature); err != nil {
        c.JSON(401, gin.H{"error": "Invalid signature"})
        return
    }
    
    // Process webhook
    event, err := gateway.ProcessWebhook(payload)
    if err != nil {
        c.JSON(400, gin.H{"error": "Failed to process webhook"})
        return
    }
    
    // Handle event
    if err := h.handleEvent(event); err != nil {
        h.logger.Error("Failed to handle webhook event", "event", event, "error", err)
        c.JSON(500, gin.H{"error": "Failed to process event"})
        return
    }
    
    c.JSON(200, gin.H{"received": true})
}

func (h *WebhookHandler) handleEvent(event *WebhookEvent) error {
    switch event.Type {
    case "payment_intent.succeeded":
        return h.handlePaymentSucceeded(event)
    case "payment_intent.payment_failed":
        return h.handlePaymentFailed(event)
    case "invoice.payment_succeeded":
        return h.handleInvoicePaymentSucceeded(event)
    case "customer.subscription.updated":
        return h.handleSubscriptionUpdated(event)
    case "customer.subscription.deleted":
        return h.handleSubscriptionCanceled(event)
    default:
        h.logger.Info("Unhandled webhook event type", "type", event.Type)
        return nil
    }
}
```

## Payment Analytics

### Revenue Dashboard

```mermaid
flowchart LR
    A[Payment Data] --> B[Analytics Engine]
    B --> C[Revenue Metrics]
    B --> D[Subscription Metrics]
    B --> E[Customer Metrics]
    
    C --> C1[MRR - Monthly Recurring Revenue]
    C --> C2[ARR - Annual Recurring Revenue]
    C --> C3[Churn Rate]
    C --> C4[LTV - Lifetime Value]
    
    D --> D1[Active Subscriptions]
    D --> D2[New Subscriptions]
    D --> D3[Canceled Subscriptions]
    D --> D4[Plan Distribution]
    
    E --> E1[Customer Acquisition Cost]
    E --> E2[Payment Success Rate]
    E --> E3[Refund Rate]
    E --> E4[Geographic Distribution]
```

### Analytics Service
```go
type PaymentAnalytics struct {
    db             *gorm.DB
    cacheService   CacheService
}

type RevenueMetrics struct {
    MRR             decimal.Decimal `json:"mrr"`
    ARR             decimal.Decimal `json:"arr"`
    GrowthRate      float64         `json:"growth_rate"`
    ChurnRate       float64         `json:"churn_rate"`
    ARPU            decimal.Decimal `json:"arpu"` // Average Revenue Per User
    LTV             decimal.Decimal `json:"ltv"`  // Lifetime Value
}

func (a *PaymentAnalytics) GetRevenueMetrics(tenantID uint, period string) (*RevenueMetrics, error) {
    cacheKey := fmt.Sprintf("revenue_metrics:%d:%s", tenantID, period)
    
    // Try cache first
    if cached, err := a.cacheService.Get(cacheKey); err == nil {
        var metrics RevenueMetrics
        if err := json.Unmarshal(cached, &metrics); err == nil {
            return &metrics, nil
        }
    }
    
    // Calculate metrics
    mrr, err := a.calculateMRR(tenantID)
    if err != nil {
        return nil, err
    }
    
    arr := mrr.Mul(decimal.NewFromInt(12))
    growthRate := a.calculateGrowthRate(tenantID, period)
    churnRate := a.calculateChurnRate(tenantID, period)
    arpu := a.calculateARPU(tenantID, period)
    ltv := a.calculateLTV(tenantID, period)
    
    metrics := &RevenueMetrics{
        MRR:        mrr,
        ARR:        arr,
        GrowthRate: growthRate,
        ChurnRate:  churnRate,
        ARPU:       arpu,
        LTV:        ltv,
    }
    
    // Cache results
    if data, err := json.Marshal(metrics); err == nil {
        a.cacheService.Set(cacheKey, data, 1*time.Hour)
    }
    
    return metrics, nil
}

func (a *PaymentAnalytics) calculateMRR(tenantID uint) (decimal.Decimal, error) {
    var result struct {
        MRR decimal.Decimal
    }
    
    query := `
        SELECT COALESCE(SUM(
            CASE 
                WHEN s.interval = 'month' THEN s.amount
                WHEN s.interval = 'year' THEN s.amount / 12
                ELSE 0
            END
        ), 0) as mrr
        FROM subscriptions s
        JOIN subscription_plans sp ON s.plan_id = sp.id
        WHERE s.tenant_id = ? AND s.status = 'active'
    `
    
    err := a.db.Raw(query, tenantID).Scan(&result).Error
    return result.MRR, err
}
```

## API Endpoints

### Payment Management

#### Create Payment Intent
```http
POST /api/v1/payments/intents
Content-Type: application/json

{
  "amount": 29.99,
  "currency": "USD",
  "customer_id": 123,
  "payment_method": "pm_1234567890",
  "description": "Monthly subscription payment",
  "metadata": {
    "subscription_id": "456"
  }
}
```

#### Confirm Payment
```http
POST /api/v1/payments/{payment_id}/confirm
Content-Type: application/json

{
  "payment_method": "pm_1234567890"
}
```

#### Refund Payment
```http
POST /api/v1/payments/{payment_id}/refund
Content-Type: application/json

{
  "amount": 29.99,
  "reason": "requested_by_customer"
}
```

### Subscription Management

#### Create Subscription
```http
POST /api/v1/subscriptions
Content-Type: application/json

{
  "plan_id": 123,
  "customer_id": 456,
  "payment_method": "pm_1234567890",
  "trial_days": 14,
  "prorate": true
}
```

#### Update Subscription
```http
PUT /api/v1/subscriptions/{subscription_id}
Content-Type: application/json

{
  "plan_id": 124,
  "prorate": true,
  "cancel_at_period_end": false
}
```

#### Cancel Subscription
```http
DELETE /api/v1/subscriptions/{subscription_id}
Content-Type: application/json

{
  "cancel_at_period_end": true,
  "cancellation_reason": "User requested cancellation"
}
```

### Invoice Management

#### List Invoices
```http
GET /api/v1/invoices?customer_id=123&status=open&cursor=abc123&limit=20
```

#### Get Invoice
```http
GET /api/v1/invoices/{invoice_id}
```

#### Download Invoice PDF
```http
GET /api/v1/invoices/{invoice_id}/pdf
```

#### Send Invoice Email
```http
POST /api/v1/invoices/{invoice_id}/send
```

### Analytics

#### Revenue Dashboard
```http
GET /api/v1/analytics/revenue?period=month&tenant_id=123
```

#### Subscription Metrics
```http
GET /api/v1/analytics/subscriptions?period=month&tenant_id=123
```

#### Payment Success Rate
```http
GET /api/v1/analytics/payment-success-rate?period=week&gateway=stripe
```

## Security & Compliance

### PCI Compliance
- **No Card Data Storage**: Never store card data locally
- **Tokenization**: Use gateway tokens for stored payment methods
- **Secure Transmission**: HTTPS cho tất cả payment communications
- **Access Control**: Restricted access đến payment functions

### Data Protection
- **Encryption**: Encrypt sensitive payment data
- **Audit Logging**: Log tất cả payment operations
- **Data Minimization**: Store minimal payment information
- **Regular Security Audits**: Regular security assessments

### Fraud Prevention
- **Velocity Checks**: Monitor payment frequency
- **Blacklist Management**: Block known fraudulent customers
- **Risk Scoring**: Implement risk-based authentication
- **Real-time Monitoring**: Monitor suspicious activities

## Multi-Tenancy & Website Isolation

### Website-based Payment Management

```mermaid
flowchart TD
    A[Payment Request] --> B[Website Detection]
    B --> C[Load Website Payment Config]
    C --> D[Select Website Gateway]
    D --> E[Process Payment]
    E --> F[Update Website Analytics]
    
    G[Subscription Management] --> H[Website Context]
    H --> I[Website-specific Plans]
    I --> J[Website Billing]
    J --> K[Website Analytics]
```

### Payment Repository với Website Isolation

```go
type PaymentRepository struct {
    db *gorm.DB
    websiteID *uint
    tenantID uint
}

func (r *PaymentRepository) GetPayments(filters PaymentFilters) ([]Payment, error) {
    query := r.db.Where("tenant_id = ?", r.tenantID)
    
    if r.websiteID != nil {
        query = query.Where("website_id = ?", *r.websiteID)
    }
    
    if filters.Status != "" {
        query = query.Where("status = ?", filters.Status)
    }
    
    var payments []Payment
    err := query.Find(&payments).Error
    return payments, err
}

func (r *PaymentRepository) CreatePayment(payment *Payment) error {
    payment.TenantID = r.tenantID
    payment.WebsiteID = r.websiteID
    return r.db.Create(payment).Error
}
```

### Website-specific Payment Plans

```go
type WebsitePaymentPlan struct {
    ID          uint            `json:"id"`
    WebsiteID   uint            `json:"website_id"`
    Name        string          `json:"name"`
    Amount      decimal.Decimal `json:"amount"`
    Currency    string          `json:"currency"`
    Features    []string        `json:"features"`
    Limits      map[string]int  `json:"limits"`
    IsActive    bool            `json:"is_active"`
}

type WebsitePaymentService struct {
    websiteID uint
    tenantID  uint
    gateway   PaymentGateway
}

func (s *WebsitePaymentService) GetAvailablePlans() ([]WebsitePaymentPlan, error) {
    var plans []WebsitePaymentPlan
    err := s.db.Where("website_id = ? AND is_active = ?", s.websiteID, true).Find(&plans).Error
    return plans, err
}
```

## Configuration

### Payment Gateway Configuration với Website Support
```yaml
payment:
  default_gateway: "stripe"
  currency_fallback: "USD"
  website_isolation: true
  
  gateways:
    stripe:
      enabled: true
      environment: "sandbox" # sandbox, production
      secret_key: "${STRIPE_SECRET_KEY}"
      publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
      webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
      supported_currencies: ["USD", "EUR", "GBP"]
      website_support: true
      
    paypal:
      enabled: true
      environment: "sandbox"
      client_id: "${PAYPAL_CLIENT_ID}"
      client_secret: "${PAYPAL_CLIENT_SECRET}"
      webhook_id: "${PAYPAL_WEBHOOK_ID}"
      website_support: true
      
    vnpay:
      enabled: true
      environment: "sandbox"
      tmn_code: "${VNPAY_TMN_CODE}"
      hash_secret: "${VNPAY_HASH_SECRET}"
      supported_currencies: ["VND"]
      website_support: true
      
  features:
    subscriptions: true
    refunds: true
    webhooks: true
    analytics: true
    website_specific_plans: true
    
  billing:
    invoice_due_days: 30
    grace_period_days: 7
    auto_retry_failed_payments: true
    max_retry_attempts: 3
    
  # Website-specific settings
  website_settings:
    allow_custom_plans: true
    separate_analytics: true
    isolated_customers: true
    
  # Cache configuration
  cache:
    enabled: true
    website_prefix: true
    keys:
      payment: "website:{website_id}:payment:{payment_id}"
      subscription: "website:{website_id}:subscription:{subscription_id}"
      customer: "website:{website_id}:customer:{customer_id}"
      analytics: "website:{website_id}:analytics:{metric}:{period}"
```

## Best Practices

### Payment Processing
- **Idempotency**: Ensure payment operations are idempotent
- **Error Handling**: Graceful error handling và user feedback
- **Timeout Handling**: Handle gateway timeouts appropriately
- **Reconciliation**: Regular reconciliation với gateway records

### Subscription Management
- **Proration**: Handle mid-cycle plan changes properly
- **Grace Periods**: Provide grace periods cho failed payments
- **Dunning Management**: Implement dunning cho failed payments
- **Cancellation Flow**: Clear cancellation process

### Financial Reporting
- **Revenue Recognition**: Proper revenue recognition principles
- **Tax Compliance**: Accurate tax calculation và reporting
- **Audit Trail**: Maintain comprehensive audit trails
- **Reconciliation**: Regular financial reconciliation

### Website Payment Analytics

```go
type WebsitePaymentAnalytics struct {
    WebsiteID       uint            `json:"website_id"`
    TenantID        uint            `json:"tenant_id"`
    MRR             decimal.Decimal `json:"mrr"`
    TotalRevenue    decimal.Decimal `json:"total_revenue"`
    ActiveSubscriptions int         `json:"active_subscriptions"`
    ChurnRate       float64         `json:"churn_rate"`
    PaymentSuccessRate float64      `json:"payment_success_rate"`
}

func (a *PaymentAnalytics) GetWebsiteMetrics(websiteID uint) (*WebsitePaymentAnalytics, error) {
    cacheKey := fmt.Sprintf("website:%d:analytics:payment:monthly", websiteID)
    
    // Try cache first
    if cached, err := a.cache.Get(cacheKey); err == nil {
        return cached.(*WebsitePaymentAnalytics), nil
    }
    
    // Calculate metrics for website
    metrics := &WebsitePaymentAnalytics{
        WebsiteID: websiteID,
        MRR:       a.calculateWebsiteMRR(websiteID),
        TotalRevenue: a.calculateWebsiteRevenue(websiteID),
        ActiveSubscriptions: a.countActiveSubscriptions(websiteID),
        ChurnRate: a.calculateWebsiteChurnRate(websiteID),
        PaymentSuccessRate: a.calculateWebsiteSuccessRate(websiteID),
    }
    
    // Cache results
    a.cache.Set(cacheKey, metrics, 1*time.Hour)
    return metrics, nil
}
```

### Best Practices cho Website Payment

#### Payment Isolation
- **Website-scoped Payments**: Tất cả payments thuộc về website cụ thể
- **Isolated Analytics**: Analytics riêng cho mỗi website
- **Separate Billing**: Billing cycles riêng per website
- **Website-specific Plans**: Plans có thể customize per website

#### Performance Optimization
- **Website-aware Caching**: Cache keys có website prefix
- **Batch Processing**: Process payments theo website
- **Optimized Queries**: Database queries với website_id index
- **Async Processing**: Async webhook processing per website

#### Security Considerations
- **Website Validation**: Validate website ownership trong mọi operation
- **Cross-website Prevention**: Ngăn chặn access cross-website
- **Audit Logging**: Log mọi payment operations với website context
- **Data Encryption**: Encrypt payment data với website-specific keys

### Example Implementation
```go
type WebsitePaymentService struct {
    repo      PaymentRepository
    gateway   PaymentGateway
    analytics PaymentAnalytics
    websiteID uint
}

func (s *WebsitePaymentService) ProcessPayment(req *PaymentRequest) (*Payment, error) {
    // Validate website access
    if !s.validateWebsiteAccess(req.UserID, s.websiteID) {
        return nil, errors.New("access denied to website")
    }
    
    // Process payment with website context
    payment := &Payment{
        TenantID:   req.TenantID,
        WebsiteID:  &s.websiteID,
        CustomerID: req.CustomerID,
        Amount:     req.Amount,
        Currency:   req.Currency,
        Status:     "pending",
    }
    
    // Process through gateway
    gatewayResp, err := s.gateway.ProcessPayment(req)
    if err != nil {
        return nil, err
    }
    
    payment.GatewayPaymentID = gatewayResp.ID
    payment.Status = gatewayResp.Status
    
    // Save to database
    err = s.repo.CreatePayment(payment)
    if err != nil {
        return nil, err
    }
    
    // Update website analytics
    s.analytics.UpdateWebsiteMetrics(s.websiteID, payment)
    
    return payment, nil
}
```

## Plugin Configuration

### Payment Gateway Selection

```yaml
payment:
  # Active payment gateway plugin
  active_gateway: "stripe"

  # Fallback gateways (in order of preference)
  fallback_gateways: ["paypal", "vnpay"]

  # Gateway selection rules
  gateway_rules:
    currency_based:
      USD: ["stripe", "paypal"]
      VND: ["vnpay", "stripe"]
      EUR: ["stripe", "paypal"]

    region_based:
      US: ["stripe", "paypal"]
      VN: ["vnpay", "stripe"]
      EU: ["stripe", "paypal"]

  # Gateway-specific configurations
  gateways:
    stripe:
      plugin: "stripe"
      config:
        secret_key: "${STRIPE_SECRET_KEY}"
        webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
        capture_method: "automatic"

    paypal:
      plugin: "paypal"
      config:
        client_id: "${PAYPAL_CLIENT_ID}"
        client_secret: "${PAYPAL_CLIENT_SECRET}"
        environment: "sandbox" # or "live"

    vnpay:
      plugin: "vnpay"
      config:
        merchant_id: "${VNPAY_MERCHANT_ID}"
        secret_key: "${VNPAY_SECRET_KEY}"
        return_url: "${BASE_URL}/payment/vnpay/return"
```

### Plugin Management

```go
// Initialize payment system with plugins
func InitializePaymentSystem() {
    registry := payment.NewPluginRegistry()

    // Register available plugins
    registry.RegisterPlugin("stripe", stripe.NewPlugin())
    registry.RegisterPlugin("paypal", paypal.NewPlugin())
    registry.RegisterPlugin("vnpay", vnpay.NewPlugin())

    // Set active gateway
    registry.SetActivePlugin("stripe")

    // Initialize payment service
    paymentService := payment.NewService(registry)
}
```

## Tài liệu liên quan

### Core Modules
- **[Tenant Module](./tenant.md)** - Multi-tenant subscription management
- **[Auth Module](./auth.md)** - User authentication cho payment flows
- **[Website Module](./website.md)** - Website-specific payment settings
- **[Email Module](./email.md)** - Invoice và payment notification emails

### Payment Plugins
- **[Stripe Plugin](../plugins/payment/stripe.md)** - Stripe implementation
- **[PayPal Plugin](../plugins/payment/paypal.md)** - PayPal implementation
- **[VNPay Plugin](../plugins/payment/vnpay.md)** - VNPay implementation
- **[Bank Transfer Plugin](../plugins/payment/bank-transfer.md)** - Bank transfer implementation

### Architecture & Infrastructure
- **[Module vs Plugin Boundaries](../architecture/module-vs-plugin-boundaries.md)** - Architectural guidelines
- **[Plugin System Overview](../plugins/overview.md)** - Plugin architecture
- **[Queue System](../architecture/queue-system.md)** - Background payment processing
- **[Inter-module Communication](../architecture/inter-module-communication.md)** - Event-driven patterns

### API & Development
- **[Response Standard](../api/response-standard.md)** - API response formats
- **[Webhook Documentation](../integrations/webhooks.md)** - Webhook handling
- **[Creating Plugins](../plugins/creating-plugins.md)** - Plugin development guide
- **[Security Guidelines](../best-practices/security.md)** - Payment security best practices