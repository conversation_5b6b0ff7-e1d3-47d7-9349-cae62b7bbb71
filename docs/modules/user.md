# User Module - <PERSON><PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

User Module cung cấp hệ thống quản lý người dùng toàn diện cho Blog API v3, bao gồm user profiles, preferences, activity tracking, social features, và user analytics. Module được thiết kế để hỗ trợ multi-tenancy, role-based permissions, và social interactions.

## Mục tiêu

- **User Management**: Quản lý profiles và thông tin người dùng
- **User Preferences**: Cá nhân hóa trải nghiệm người dùng
- **Activity Tracking**: <PERSON> dõi hoạt động và engagement
- **Social Features**: Following, favorites, và social interactions
- **Analytics**: User behavior analytics và insights
- **Privacy Controls**: Quản lý privacy và data protection
- **Multi-tenancy**: Hỗ trợ tenant isolation với website_id

## Kiến trúc hệ thống

### User Module Architecture

```mermaid
flowchart TD
    A[User Module] --> B[Profile Management]
    A --> C[Preferences System]
    A --> D[Activity Tracking]
    A --> E[Social Features]
    A --> F[Privacy Controls]
    A --> G[Analytics Engine]
    
    B --> B1[Basic Profile]
    B --> B2[Extended Profile]
    B --> B3[Avatar Management]
    B --> B4[Profile Verification]
    
    C --> C1[UI Preferences]
    C --> C2[Content Preferences]
    C --> C3[Notification Settings]
    C --> C4[Privacy Settings]
    
    D --> D1[Reading History]
    D --> D2[Interaction Tracking]
    D --> D3[Engagement Metrics]
    D --> D4[Session Tracking]
    
    E --> E1[Following System]
    E --> E2[Favorites]
    E --> E3[Bookmarks]
    E --> E4[Social Sharing]
    
    F --> F1[Data Privacy]
    F --> F2[Content Visibility]
    F --> F3[Profile Privacy]
    F --> F4[GDPR Compliance]
    
    G --> G1[User Analytics]
    G --> G2[Behavior Analysis]
    G --> G3[Engagement Reports]
    G --> G4[Recommendations]
```

### Components

#### Profile Management
- **Basic Profile**: Name, email, avatar, bio
- **Extended Profile**: Social links, location, interests
- **Avatar Management**: Upload, crop, optimize avatars
- **Profile Verification**: Email, phone, identity verification

#### Preferences System
- **UI Preferences**: Theme, language, layout preferences
- **Content Preferences**: Favorite categories, content types
- **Notification Settings**: Email, push, in-app notifications
- **Privacy Settings**: Profile visibility, data sharing

#### Activity Tracking
- **Reading History**: Track posts read, time spent
- **Interaction Tracking**: Comments, likes, shares
- **Engagement Metrics**: User engagement analytics
- **Session Tracking**: Login sessions, device tracking

## Model Structures

### User Profile Models

```mermaid
erDiagram
    USER {
        uint id PK
        uint website_id FK
        string email UK
        string username UK
        string password_hash
        string first_name
        string last_name
        string avatar_url
        string status
        bool email_verified
        bool phone_verified
        datetime email_verified_at
        datetime last_login_at
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }
    
    USER_PROFILE {
        uint id PK
        uint website_id FK
        uint user_id FK
        text bio
        string website
        string location
        string timezone
        date birth_date
        string gender
        string phone
        json social_links
        json interests
        json skills
        datetime created_at
        datetime updated_at
    }
    
    USER_PREFERENCES {
        uint id PK
        uint website_id FK
        uint user_id FK
        string category
        string key
        string value
        string type
        datetime created_at
        datetime updated_at
    }
    
    USER_ACTIVITY {
        uint id PK
        uint website_id FK
        uint user_id FK
        string activity_type
        string entity_type
        uint entity_id
        json metadata
        string ip_address
        string user_agent
        datetime created_at
    }
    
    USER_FOLLOW {
        uint website_id FK
        uint follower_id FK
        uint following_id FK
        datetime created_at
    }
    
    USER_FAVORITE {
        uint id PK
        uint website_id FK
        uint user_id FK
        string entity_type
        uint entity_id
        datetime created_at
    }
    
    USER_BOOKMARK {
        uint id PK
        uint website_id FK
        uint user_id FK
        uint post_id FK
        string folder
        json tags
        text notes
        datetime created_at
        datetime updated_at
    }
    
    USER_SESSION {
        uint id PK
        uint website_id FK
        uint user_id FK
        string session_token
        string device_info
        string ip_address
        string user_agent
        datetime last_activity
        datetime expires_at
        datetime created_at
    }
    
    USER ||--|| USER_PROFILE : "has"
    USER ||--o{ USER_PREFERENCES : "has"
    USER ||--o{ USER_ACTIVITY : "has"
    USER ||--o{ USER_FOLLOW : "follows"
    USER ||--o{ USER_FAVORITE : "has"
    USER ||--o{ USER_BOOKMARK : "has"
    USER ||--o{ USER_SESSION : "has"
```

## User Profile Management

### 1. Profile Creation Flow

```mermaid
sequenceDiagram
    participant User as New User
    participant API as User API
    participant Auth as Auth Service
    participant Profile as Profile Service
    participant Storage as File Storage
    participant Email as Email Service
    participant Analytics as Analytics Service
    
    User->>API: Complete registration
    API->>Auth: Create user account
    Auth->>API: Return user ID
    
    API->>Profile: Create basic profile
    Profile->>API: Profile created
    
    User->>API: Upload avatar
    API->>Storage: Store avatar image
    Storage->>API: Return avatar URL
    API->>Profile: Update avatar URL
    
    User->>API: Complete profile info
    API->>Profile: Update extended profile
    Profile->>Analytics: Track profile completion
    
    API->>Email: Send welcome email
    Email->>User: Welcome email sent
    
    API->>User: Profile setup complete
```

### 2. Profile Update Workflow

```mermaid
stateDiagram-v2
    [*] --> Incomplete : Registration
    Incomplete --> BasicComplete : Add basic info
    BasicComplete --> EmailVerified : Verify email
    EmailVerified --> ProfileComplete : Add extended info
    ProfileComplete --> Verified : Identity verification
    
    BasicComplete --> BasicComplete : Update basic info
    EmailVerified --> EmailVerified : Update preferences
    ProfileComplete --> ProfileComplete : Update profile
    Verified --> Verified : Update any info
    
    note right of Incomplete
        - Name required
        - Email required
        - Password set
    end note
    
    note right of BasicComplete
        - Avatar uploaded
        - Bio added
        - Basic info complete
    end note
    
    note right of ProfileComplete
        - Extended profile
        - Preferences set
        - Social links added
    end note
```

### 3. Avatar Management

```mermaid
flowchart TD
    A[Upload Avatar] --> B{File Validation}
    B -->|Valid| C[Image Processing]
    B -->|Invalid| D[Return Error]
    
    C --> E[Generate Thumbnails]
    C --> F[Optimize Images]
    C --> G[Extract Metadata]
    
    E --> H[Store Multiple Sizes]
    F --> H
    G --> H
    
    H --> I[Update User Profile]
    I --> J[Invalidate Cache]
    J --> K[Return URLs]
    
    style A fill:#e3f2fd
    style K fill:#e8f5e8
    style D fill:#ffebee
```

#### Avatar Processing Features
- **Multiple Sizes**: Generate thumbnails (32x32, 64x64, 128x128, 256x256)
- **Format Optimization**: Convert to optimal formats (WebP, AVIF fallbacks)
- **Content Validation**: Validate image content for appropriateness
- **CDN Integration**: Distribute via CDN for fast loading

## User Preferences System

### 1. Preferences Architecture

```mermaid
flowchart LR
    A[User Preferences] --> B[UI Preferences]
    A --> C[Content Preferences]
    A --> D[Notification Preferences]
    A --> E[Privacy Preferences]
    
    B --> B1[Theme]
    B --> B2[Language]
    B --> B3[Timezone]
    B --> B4[Layout]
    
    C --> C1[Categories]
    C --> C2[Content Types]
    C --> C3[Difficulty Level]
    C --> C4[Reading Speed]
    
    D --> D1[Email Notifications]
    D --> D2[Push Notifications]
    D --> D3[In-App Notifications]
    D --> D4[Frequency Settings]
    
    E --> E1[Profile Visibility]
    E --> E2[Activity Visibility]
    E --> E3[Data Sharing]
    E --> E4[Cookie Preferences]
```

### 2. Preferences Management

#### Theme Preferences
```json
{
  "ui": {
    "theme": "dark",
    "accent_color": "#3498db",
    "font_size": "medium",
    "font_family": "system",
    "layout": "comfortable",
    "sidebar_collapsed": false,
    "show_reading_progress": true,
    "enable_animations": true
  }
}
```

#### Content Preferences
```json
{
  "content": {
    "preferred_categories": [1, 3, 7, 12],
    "excluded_categories": [5, 9],
    "content_types": ["article", "tutorial", "video"],
    "difficulty_level": "intermediate",
    "reading_speed": 250,
    "auto_bookmark": true,
    "hide_read_posts": false
  }
}
```

#### Notification Preferences
```json
{
  "notifications": {
    "email": {
      "enabled": true,
      "frequency": "weekly",
      "types": ["new_post", "comment_reply", "newsletter"]
    },
    "push": {
      "enabled": true,
      "types": ["comment_reply", "mention"]
    },
    "in_app": {
      "enabled": true,
      "types": ["all"]
    }
  }
}
```

## Activity Tracking System

### 1. Activity Types

```mermaid
flowchart TD
    A[User Activities] --> B[Content Activities]
    A --> C[Social Activities]
    A --> D[System Activities]
    A --> E[Engagement Activities]
    
    B --> B1[Post Read]
    B --> B2[Post Shared]
    B --> B3[Content Downloaded]
    B --> B4[Search Query]
    
    C --> C1[User Followed]
    C --> C2[Comment Posted]
    C --> C3[Post Liked]
    C --> C4[Content Bookmarked]
    
    D --> D1[Profile Updated]
    D --> D2[Settings Changed]
    D --> D3[Login/Logout]
    D --> D4[Password Changed]
    
    E --> E1[Time Spent Reading]
    E --> E2[Scroll Depth]
    E --> E3[Click Through]
    E --> E4[Return Visits]
```

### 2. Activity Tracking Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Frontend as Frontend App
    participant API as Activity API
    participant Queue as Message Queue
    participant Analytics as Analytics Service
    participant DB as Database
    
    User->>Frontend: Perform action
    Frontend->>API: Track activity
    API->>Queue: Queue activity event
    API->>Frontend: Acknowledge
    
    Queue->>Analytics: Process activity
    Analytics->>Analytics: Aggregate metrics
    Analytics->>DB: Store processed data
    
    Analytics->>Analytics: Generate insights
    Analytics->>API: Update user metrics
```

### 3. Reading Analytics

#### Reading Session Tracking
```go
type ReadingSession struct {
    ID           uint      `json:"id"`
    WebsiteID    uint      `json:"website_id"`
    UserID       uint      `json:"user_id"`
    PostID       uint      `json:"post_id"`
    StartTime    time.Time `json:"start_time"`
    EndTime      *time.Time `json:"end_time"`
    Duration     int       `json:"duration"` // seconds
    ScrollDepth  float64   `json:"scroll_depth"` // percentage
    Completed    bool      `json:"completed"`
    DeviceType   string    `json:"device_type"`
    ReferrerType string    `json:"referrer_type"`
}
```

#### Engagement Metrics
```go
type UserEngagementMetrics struct {
    WebsiteID           uint    `json:"website_id"`
    UserID              uint    `json:"user_id"`
    PostsRead           int     `json:"posts_read"`
    AverageReadingTime  float64 `json:"average_reading_time"`
    CompletionRate      float64 `json:"completion_rate"`
    ReturnVisitRate     float64 `json:"return_visit_rate"`
    SocialInteractions  int     `json:"social_interactions"`
    ContentShared       int     `json:"content_shared"`
    BookmarksCreated    int     `json:"bookmarks_created"`
    CommentsPosted      int     `json:"comments_posted"`
    LastActiveDate      time.Time `json:"last_active_date"`
}
```

## Social Features

### 1. Following System

```mermaid
flowchart TD
    A[User A] -->|Follows| B[User B]
    B -->|Can Follow Back| A
    
    C[Following Relationship] --> D[Notifications]
    C --> E[Content Feed]
    C --> F[Recommendations]
    
    D --> D1[New Post Alerts]
    D --> D2[Activity Updates]
    
    E --> E1[Following Feed]
    E --> E2[Priority Content]
    
    F --> F1[Suggested Users]
    F --> F2[Similar Interests]
```

#### Following Features
- **Mutual Following**: Detect mutual connections
- **Follow Suggestions**: AI-powered user recommendations
- **Follow Feed**: Personalized content from followed users
- **Follow Notifications**: Updates from followed users
- **Privacy Controls**: Private/public follow lists

### 2. Favorites & Bookmarks

```mermaid
erDiagram
    USER_FAVORITE {
        uint id PK
        uint user_id FK
        string entity_type
        uint entity_id
        json metadata
        datetime created_at
    }
    
    USER_BOOKMARK {
        uint id PK
        uint user_id FK
        uint post_id FK
        string folder
        json tags
        text notes
        bool is_public
        datetime created_at
        datetime updated_at
    }
    
    BOOKMARK_FOLDER {
        uint id PK
        uint website_id FK
        uint user_id FK
        string name
        string description
        string color
        bool is_default
        int sort_order
        datetime created_at
        datetime updated_at
    }
```

#### Bookmark Organization
- **Folders**: Organize bookmarks into folders
- **Tags**: Tag bookmarks for easy searching
- **Notes**: Add personal notes to bookmarks
- **Public Sharing**: Share bookmark collections
- **Export/Import**: Backup and restore bookmarks

### 3. Social Interactions Flow

```mermaid
sequenceDiagram
    participant UserA as User A
    participant API as Social API
    participant Notification as Notification Service
    participant UserB as User B
    participant Analytics as Analytics Service
    
    UserA->>API: Follow User B
    API->>API: Create follow relationship
    API->>Notification: Send follow notification
    Notification->>UserB: Notify about new follower
    API->>Analytics: Track social interaction
    
    UserA->>API: Bookmark post
    API->>API: Create bookmark
    API->>Analytics: Track bookmark event
    
    UserA->>API: Share post
    API->>API: Track share event
    API->>Analytics: Update engagement metrics
```

## User Analytics & Insights

### 1. Analytics Dashboard

```mermaid
flowchart TD
    A[User Analytics] --> B[Reading Analytics]
    A --> C[Engagement Analytics]
    A --> D[Social Analytics]
    A --> E[Preference Analytics]
    
    B --> B1[Reading Time]
    B --> B2[Articles Read]
    B --> B3[Reading Patterns]
    B --> B4[Content Preferences]
    
    C --> C1[Interaction Rate]
    C --> C2[Return Frequency]
    C --> C3[Session Duration]
    C --> C4[Bounce Rate]
    
    D --> D1[Followers Growth]
    D --> D2[Content Shares]
    D --> D3[Social Interactions]
    D --> D4[Community Engagement]
    
    E --> E1[Category Preferences]
    E --> E2[Device Usage]
    E --> E3[Time Patterns]
    E --> E4[Feature Usage]
```

### 2. Personal Insights

#### Reading Insights
```json
{
  "reading_stats": {
    "total_articles_read": 156,
    "total_reading_time": "42h 30m",
    "average_session_length": "12m 45s",
    "completion_rate": 78.5,
    "reading_streak": {
      "current": 7,
      "longest": 23
    },
    "reading_goals": {
      "monthly_target": 20,
      "current_progress": 16,
      "achievement_rate": 80
    }
  }
}
```

#### Engagement Insights
```json
{
  "engagement_stats": {
    "social_interactions": 89,
    "comments_posted": 23,
    "posts_shared": 12,
    "bookmarks_created": 45,
    "followers_gained": 8,
    "following_count": 34,
    "favorite_categories": [
      {"id": 1, "name": "Technology", "percentage": 35},
      {"id": 3, "name": "Programming", "percentage": 28},
      {"id": 7, "name": "AI/ML", "percentage": 22}
    ]
  }
}
```

## API Endpoints

### User Profile Management

#### Get User Profile
```http
GET /api/cms/v1/users/{user_id}/profile
```

#### Update User Profile
```http
PUT /api/cms/v1/users/me/profile
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "bio": "Software developer passionate about Go and web technologies",
  "website": "https://johndoe.dev",
  "location": "Ho Chi Minh City, Vietnam",
  "timezone": "Asia/Ho_Chi_Minh",
  "social_links": {
    "twitter": "@johndoe",
    "github": "johndoe",
    "linkedin": "johndoe"
  },
  "interests": ["programming", "technology", "ai", "blockchain"],
  "skills": ["go", "javascript", "react", "docker"]
}
```

#### Upload Avatar
```http
POST /api/cms/v1/users/me/avatar
Content-Type: multipart/form-data

file: [image file]
```

### User Preferences

#### Get User Preferences
```http
GET /api/cms/v1/users/me/preferences?category=ui,content,notifications
```

#### Update Preferences
```http
PUT /api/cms/v1/users/me/preferences
Content-Type: application/json

{
  "ui": {
    "theme": "dark",
    "language": "vi",
    "timezone": "Asia/Ho_Chi_Minh"
  },
  "content": {
    "preferred_categories": [1, 3, 7],
    "content_types": ["article", "tutorial"]
  },
  "notifications": {
    "email": {
      "enabled": true,
      "frequency": "weekly"
    }
  }
}
```

### Social Features

#### Follow User
```http
POST /api/cms/v1/users/{user_id}/follow
```

#### Unfollow User
```http
DELETE /api/cms/v1/users/{user_id}/follow
```

#### Get User Followers
```http
GET /api/cms/v1/users/{user_id}/followers?cursor=abc123&limit=20
```

#### Get User Following
```http
GET /api/cms/v1/users/{user_id}/following?cursor=abc123&limit=20
```

#### Create Bookmark
```http
POST /api/cms/v1/users/me/bookmarks
Content-Type: application/json

{
  "post_id": 123,
  "folder": "Technical Articles",
  "tags": ["go", "programming", "tutorial"],
  "notes": "Great explanation of Go concurrency patterns",
  "is_public": false
}
```

#### Get User Bookmarks
```http
GET /api/cms/v1/users/me/bookmarks?folder=Technical%20Articles&cursor=abc123&limit=20
```

### User Analytics

#### Get User Dashboard
```http
GET /api/cms/v1/users/me/dashboard
```

#### Get Reading Analytics
```http
GET /api/cms/v1/users/me/analytics/reading?period=30d
```

#### Get Engagement Analytics
```http
GET /api/cms/v1/users/me/analytics/engagement?period=7d
```

## Privacy & Security

### 1. Privacy Controls

```mermaid
flowchart TD
    A[Privacy Settings] --> B[Profile Privacy]
    A --> C[Activity Privacy]
    A --> D[Data Privacy]
    A --> E[Communication Privacy]
    
    B --> B1[Public Profile]
    B --> B2[Followers Only]
    B --> B3[Private Profile]
    
    C --> C1[Reading History]
    C --> C2[Social Activities]
    C --> C3[Location Data]
    
    D --> D1[Data Export]
    D --> D2[Data Deletion]
    D --> D3[Cookie Control]
    
    E --> E1[Email Preferences]
    E --> E2[Contact Permissions]
    E --> E3[Marketing Consent]
```

### 2. GDPR Compliance

#### Data Export
```http
GET /api/cms/v1/users/me/export
```

**Response:**
```json
{
  "user_data": {
    "profile": {...},
    "preferences": {...},
    "activities": [...],
    "bookmarks": [...],
    "social_connections": [...]
  },
  "export_date": "2024-07-15T10:30:00Z",
  "format": "json"
}
```

#### Data Deletion Request
```http
POST /api/cms/v1/users/me/delete-request
Content-Type: application/json

{
  "reason": "No longer using the service",
  "confirm_deletion": true
}
```

### 3. Security Features

#### Account Security
- **Two-Factor Authentication**: TOTP, SMS, email 2FA
- **Login Monitoring**: Track login attempts and devices
- **Session Management**: Active session monitoring
- **Password Security**: Strong password requirements

#### Data Protection
- **Encryption**: Encrypt sensitive user data
- **Access Logging**: Log data access attempts
- **Data Minimization**: Collect only necessary data
- **Consent Management**: Granular consent controls

## Performance Optimization

### 1. Caching Strategy

```mermaid
flowchart LR
    A[User Request] --> B{Cache Check}
    B -->|Hit| C[Return Cached Data]
    B -->|Miss| D[Query Database]
    D --> E[Cache Result]
    E --> F[Return Data]
    
    G[Profile Update] --> H[Invalidate Cache]
    H --> I[Update Database]
    I --> J[Refresh Cache]
```

#### Cache Layers
- **Profile Cache**: User profile data (TTL: 1 hour)
- **Preferences Cache**: User preferences (TTL: 6 hours)
- **Analytics Cache**: Aggregated analytics (TTL: 24 hours)
- **Social Cache**: Following/followers counts (TTL: 30 minutes)

### 2. Database Optimization

#### Indexing Strategy
```sql
-- User profile indexes with tenant support
CREATE INDEX idx_users_website_email ON users(website_id, email);
CREATE INDEX idx_users_website_username ON users(website_id, username);
CREATE INDEX idx_users_website_status ON users(website_id, status);
CREATE INDEX idx_users_website_last_login ON users(website_id, last_login_at);

-- Activity tracking indexes
CREATE INDEX idx_user_activity_website_user_type ON user_activities(website_id, user_id, activity_type);
CREATE INDEX idx_user_activity_website_created ON user_activities(website_id, created_at);
CREATE INDEX idx_user_activity_website_entity ON user_activities(website_id, entity_type, entity_id);

-- Social features indexes
CREATE INDEX idx_user_follow_website_follower ON user_follows(website_id, follower_id);
CREATE INDEX idx_user_follow_website_following ON user_follows(website_id, following_id);
CREATE INDEX idx_user_bookmarks_website_user ON user_bookmarks(website_id, user_id);
CREATE INDEX idx_user_bookmarks_website_folder ON user_bookmarks(website_id, folder);
```

## Tenant Isolation

### Multi-tenancy Implementation
```go
type UserRepository struct {
    db *gorm.DB
}

// GetUserProfile với tenant isolation
func (r *UserRepository) GetUserProfile(websiteID, userID uint) (*UserProfile, error) {
    var profile UserProfile
    err := r.db.Where("website_id = ? AND user_id = ?", websiteID, userID).
        Preload("User").
        First(&profile).Error
    return &profile, err
}

// GetUsersByWebsite với pagination
func (r *UserRepository) GetUsersByWebsite(websiteID uint, cursor string, limit int) ([]User, error) {
    var users []User
    query := r.db.Where("website_id = ?", websiteID)
    
    if cursor != "" {
        query = query.Where("id > ?", cursor)
    }
    
    err := query.Limit(limit).Order("id ASC").Find(&users).Error
    return users, err
}

// CreateUserActivity với tenant validation
func (r *UserRepository) CreateUserActivity(activity *UserActivity) error {
    // Validate user belongs to website
    var user User
    if err := r.db.Where("id = ? AND website_id = ?", activity.UserID, activity.WebsiteID).
        First(&user).Error; err != nil {
        return errors.New("user not found in website")
    }
    
    return r.db.Create(activity).Error
}

// GetFollowers với tenant filtering
func (r *UserRepository) GetFollowers(websiteID, userID uint, cursor string, limit int) ([]User, error) {
    var followers []User
    query := r.db.
        Table("users").
        Joins("JOIN user_follows ON users.id = user_follows.follower_id").
        Where("user_follows.website_id = ? AND user_follows.following_id = ?", websiteID, userID)
    
    if cursor != "" {
        query = query.Where("users.id > ?", cursor)
    }
    
    err := query.Limit(limit).Order("users.id ASC").Find(&followers).Error
    return followers, err
}
```

### Cache Key Patterns
```go
// Cache keys với website_id prefix
const (
    UserProfileKey    = "website:%d:user:%d:profile"           // website:1:user:123:profile
    UserPrefsKey      = "website:%d:user:%d:prefs:%s"         // website:1:user:123:prefs:ui
    UserActivityKey   = "website:%d:user:%d:activity:%s"      // website:1:user:123:activity:reading
    UserFollowersKey  = "website:%d:user:%d:followers"        // website:1:user:123:followers
    UserFollowingKey  = "website:%d:user:%d:following"        // website:1:user:123:following
    UserBookmarksKey  = "website:%d:user:%d:bookmarks:%s"     // website:1:user:123:bookmarks:folder
    UserAnalyticsKey  = "website:%d:user:%d:analytics:%s"     // website:1:user:123:analytics:engagement
)

// Helper functions
func GetUserProfileCacheKey(websiteID, userID uint) string {
    return fmt.Sprintf(UserProfileKey, websiteID, userID)
}

func GetUserPrefsCacheKey(websiteID, userID uint, category string) string {
    return fmt.Sprintf(UserPrefsKey, websiteID, userID, category)
}
```

### API Middleware với Tenant Validation
```go
func UserTenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get website_id from context (set by auth middleware)
        websiteID, exists := c.Get("website_id")
        if !exists {
            c.JSON(400, gin.H{"error": "missing website context"})
            c.Abort()
            return
        }
        
        // For user-specific endpoints, validate user belongs to website
        userIDParam := c.Param("user_id")
        if userIDParam != "" && userIDParam != "me" {
            userID, _ := strconv.ParseUint(userIDParam, 10, 32)
            
            var user User
            if err := db.Where("id = ? AND website_id = ?", userID, websiteID).First(&user).Error; err != nil {
                c.JSON(404, gin.H{"error": "user not found in website"})
                c.Abort()
                return
            }
        }
        
        c.Next()
    }
}
```

### Elasticsearch Integration với Tenant Isolation
```go
// User search với tenant-specific index
func SearchUsers(websiteID uint, query string) ([]User, error) {
    indexName := fmt.Sprintf("website_%d_users", websiteID)
    
    searchResult, err := esClient.Search().
        Index(indexName).
        Query(elastic.NewMultiMatchQuery(query, "name", "username", "bio")).
        From(0).Size(20).
        Do(context.Background())
    
    if err != nil {
        return nil, err
    }
    
    var users []User
    for _, hit := range searchResult.Hits.Hits {
        var user User
        if err := json.Unmarshal(hit.Source, &user); err == nil {
            users = append(users, user)
        }
    }
    
    return users, nil
}
```

## Configuration

### User Module Settings
```yaml
user:
  enabled: true
  
  profile:
    max_bio_length: 500
    max_interests: 20
    max_skills: 30
    avatar_max_size: "5MB"
    avatar_formats: ["jpg", "png", "webp"]
    
  preferences:
    cache_ttl: "6h"
    auto_save: true
    
  activity:
    tracking_enabled: true
    retention_days: 365
    anonymize_after_days: 90
    
  social:
    max_following: 1000
    max_bookmarks: 5000
    bookmark_folders: 50
    
  privacy:
    gdpr_compliance: true
    data_export_format: "json"
    deletion_grace_period: "30d"
    
  analytics:
    enabled: true
    real_time_updates: true
    insights_generation: true
```

## Best Practices

### Profile Management
- **Complete Profiles**: Encourage profile completion
- **Profile Verification**: Verify email and phone
- **Privacy First**: Default to private settings
- **Regular Updates**: Prompt for profile updates
- **Tenant Isolation**: Always filter by website_id

### Activity Tracking
- **Privacy Conscious**: Respect user privacy preferences
- **Data Minimization**: Collect only necessary data
- **Transparent Tracking**: Clear about what's tracked
- **User Control**: Allow users to opt-out
- **Tenant Boundaries**: Never mix activity data across websites

### Social Features
- **Quality Connections**: Promote meaningful connections
- **Content Discovery**: Help users find relevant content
- **Engagement**: Encourage positive interactions
- **Moderation**: Implement content moderation
- **Website Scoping**: Social connections only within same website

### Multi-tenancy Best Practices
- **Always Filter**: Include website_id in all queries
- **Cache Isolation**: Use website_id prefix in cache keys
- **Index Separation**: Separate Elasticsearch indices per website
- **Validate Ownership**: Verify resources belong to correct website
- **Cross-tenant Security**: Prevent data leakage between websites

## Tài liệu liên quan

### Core Dependencies
- **[Auth Module](./auth.md)** - User authentication, session management, và security features
- **[RBAC Module](./rbac.md)** - Role-based permissions và access control
- **[Tenant Module](./tenant.md)** - Multi-tenancy architecture và website isolation

### Content & Social Features
- **[Blog Module](./blog.md)** - User-generated content, authoring, và publishing
- **[Media Module](./media.md)** - Profile avatars, file uploads, và media management
- **[Notification Module](./notification.md)** - User preferences, email notifications, và alerts
- **[Socket Module](./socket.md)** - Real-time user presence, activity feeds, và messaging

### Architecture & Database
- **[Multi-tenant User Management](../architecture/multi-tenant-user-management.md)** - Chi tiết về global user model
- **[Database Design](../database/database-design.md)** - User schema, indexing, và naming conventions
- **[ERD Schema](../database/erd-schema.md)** - User relationships và database structure

### API & Integration
- **[Response Standard](../api/response-standard.md)** - API response formats cho user endpoints
- **[CMS API](../api/cms-api.md)** - Admin user management APIs
- **[Frontend API](../api/frontend-api.md)** - Public user profile APIs

### Development & Testing
- **[Testing Guidelines](../development/testing.md)** - User module testing strategies
- **[Local Testing](../development/local-testing.md)** - Development environment setup

### Security & Compliance
- **[Security Best Practices](../best-practices/security.md)** - User data protection guidelines
- **[GDPR Compliance](../compliance/gdpr.md)** - Data privacy và user rights
- **[Logging System](../architecture/logging-system.md)** - User activity logging và audit trails

### Performance & Monitoring
- **[Caching Strategy](../performance/caching.md)** - User data caching patterns
- **[Analytics Integration](../integrations/analytics.md)** - User behavior tracking
- **[Search Integration](../integrations/search.md)** - User search và discovery features