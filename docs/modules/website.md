# Module Website - Tài liệu Tiếng Việt

## Tổng quan

Module Website cung cấp hệ thống quản lý website frontend cho mỗi tenant, cho phép tùy chỉnh giao diện, theme, và cấu trúc trang web mà không cần kiến thức lập trình.

## Tính năng chính

- **Quản lý Theme**: Cài đặt, tùy chỉnh theme website
- **Page Builder**: Tạo trang web với drag & drop
- **Menu Management**: Quản lý menu navigation
- **Widget System**: Thêm các widget vào sidebar, footer
- **SEO Optimization**: Tối ưu SEO cho từng trang
- **Media Library**: Quản lý hình ảnh, video, files
- **Custom CSS/JS**: Thêm code tùy chỉnh

## Kiến trúc Module

### Cấu trú<PERSON> thư mục
```
internal/modules/website/
├── models/                   # Các model website
│   ├── theme.go             # Model theme
│   ├── page.go              # Model trang web
│   ├── menu.go              # Model menu
│   ├── widget.go            # Model widget
│   └── media.go             # Model media
├── services/                # Logic nghiệp vụ
│   ├── theme_service.go     # Dịch vụ theme
│   ├── page_service.go      # Dịch vụ trang web
│   ├── media_service.go     # Dịch vụ media
│   └── seo_service.go       # Dịch vụ SEO
├── handlers/                # HTTP handlers
├── repositories/            # Truy cập dữ liệu
├── templates/               # Template engine
└── renderers/               # Render website
```

## Mô hình dữ liệu

### Theme
- **ID**: Định danh theme
- **WebsiteID**: ID website sở hữu (multi-tenancy)
- **TenantID**: ID tenant sở hữu
- **Name**: Tên theme
- **Version**: Phiên bản theme
- **Config**: Cấu hình theme (JSON)
- **Assets**: Đường dẫn assets (CSS, JS, images)
- **Status**: Trạng thái (active, inactive)

### Page
- **ID**: Định danh trang
- **WebsiteID**: ID website (multi-tenancy)
- **TenantID**: ID tenant
- **Title**: Tiêu đề trang
- **Slug**: URL slug
- **Content**: Nội dung trang (JSON blocks)
- **Template**: Template sử dụng
- **SEO**: Metadata SEO
- **Status**: Trạng thái publish

### Menu
- **ID**: Định danh menu
- **WebsiteID**: ID website (multi-tenancy)
- **TenantID**: ID tenant
- **Name**: Tên menu
- **Location**: Vị trí hiển thị
- **Items**: Danh sách menu items (JSON)

### Widget
- **ID**: Định danh widget
- **WebsiteID**: ID website (multi-tenancy)
- **TenantID**: ID tenant
- **Type**: Loại widget
- **Position**: Vị trí hiển thị
- **Config**: Cấu hình widget
- **Order**: Thứ tự hiển thị

## Luồng hoạt động

### 1. Cài đặt Theme

```mermaid
sequenceDiagram
    participant User as Người dùng
    participant Admin as Website Admin
    participant ThemeStore as Theme Store
    participant API as Website API
    participant Storage as File Storage
    
    User->>Admin: Chọn theme từ store
    Admin->>ThemeStore: Download theme package
    ThemeStore->>Admin: Trả về theme files
    Admin->>API: Upload theme
    API->>Storage: Lưu theme files
    API->>API: Parse theme config
    API->>Admin: Theme cài đặt thành công
```

### 2. Tạo trang web với Page Builder

```mermaid
flowchart TD
    A[Bắt đầu tạo trang] --> B[Chọn template]
    B --> C[Drag & Drop blocks]
    C --> D{Thêm block mới?}
    D -->|Có| E[Chọn loại block]
    E --> F[Cấu hình block]
    F --> C
    D -->|Không| G[Preview trang]
    G --> H{Hài lòng?}
    H -->|Không| C
    H -->|Có| I[Lưu trang]
    I --> J[Publish trang]
```

### 3. Render website cho visitor

```mermaid
sequenceDiagram
    participant Visitor as Khách truy cập
    participant CDN as CDN
    participant API as Website API
    participant Cache as Cache System
    participant DB as Database
    
    Visitor->>CDN: Truy cập website
    CDN->>API: Forward request
    API->>Cache: Kiểm tra cache
    Cache->>API: Cache miss
    API->>DB: Lấy dữ liệu trang
    API->>API: Render HTML
    API->>Cache: Lưu cache
    API->>CDN: Trả về HTML
    CDN->>Visitor: Hiển thị website
```

### 4. Quản lý SEO

```mermaid
flowchart LR
    A[Tạo/Sửa trang] --> B[Nhập SEO metadata]
    B --> C[Tự động generate]
    C --> D[Meta title]
    C --> E[Meta description]
    C --> F[Open Graph tags]
    C --> G[Structured data]
    D --> H[Validate SEO]
    E --> H
    F --> H
    G --> H
    H --> I{SEO score OK?}
    I -->|Không| J[Hiển thị đề xuất]
    J --> B
    I -->|Có| K[Lưu trang]
```

## Các loại Block trong Page Builder

### Content Blocks
- **Text Block**: Văn bản với rich editor
- **Heading Block**: Tiêu đề H1-H6
- **Image Block**: Hình ảnh với caption
- **Video Block**: Embed video
- **Quote Block**: Trích dẫn
- **Code Block**: Hiển thị code

### Layout Blocks
- **Container Block**: Wrapper cho các block khác
- **Columns Block**: Chia cột layout
- **Spacer Block**: Khoảng trống
- **Divider Block**: Đường chia
- **Grid Block**: Layout dạng lưới

### Media Blocks
- **Gallery Block**: Thư viện ảnh
- **Slider Block**: Slideshow
- **Audio Block**: Player nhạc
- **File Download Block**: Link download

### Interactive Blocks
- **Button Block**: Nút call-to-action
- **Form Block**: Form liên hệ
- **Social Share Block**: Chia sẻ mạng xã hội
- **Comment Block**: Hệ thống comment

### Dynamic Blocks
- **Blog Posts Block**: Hiển thị bài viết
- **Categories Block**: Danh mục bài viết
- **Tags Block**: Thẻ tag
- **Recent Posts Block**: Bài viết mới nhất
- **Popular Posts Block**: Bài viết phổ biến

## Theme System

### Cấu trúc Theme

```
theme-name/
├── theme.json              # Metadata theme
├── style.css              # CSS chính
├── script.js              # JavaScript
├── templates/             # Template files
│   ├── index.html         # Trang chủ
│   ├── single.html        # Trang bài viết
│   ├── category.html      # Trang danh mục
│   └── page.html          # Trang custom
├── partials/              # Partial templates
│   ├── header.html        # Header
│   ├── footer.html        # Footer
│   └── sidebar.html       # Sidebar
└── assets/                # Static assets
    ├── images/
    ├── fonts/
    └── icons/
```

### Theme Configuration

```json
{
  "name": "Modern Blog",
  "version": "1.0.0",
  "author": "Theme Author",
  "description": "A modern blog theme",
  "settings": {
    "colors": {
      "primary": "#007bff",
      "secondary": "#6c757d",
      "background": "#ffffff"
    },
    "typography": {
      "heading_font": "Roboto",
      "body_font": "Open Sans"
    },
    "layout": {
      "sidebar_position": "right",
      "container_width": "1200px"
    }
  },
  "supported_blocks": [
    "text", "heading", "image", "video",
    "gallery", "button", "form"
  ]
}
```

## Media Management

### Upload Flow

```mermaid
flowchart TD
    A[Chọn file] --> B[Validate file]
    B --> C{File hợp lệ?}
    C -->|Không| D[Hiển thị lỗi]
    C -->|Có| E[Resize/Optimize]
    E --> F[Upload to storage]
    F --> G[Generate thumbnails]
    G --> H[Lưu metadata DB]
    H --> I[Trả về URL]
```

### Tối ưu hóa Media
- **Image Optimization**: Nén ảnh, convert format
- **Thumbnail Generation**: Tạo ảnh thumbnail tự động
- **CDN Integration**: Phân phối qua CDN
- **Lazy Loading**: Load ảnh khi cần
- **WebP Support**: Hỗ trợ format WebP

## SEO Features

### On-Page SEO
- Meta title và description tự động
- URL slug thân thiện
- Heading structure (H1-H6)
- Alt text cho hình ảnh
- Internal linking suggestions

### Technical SEO
- Sitemap XML tự động
- Robots.txt configuration
- Schema markup
- Page speed optimization
- Mobile-friendly design

### Analytics Integration
- Google Analytics
- Google Search Console
- Facebook Pixel
- Custom tracking codes

## API Endpoints

### Theme Management
- `GET /api/v1/themes` - Danh sách theme (filtered by website_id)
- `POST /api/v1/themes` - Upload theme mới
- `PUT /api/v1/themes/{id}` - Cập nhật theme
- `POST /api/v1/themes/{id}/activate` - Kích hoạt theme

### Page Management
- `GET /api/v1/pages` - Danh sách trang (filtered by website_id)
- `POST /api/v1/pages` - Tạo trang mới
- `PUT /api/v1/pages/{id}` - Cập nhật trang
- `POST /api/v1/pages/{id}/publish` - Publish trang

### Media Library
- `GET /api/v1/media` - Danh sách media (filtered by website_id)
- `POST /api/v1/media/upload` - Upload file
- `PUT /api/v1/media/{id}` - Cập nhật metadata
- `DELETE /api/v1/media/{id}` - Xóa file

### Menu Management
- `GET /api/v1/menus` - Danh sách menu (filtered by website_id)
- `POST /api/v1/menus` - Tạo menu mới
- `PUT /api/v1/menus/{id}` - Cập nhật menu

## Frontend Rendering

### Static Site Generation
```mermaid
flowchart LR
    A[Content thay đổi] --> B[Trigger build]
    B --> C[Generate static files]
    C --> D[Optimize assets]
    D --> E[Deploy to CDN]
    E --> F[Update cache]
```

### Caching Strategy
- **Browser Cache**: Cache assets lâu dài
- **CDN Cache**: Cache HTML, CSS, JS
- **Server Cache**: Cache API responses
- **Database Cache**: Cache queries thường dùng

## Performance Optimization

### Asset Optimization
- CSS/JS minification
- Image compression
- Font optimization
- Bundle splitting

### Loading Strategy
- Critical CSS inline
- Non-critical CSS async
- JavaScript lazy loading
- Image lazy loading

## Mobile Responsiveness

### Responsive Design
- Mobile-first approach
- Flexible grid system
- Adaptive images
- Touch-friendly interface

### Progressive Web App
- Service worker
- Offline functionality
- App-like experience
- Push notifications

## Tích hợp với các module khác

### Blog Module
- Hiển thị bài viết trên website
- Trang danh mục và tag
- Trang chi tiết bài viết
- Widget bài viết liên quan

### Auth Module
- Trang đăng nhập/đăng ký
- Trang profile người dùng
- Protected pages
- User-generated content

### Tenant Module
- Multi-tenant website
- Custom domain support
- Tenant-specific themes
- Isolated media libraries

## Multi-Tenancy Isolation

### Repository Pattern với Website ID

```go
type WebsiteRepository interface {
    GetThemesByWebsiteID(websiteID int64) ([]*Theme, error)
    GetPagesByWebsiteID(websiteID int64) ([]*Page, error)
    GetMenusByWebsiteID(websiteID int64) ([]*Menu, error)
    GetWidgetsByWebsiteID(websiteID int64) ([]*Widget, error)
}

type websiteRepository struct {
    db *gorm.DB
}

func (r *websiteRepository) GetThemesByWebsiteID(websiteID int64) ([]*Theme, error) {
    var themes []*Theme
    return themes, r.db.Where("website_id = ?", websiteID).Find(&themes).Error
}

func (r *websiteRepository) GetPagesByWebsiteID(websiteID int64) ([]*Page, error) {
    var pages []*Page
    return pages, r.db.Where("website_id = ?", websiteID).Find(&pages).Error
}
```

### Cache Keys với Website ID

```go
func (s *WebsiteService) getCacheKey(websiteID int64, suffix string) string {
    return fmt.Sprintf("website:website:%d:%s", websiteID, suffix)
}

// Cache theme data
func (s *WebsiteService) cacheTheme(websiteID int64, theme *Theme) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("theme:%s", theme.Name))
    s.cache.Set(key, theme, 1*time.Hour)
}

// Cache page data
func (s *WebsiteService) cachePage(websiteID int64, page *Page) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("page:%s", page.Slug))
    s.cache.Set(key, page, 30*time.Minute)
}

// Cache menu data
func (s *WebsiteService) cacheMenu(websiteID int64, menu *Menu) {
    key := s.getCacheKey(websiteID, fmt.Sprintf("menu:%s", menu.Location))
    s.cache.Set(key, menu, 1*time.Hour)
}
```

### Service Layer với Website Context

```go
type WebsiteService struct {
    repo  WebsiteRepository
    cache CacheService
}

func (s *WebsiteService) GetActiveThemeForWebsite(websiteID int64) (*Theme, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, "active_theme")
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Theme), nil
    }
    
    // Fetch from database with website_id filter
    theme, err := s.repo.GetActiveThemeByWebsiteID(websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cacheTheme(websiteID, theme)
    return theme, nil
}

func (s *WebsiteService) GetPageBySlugForWebsite(websiteID int64, slug string) (*Page, error) {
    // Check cache first
    cacheKey := s.getCacheKey(websiteID, fmt.Sprintf("page:%s", slug))
    if cached, found := s.cache.Get(cacheKey); found {
        return cached.(*Page), nil
    }
    
    // Fetch from database with website_id filter
    page, err := s.repo.GetPageBySlugAndWebsiteID(slug, websiteID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    s.cachePage(websiteID, page)
    return page, nil
}
```

### Website Context Middleware

```go
func WebsiteContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get website ID from subdomain or header
        websiteID := extractWebsiteID(c)
        if websiteID == 0 {
            c.JSON(400, gin.H{"error": "Website ID is required"})
            c.Abort()
            return
        }
        
        c.Set("website_id", websiteID)
        c.Next()
    }
}

func extractWebsiteID(c *gin.Context) int64 {
    // Try from header first
    if websiteID := c.GetHeader("X-Website-ID"); websiteID != "" {
        if id, err := strconv.ParseInt(websiteID, 10, 64); err == nil {
            return id
        }
    }
    
    // Try from subdomain
    host := c.Request.Host
    if subdomain := extractSubdomain(host); subdomain != "" {
        // Look up website by subdomain
        return lookupWebsiteBySubdomain(subdomain)
    }
    
    return 0
}
```

### Response Context

```go
type WebsiteResponse struct {
    Status Status      `json:"status"`
    Data   interface{} `json:"data"`
    Meta   *Meta       `json:"meta,omitempty"`
    Website *WebsiteContext `json:"website,omitempty"`
}

type WebsiteContext struct {
    ID     int64  `json:"id"`
    Domain string `json:"domain"`
    Name   string `json:"name"`
    Theme  string `json:"theme"`
}

func (s *WebsiteService) AddWebsiteContext(response *WebsiteResponse, websiteID int64) {
    if website, err := s.GetWebsiteByID(websiteID); err == nil {
        response.Website = &WebsiteContext{
            ID:     website.ID,
            Domain: website.Domain,
            Name:   website.Name,
            Theme:  website.ActiveTheme,
        }
    }
}
```

## Best Practices

### Design Principles
- Responsive design
- Accessibility standards
- User experience focus
- Brand consistency

### Performance
- Fast loading times
- Optimized images
- Minimal HTTP requests
- Efficient caching

### SEO
- Semantic HTML structure
- Proper heading hierarchy
- Meta tags optimization
- Clean URLs

## Tài liệu liên quan

- [Module System Overview](./overview.md)
- [Blog Module](./blog.md)
- [Tenant Module](./tenant.md)
- [Performance Best Practices](../best-practices/performance.md)