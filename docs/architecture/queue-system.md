# Hệ thống Queue - Tài liệu Tiếng Việt

## Tổng quan

Hệ thống Queue cung cấp cơ chế xử lý bất đồng bộ đáng tin cậy cho các tác vụ nặng, background processing, và giao tiếp giữa các module trong Blog API v3.

## Mục tiêu

- **Asynchronous Processing**: Xử lý bất đồng bộ các tác vụ nặng
- **Reliability**: <PERSON><PERSON><PERSON> bảo tin cậy trong xử lý message
- **Scalability**: Kh<PERSON> năng mở rộng theo tải
- **Fault Tolerance**: Chịu lỗi và phục hồi tự động
- **Performance**: Tối ưu hiệu suất xử lý
- **Monitoring**: <PERSON> dõi và giám sát hệ thống

## Kiến trúc hệ thống

### Queue Architecture Overview

```mermaid
flowchart TD
    A[Producers] --> B[Message Broker]
    B --> C[Queue 1: Email]
    B --> D[Queue 2: Media Processing]
    B --> E[Queue 3: Notifications]
    B --> F[Queue 4: Analytics]
    
    C --> G[Email Workers]
    D --> H[Media Workers]
    E --> I[Notification Workers]
    F --> J[Analytics Workers]
    
    G --> K[SMTP Server]
    H --> L[File Storage]
    I --> M[Push Services]
    J --> N[Analytics DB]
    
    O[Dead Letter Queue] --> B
    P[Retry Queue] --> B
```

### Components

#### Message Broker
- **Redis Streams**: Primary queue implementation
- **RabbitMQ**: Alternative for complex routing
- **Apache Kafka**: For high-throughput scenarios

#### Queue Types
- **Standard Queue**: FIFO processing
- **Priority Queue**: Priority-based processing
- **Delay Queue**: Scheduled/delayed processing
- **Dead Letter Queue**: Failed message handling

#### Workers
- **Concurrent Workers**: Xử lý đồng thời nhiều message
- **Specialized Workers**: Worker chuyên biệt cho từng loại task
- **Auto-scaling Workers**: Tự động scale theo tải

## Message Structure

### Message Format

```go
type QueueMessage struct {
    // Metadata
    ID          string                 `json:"id"`
    Type        string                 `json:"type"`
    Queue       string                 `json:"queue"`
    Priority    int                    `json:"priority"`
    
    // Payload
    Data        interface{}            `json:"data"`
    Headers     map[string]interface{} `json:"headers"`
    
    // Processing
    Attempts    int                    `json:"attempts"`
    MaxAttempts int                    `json:"max_attempts"`
    Delay       time.Duration          `json:"delay"`
    
    // Timestamps
    CreatedAt   time.Time              `json:"created_at"`
    ScheduledAt *time.Time             `json:"scheduled_at"`
    ProcessedAt *time.Time             `json:"processed_at"`
    
    // Tracing
    TraceID     string                 `json:"trace_id"`
    SourceModule string                `json:"source_module"`
}
```

### Message Types

```yaml
# Email Messages
email.welcome:
  queue: "email"
  priority: 5
  data:
    to: "<EMAIL>"
    template: "welcome"
    variables:
      user_name: "John Doe"

email.password_reset:
  queue: "email"
  priority: 10
  data:
    to: "<EMAIL>"
    reset_token: "abc123"

# Media Processing
media.image.resize:
  queue: "media_processing"
  priority: 7
  data:
    file_id: 123
    sizes: ["thumbnail", "medium", "large"]
    
media.video.transcode:
  queue: "media_processing"
  priority: 3
  data:
    file_id: 456
    formats: ["720p", "1080p"]

# Notifications
notification.push:
  queue: "notifications"
  priority: 8
  data:
    user_id: 789
    title: "New comment on your post"
    body: "Someone commented on your blog post"

# Analytics
analytics.track_event:
  queue: "analytics"
  priority: 1
  data:
    event_type: "post_view"
    user_id: 123
    post_id: 456
    timestamp: "2024-01-15T10:30:00Z"
```

## Queue Implementations

### 1. Redis Streams Implementation

```mermaid
flowchart LR
    A[Producer] --> B[Redis Stream]
    B --> C[Consumer Group 1]
    B --> D[Consumer Group 2]
    
    C --> E[Worker 1A]
    C --> F[Worker 1B]
    
    D --> G[Worker 2A]
    D --> H[Worker 2B]
    
    I[Pending List] --> C
    J[Acknowledgment] --> B
```

#### Redis Configuration

```yaml
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  max_len: 10000  # Max messages per stream
  
streams:
  email:
    name: "email_queue"
    consumer_group: "email_workers"
    max_workers: 5
    
  media_processing:
    name: "media_queue" 
    consumer_group: "media_workers"
    max_workers: 3
    
  notifications:
    name: "notification_queue"
    consumer_group: "notification_workers"
    max_workers: 10
```

### 2. RabbitMQ Implementation

```mermaid
flowchart TD
    A[Publisher] --> B[Exchange]
    B --> C[Queue: Email]
    B --> D[Queue: Media]
    B --> E[Queue: Notifications]
    
    C --> F[Email Consumer]
    D --> G[Media Consumer]
    E --> H[Notification Consumer]
    
    I[Dead Letter Exchange] --> J[Dead Letter Queue]
    C --> I
    D --> I
    E --> I
```

#### RabbitMQ Configuration

```yaml
rabbitmq:
  url: "amqp://guest:guest@localhost:5672/"
  
exchanges:
  main:
    name: "blog_api"
    type: "topic"
    durable: true
    
queues:
  email:
    name: "email_queue"
    routing_key: "email.*"
    durable: true
    ttl: 3600000  # 1 hour
    
  media:
    name: "media_queue"
    routing_key: "media.*"
    durable: true
    ttl: 7200000  # 2 hours
```

## Queue Processing Patterns

### 1. Worker Pool Pattern

```mermaid
sequenceDiagram
    participant Queue as Message Queue
    participant Manager as Worker Manager
    participant W1 as Worker 1
    participant W2 as Worker 2
    participant W3 as Worker 3
    
    Queue->>Manager: Messages available
    Manager->>W1: Assign message
    Manager->>W2: Assign message
    Manager->>W3: Assign message
    
    W1->>Manager: Processing complete
    W2->>Manager: Processing failed
    W3->>Manager: Processing complete
    
    Manager->>Queue: Acknowledge success
    Manager->>Queue: Reject and requeue
```

### 2. Priority Processing

```mermaid
flowchart TD
    A[Incoming Messages] --> B{Priority Check}
    B -->|High Priority| C[High Priority Queue]
    B -->|Medium Priority| D[Medium Priority Queue] 
    B -->|Low Priority| E[Low Priority Queue]
    
    C --> F[High Priority Workers]
    D --> G[Medium Priority Workers]
    E --> H[Low Priority Workers]
    
    F --> I[Process Immediately]
    G --> J[Process with Delay]
    H --> K[Process when Available]
```

### 3. Batch Processing

```mermaid
sequenceDiagram
    participant Producer as Message Producer
    participant Collector as Batch Collector
    participant Queue as Batch Queue
    participant Processor as Batch Processor
    
    Producer->>Collector: Message 1
    Producer->>Collector: Message 2
    Producer->>Collector: Message 3
    
    Note over Collector: Collect until batch size or timeout
    
    Collector->>Queue: Batch of messages
    Queue->>Processor: Process batch
    Processor->>Processor: Process all messages
    Processor->>Queue: Batch complete
```

## Error Handling và Retry Logic

### Retry Strategy

```mermaid
flowchart TD
    A[Message Processing] --> B{Success?}
    B -->|Yes| C[Acknowledge Message]
    B -->|No| D[Check Retry Count]
    
    D --> E{Max Retries?}
    E -->|No| F[Calculate Backoff]
    E -->|Yes| G[Send to DLQ]
    
    F --> H[Schedule Retry]
    H --> I[Wait for Backoff]
    I --> A
    
    G --> J[Log Error]
    J --> K[Notify Admin]
```

### Backoff Strategies

#### Exponential Backoff
```yaml
retry_policy:
  max_attempts: 5
  base_delay: "1s"
  max_delay: "300s"
  multiplier: 2
  jitter: true

# Retry delays: 1s, 2s, 4s, 8s, 16s
```

#### Linear Backoff
```yaml
retry_policy:
  max_attempts: 3
  base_delay: "30s"
  increment: "30s"
  
# Retry delays: 30s, 60s, 90s
```

#### Fixed Interval
```yaml
retry_policy:
  max_attempts: 5
  interval: "60s"
  
# Retry delays: 60s, 60s, 60s, 60s, 60s
```

### Dead Letter Queue Handling

```mermaid
sequenceDiagram
    participant Main as Main Queue
    participant DLQ as Dead Letter Queue
    participant Monitor as DLQ Monitor
    participant Admin as Administrator
    
    Main->>DLQ: Failed message after max retries
    DLQ->>Monitor: New message in DLQ
    Monitor->>Admin: Alert about failed message
    
    Admin->>Monitor: Investigate issue
    Monitor->>Admin: Provide error details
    
    Admin->>DLQ: Fix issue and requeue
    DLQ->>Main: Requeue fixed message
```

## Queue Monitoring

### Metrics Collection

```mermaid
graph LR
    A[Queue Metrics] --> B[Message Count]
    A --> C[Processing Rate]
    A --> D[Error Rate]
    A --> E[Latency]
    A --> F[Worker Status]
    
    B --> B1[Pending Messages]
    B --> B2[In Progress]
    B --> B3[Completed]
    B --> B4[Failed]
    
    C --> C1[Messages/Second]
    C --> C2[Throughput]
    
    D --> D1[Failure Rate %]
    D --> D2[DLQ Growth]
    
    E --> E1[Queue Latency]
    E --> E2[Processing Time]
    
    F --> F1[Active Workers]
    F --> F2[Worker Health]
```

### Health Checks

```go
type QueueHealthCheck struct {
    QueueName        string
    PendingMessages  int64
    ProcessingRate   float64
    ErrorRate        float64
    WorkerCount      int
    LastProcessedAt  time.Time
}

func (q *QueueManager) HealthCheck() *QueueHealthCheck {
    return &QueueHealthCheck{
        QueueName:       q.name,
        PendingMessages: q.getPendingCount(),
        ProcessingRate:  q.getProcessingRate(),
        ErrorRate:       q.getErrorRate(),
        WorkerCount:     q.getActiveWorkerCount(),
        LastProcessedAt: q.getLastProcessedTime(),
    }
}
```

### Alerting Rules

```yaml
alerts:
  high_queue_depth:
    condition: "pending_messages > 1000"
    severity: "warning"
    message: "Queue depth is high"
    
  low_processing_rate:
    condition: "processing_rate < 10"
    severity: "critical"
    message: "Processing rate is too low"
    
  high_error_rate:
    condition: "error_rate > 0.05"  # 5%
    severity: "critical" 
    message: "Error rate is too high"
    
  worker_down:
    condition: "active_workers == 0"
    severity: "critical"
    message: "No active workers"
```

## Specific Queue Use Cases

### 1. Email Queue

```mermaid
flowchart TD
    A[Email Request] --> B[Validate Email]
    B --> C[Template Processing]
    C --> D[Queue Message]
    D --> E[Email Worker]
    E --> F[SMTP Send]
    F --> G{Send Success?}
    G -->|Yes| H[Log Success]
    G -->|No| I[Retry Logic]
    I --> J{Max Retries?}
    J -->|No| E
    J -->|Yes| K[Dead Letter Queue]
```

#### Email Message Processing

```go
type EmailMessage struct {
    To          []string
    CC          []string
    BCC         []string
    Subject     string
    Template    string
    Variables   map[string]interface{}
    Attachments []Attachment
    Priority    int
}

func (w *EmailWorker) ProcessMessage(msg *QueueMessage) error {
    emailData := msg.Data.(*EmailMessage)
    
    // Render template
    content, err := w.templateEngine.Render(emailData.Template, emailData.Variables)
    if err != nil {
        return err
    }
    
    // Send email
    return w.smtpClient.Send(&Email{
        To:      emailData.To,
        Subject: emailData.Subject,
        Content: content,
    })
}
```

### 2. Media Processing Queue

```mermaid
flowchart TD
    A[Media Upload] --> B[Queue Processing]
    B --> C[Download Original]
    C --> D[Image Processing]
    D --> E[Generate Thumbnails]
    E --> F[Video Transcoding]
    F --> G[Extract Metadata]
    G --> H[Upload Processed Files]
    H --> I[Update Database]
    I --> J[Notify Completion]
```

#### Media Processing Pipeline

```go
type MediaProcessingMessage struct {
    FileID      uint
    Operations  []string  // ["thumbnail", "resize", "watermark"]
    Sizes       []string  // ["small", "medium", "large"]
    Quality     int
    Format      string
}

func (w *MediaWorker) ProcessMessage(msg *QueueMessage) error {
    mediaData := msg.Data.(*MediaProcessingMessage)
    
    // Download original file
    originalFile, err := w.storage.Download(mediaData.FileID)
    if err != nil {
        return err
    }
    
    // Process based on operations
    for _, operation := range mediaData.Operations {
        switch operation {
        case "thumbnail":
            err = w.generateThumbnails(originalFile, mediaData.Sizes)
        case "resize":
            err = w.resizeImage(originalFile, mediaData.Sizes)
        case "watermark":
            err = w.addWatermark(originalFile)
        }
        
        if err != nil {
            return err
        }
    }
    
    // Update database
    return w.updateFileRecord(mediaData.FileID)
}
```

### 3. Notification Queue

```mermaid
flowchart TD
    A[Notification Event] --> B[User Preferences]
    B --> C{Notification Enabled?}
    C -->|No| D[Skip Notification]
    C -->|Yes| E[Queue Processing]
    E --> F[Format Message]
    F --> G[Send Push Notification]
    G --> H[Send Email]
    H --> I[Update Read Status]
    I --> J[Log Delivery]
```

#### Notification Processing

```go
type NotificationMessage struct {
    UserID    uint
    Type      string
    Title     string
    Body      string
    Data      map[string]interface{}
    Channels  []string  // ["push", "email", "sms"]
}

func (w *NotificationWorker) ProcessMessage(msg *QueueMessage) error {
    notifData := msg.Data.(*NotificationMessage)
    
    // Get user preferences
    prefs, err := w.getUserPreferences(notifData.UserID)
    if err != nil {
        return err
    }
    
    // Send through enabled channels
    for _, channel := range notifData.Channels {
        if prefs.IsChannelEnabled(channel) {
            switch channel {
            case "push":
                err = w.sendPushNotification(notifData)
            case "email":
                err = w.sendEmailNotification(notifData)
            case "sms":
                err = w.sendSMSNotification(notifData)
            }
            
            if err != nil {
                w.logger.Error("Failed to send notification", 
                    "channel", channel, 
                    "error", err)
            }
        }
    }
    
    return nil
}
```

## Performance Optimization

### Connection Pooling

```mermaid
flowchart LR
    A[Workers] --> B[Connection Pool]
    B --> C[Redis Connection 1]
    B --> D[Redis Connection 2]
    B --> E[Redis Connection 3]
    
    F[Pool Manager] --> B
    F --> G[Health Check]
    F --> H[Connection Rotation]
```

### Batch Processing Optimization

```go
type BatchProcessor struct {
    batchSize    int
    batchTimeout time.Duration
    buffer       []QueueMessage
    mutex        sync.Mutex
}

func (bp *BatchProcessor) AddMessage(msg QueueMessage) {
    bp.mutex.Lock()
    defer bp.mutex.Unlock()
    
    bp.buffer = append(bp.buffer, msg)
    
    if len(bp.buffer) >= bp.batchSize {
        go bp.processBatch(bp.buffer)
        bp.buffer = []QueueMessage{}
    }
}

func (bp *BatchProcessor) processBatch(messages []QueueMessage) error {
    // Process all messages in batch
    for _, msg := range messages {
        if err := bp.processMessage(msg); err != nil {
            // Handle individual message error
            bp.handleError(msg, err)
        }
    }
    return nil
}
```

### Auto-scaling Workers

```mermaid
flowchart TD
    A[Queue Monitor] --> B[Check Queue Depth]
    B --> C{Queue Depth > Threshold?}
    C -->|Yes| D[Scale Up Workers]
    C -->|No| E[Check Worker Utilization]
    E --> F{Utilization < Threshold?}
    F -->|Yes| G[Scale Down Workers]
    F -->|No| H[Maintain Current Scale]
    
    D --> I[Start New Workers]
    G --> J[Stop Idle Workers]
    H --> K[Continue Monitoring]
    I --> K
    J --> K
```

## Security Considerations

### Message Encryption

```go
type EncryptedMessage struct {
    EncryptedData string
    KeyID         string
    IV            []byte
}

func (q *QueueManager) EncryptMessage(msg *QueueMessage) (*EncryptedMessage, error) {
    // Serialize message
    data, err := json.Marshal(msg)
    if err != nil {
        return nil, err
    }
    
    // Encrypt data
    encryptedData, iv, err := q.encryption.Encrypt(data)
    if err != nil {
        return nil, err
    }
    
    return &EncryptedMessage{
        EncryptedData: base64.StdEncoding.EncodeToString(encryptedData),
        KeyID:         q.encryption.GetKeyID(),
        IV:            iv,
    }, nil
}
```

### Access Control

```yaml
queue_permissions:
  email_queue:
    producers: ["auth_module", "blog_module"]
    consumers: ["email_service"]
    
  media_queue:
    producers: ["media_module"]
    consumers: ["media_processor"]
    
  notification_queue:
    producers: ["*"]  # All modules can send notifications
    consumers: ["notification_service"]
```

## Deployment và Scaling

### Single Instance Deployment

```mermaid
flowchart TD
    A[Application Server] --> B[Redis]
    A --> C[Worker Processes]
    C --> D[Email Worker]
    C --> E[Media Worker]
    C --> F[Notification Worker]
    
    D --> G[External Services]
    E --> H[File Storage]
    F --> I[Push Services]
```

### Distributed Deployment

```mermaid
flowchart TD
    A[Load Balancer] --> B[App Server 1]
    A --> C[App Server 2]
    A --> D[App Server 3]
    
    B --> E[Redis Cluster]
    C --> E
    D --> E
    
    F[Worker Pool 1] --> E
    G[Worker Pool 2] --> E
    H[Worker Pool 3] --> E
    
    F --> I[Specialized Workers]
    G --> J[Specialized Workers]
    H --> K[Specialized Workers]
```

## Tài liệu liên quan

- [Architecture Overview](./overview.md)
- [Inter-module Communication](./inter-module-communication.md)
- [Performance Best Practices](../best-practices/performance.md)
- [Monitoring Guidelines](../best-practices/monitoring.md)
- [Security Best Practices](../best-practices/security.md)