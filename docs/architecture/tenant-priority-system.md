# Tenant Priority System - <PERSON><PERSON> thống ưu tiên Tenant

## Tổng quan

Hệ thống ưu tiên tenant đ<PERSON><PERSON> bảo rằng các tenant có plan cao hơn sẽ nhận được ưu tiên về performance, tài nguyên và dịch vụ. Tài liệu này mô tả cách implement và quản lý priority system trong Blog API v3.

## Mục tiêu

- **Performance Priority**: Tenant cao cấp có response time nhanh hơn
- **Resource Allocation**: Phân bổ tài nguyên theo priority
- **Queue Priority**: Xử lý jobs theo thứ tự priority
- **Rate Limiting**: Limit khác nhau cho từng plan
- **Support Priority**: Ưu tiên support cho tenant cao cấp

## Plan Hierarchy & Priority Levels

### 1. Plan Priority Mapping

```go
type PlanType string

const (
    PlanFree       PlanType = "free"
    PlanBasic      PlanType = "basic"
    PlanPro        PlanType = "pro"
    PlanEnterprise PlanType = "enterprise"
)

type PlanConfig struct {
    Priority      int    // Higher number = higher priority
    MaxWebsites   int
    MaxUsers      int
    StorageQuota  int64  // bytes
    BandwidthQuota int64 // bytes per month
    RateLimit     RateLimit
    QueuePriority QueuePriority
    Features      []string
}

var PlanConfigs = map[PlanType]PlanConfig{
    PlanFree: {
        Priority:      10,
        MaxWebsites:   1,
        MaxUsers:      5,
        StorageQuota:  1 * 1024 * 1024 * 1024, // 1GB
        BandwidthQuota: 10 * 1024 * 1024 * 1024, // 10GB/month
        RateLimit: RateLimit{
            RequestsPerMinute: 60,
            RequestsPerHour:   1000,
            BurstLimit:        10,
        },
        QueuePriority: QueuePriorityLow,
        Features:      []string{"basic_blog", "basic_seo"},
    },
    PlanBasic: {
        Priority:      20,
        MaxWebsites:   3,
        MaxUsers:      25,
        StorageQuota:  10 * 1024 * 1024 * 1024, // 10GB
        BandwidthQuota: 100 * 1024 * 1024 * 1024, // 100GB/month
        RateLimit: RateLimit{
            RequestsPerMinute: 120,
            RequestsPerHour:   5000,
            BurstLimit:        20,
        },
        QueuePriority: QueuePriorityNormal,
        Features:      []string{"advanced_blog", "advanced_seo", "analytics"},
    },
    PlanPro: {
        Priority:      30,
        MaxWebsites:   10,
        MaxUsers:      100,
        StorageQuota:  50 * 1024 * 1024 * 1024, // 50GB
        BandwidthQuota: 500 * 1024 * 1024 * 1024, // 500GB/month
        RateLimit: RateLimit{
            RequestsPerMinute: 300,
            RequestsPerHour:   15000,
            BurstLimit:        50,
        },
        QueuePriority: QueuePriorityHigh,
        Features:      []string{"premium_blog", "premium_seo", "advanced_analytics", "custom_domain", "api_access"},
    },
    PlanEnterprise: {
        Priority:      40,
        MaxWebsites:   -1, // unlimited
        MaxUsers:      -1, // unlimited
        StorageQuota:  500 * 1024 * 1024 * 1024, // 500GB
        BandwidthQuota: -1, // unlimited
        RateLimit: RateLimit{
            RequestsPerMinute: 1000,
            RequestsPerHour:   50000,
            BurstLimit:        200,
        },
        QueuePriority: QueuePriorityCritical,
        Features:      []string{"enterprise_blog", "enterprise_seo", "enterprise_analytics", "white_label", "priority_support", "sla_guarantee"},
    },
}
```

### 2. Priority Detection Middleware

```go
package middleware

import (
    "context"
    "strconv"
    
    "github.com/gin-gonic/gin"
)

type PriorityMiddleware struct {
    tenantService TenantService
}

func NewPriorityMiddleware(tenantService TenantService) *PriorityMiddleware {
    return &PriorityMiddleware{
        tenantService: tenantService,
    }
}

func (m *PriorityMiddleware) SetPriority() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetUint("website_id")
        if websiteID == 0 {
            c.Next()
            return
        }
        
        // Get tenant priority
        priority, planType, err := m.tenantService.GetTenantPriority(c.Request.Context(), websiteID)
        if err != nil {
            // Default to lowest priority on error
            priority = 10
            planType = PlanFree
        }
        
        // Set priority in context
        c.Set("tenant_priority", priority)
        c.Set("plan_type", planType)
        
        // Set priority headers for downstream services
        c.Header("X-Tenant-Priority", strconv.Itoa(priority))
        c.Header("X-Plan-Type", string(planType))
        
        c.Next()
    }
}

type TenantService interface {
    GetTenantPriority(ctx context.Context, websiteID uint) (priority int, planType PlanType, err error)
}
```

## Rate Limiting với Priority

### 1. Priority-based Rate Limiter

```go
package ratelimit

import (
    "context"
    "fmt"
    "time"
    
    "github.com/go-redis/redis/v8"
)

type PriorityRateLimiter struct {
    redis *redis.Client
}

func NewPriorityRateLimiter(redis *redis.Client) *PriorityRateLimiter {
    return &PriorityRateLimiter{
        redis: redis,
    }
}

func (r *PriorityRateLimiter) CheckLimit(ctx context.Context, websiteID uint, priority int, planType PlanType) error {
    config := PlanConfigs[planType]
    
    // Check per-minute rate limit
    minuteKey := fmt.Sprintf("rate_limit:minute:%d:%s", websiteID, time.Now().Format("2006-01-02T15:04"))
    minuteCount, err := r.redis.Incr(ctx, minuteKey).Result()
    if err != nil {
        return err
    }
    
    if minuteCount == 1 {
        r.redis.Expire(ctx, minuteKey, time.Minute)
    }
    
    if minuteCount > int64(config.RateLimit.RequestsPerMinute) {
        return fmt.Errorf("rate limit exceeded: %d requests per minute", config.RateLimit.RequestsPerMinute)
    }
    
    // Check per-hour rate limit
    hourKey := fmt.Sprintf("rate_limit:hour:%d:%s", websiteID, time.Now().Format("2006-01-02T15"))
    hourCount, err := r.redis.Incr(ctx, hourKey).Result()
    if err != nil {
        return err
    }
    
    if hourCount == 1 {
        r.redis.Expire(ctx, hourKey, time.Hour)
    }
    
    if hourCount > int64(config.RateLimit.RequestsPerHour) {
        return fmt.Errorf("rate limit exceeded: %d requests per hour", config.RateLimit.RequestsPerHour)
    }
    
    return nil
}

// Priority-based burst handling
func (r *PriorityRateLimiter) CheckBurstLimit(ctx context.Context, websiteID uint, planType PlanType) error {
    config := PlanConfigs[planType]
    
    burstKey := fmt.Sprintf("burst_limit:%d:%s", websiteID, time.Now().Format("2006-01-02T15:04:05"))
    burstCount, err := r.redis.Incr(ctx, burstKey).Result()
    if err != nil {
        return err
    }
    
    if burstCount == 1 {
        r.redis.Expire(ctx, burstKey, time.Second)
    }
    
    if burstCount > int64(config.RateLimit.BurstLimit) {
        return fmt.Errorf("burst limit exceeded: %d requests per second", config.RateLimit.BurstLimit)
    }
    
    return nil
}
```

### 2. Rate Limit Middleware

```go
func (r *PriorityRateLimiter) Middleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        websiteID := c.GetUint("website_id")
        priority := c.GetInt("tenant_priority")
        planType := PlanType(c.GetString("plan_type"))
        
        // Check burst limit first
        if err := r.CheckBurstLimit(c.Request.Context(), websiteID, planType); err != nil {
            c.JSON(429, gin.H{
                "error": "Rate limit exceeded",
                "message": err.Error(),
                "retry_after": 1,
            })
            c.Abort()
            return
        }
        
        // Check regular rate limit
        if err := r.CheckLimit(c.Request.Context(), websiteID, priority, planType); err != nil {
            retryAfter := 60 // seconds
            if priority >= 30 { // Pro and Enterprise get shorter retry
                retryAfter = 30
            }
            
            c.JSON(429, gin.H{
                "error": "Rate limit exceeded",
                "message": err.Error(),
                "retry_after": retryAfter,
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## Queue Priority System

### 1. Priority Queue Implementation

```go
package queue

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/hibiken/asynq"
)

type QueuePriority int

const (
    QueuePriorityLow      QueuePriority = 1
    QueuePriorityNormal   QueuePriority = 2
    QueuePriorityHigh     QueuePriority = 3
    QueuePriorityCritical QueuePriority = 4
)

type PriorityQueue struct {
    client *asynq.Client
}

func NewPriorityQueue(client *asynq.Client) *PriorityQueue {
    return &PriorityQueue{
        client: client,
    }
}

func (q *PriorityQueue) EnqueueWithPriority(ctx context.Context, taskType string, payload interface{}, priority QueuePriority, websiteID uint) error {
    data, err := json.Marshal(payload)
    if err != nil {
        return err
    }
    
    task := asynq.NewTask(taskType, data)
    
    // Set priority-based options
    opts := []asynq.Option{
        asynq.Priority(int(priority)),
        asynq.Queue(fmt.Sprintf("priority_%d", priority)),
    }
    
    // Enterprise gets immediate processing
    if priority == QueuePriorityCritical {
        opts = append(opts, asynq.ProcessIn(0))
    } else if priority == QueuePriorityHigh {
        opts = append(opts, asynq.ProcessIn(5*time.Second))
    } else if priority == QueuePriorityNormal {
        opts = append(opts, asynq.ProcessIn(30*time.Second))
    } else {
        opts = append(opts, asynq.ProcessIn(2*time.Minute))
    }
    
    // Add retry policy based on priority
    if priority >= QueuePriorityHigh {
        opts = append(opts, asynq.MaxRetry(5))
    } else {
        opts = append(opts, asynq.MaxRetry(3))
    }
    
    // Add timeout based on priority
    if priority == QueuePriorityCritical {
        opts = append(opts, asynq.Timeout(30*time.Second))
    } else if priority == QueuePriorityHigh {
        opts = append(opts, asynq.Timeout(60*time.Second))
    } else {
        opts = append(opts, asynq.Timeout(5*time.Minute))
    }
    
    _, err = q.client.Enqueue(task, opts...)
    return err
}

// Queue notification with priority
func (q *PriorityQueue) EnqueueNotification(ctx context.Context, notification NotificationPayload, websiteID uint) error {
    // Get tenant priority
    priority := q.getTenantPriority(websiteID)
    
    return q.EnqueueWithPriority(ctx, "send_notification", notification, priority, websiteID)
}

// Queue email with priority
func (q *PriorityQueue) EnqueueEmail(ctx context.Context, email EmailPayload, websiteID uint) error {
    priority := q.getTenantPriority(websiteID)
    
    return q.EnqueueWithPriority(ctx, "send_email", email, priority, websiteID)
}

func (q *PriorityQueue) getTenantPriority(websiteID uint) QueuePriority {
    // This should be cached or retrieved from database
    // For now, return normal priority
    return QueuePriorityNormal
}
```

### 2. Priority Queue Worker

```go
package worker

import (
    "context"
    "log"
    
    "github.com/hibiken/asynq"
)

type PriorityWorker struct {
    server *asynq.Server
}

func NewPriorityWorker(redisOpt asynq.RedisClientOpt) *PriorityWorker {
    // Configure server with priority queues
    server := asynq.NewServer(redisOpt, asynq.Config{
        Concurrency: 20,
        Queues: map[string]int{
            "priority_4": 8,  // Critical - 40% of workers
            "priority_3": 6,  // High - 30% of workers
            "priority_2": 4,  // Normal - 20% of workers
            "priority_1": 2,  // Low - 10% of workers
        },
        StrictPriority: true, // Process higher priority queues first
    })
    
    return &PriorityWorker{
        server: server,
    }
}

func (w *PriorityWorker) Start() error {
    mux := asynq.NewServeMux()
    
    // Register task handlers
    mux.HandleFunc("send_notification", w.handleNotification)
    mux.HandleFunc("send_email", w.handleEmail)
    mux.HandleFunc("process_media", w.handleMedia)
    mux.HandleFunc("generate_report", w.handleReport)
    
    return w.server.Start(mux)
}

func (w *PriorityWorker) handleNotification(ctx context.Context, task *asynq.Task) error {
    // Handle with priority awareness
    priority := w.getTaskPriority(task)
    
    log.Printf("Processing notification with priority %d", priority)
    
    // Process notification
    return nil
}
```

## Database Connection Pool Priority

### 1. Priority-based Connection Pool

```go
package database

import (
    "context"
    "database/sql"
    "time"
    
    "gorm.io/gorm"
)

type PriorityConnectionPool struct {
    highPriorityPool *sql.DB
    normalPool       *sql.DB
    lowPriorityPool  *sql.DB
}

func NewPriorityConnectionPool(config DatabaseConfig) *PriorityConnectionPool {
    // High priority pool - more connections, faster timeout
    highPriorityDB := createConnection(DatabasePoolConfig{
        MaxIdleConns:    20,
        MaxOpenConns:    100,
        ConnMaxLifetime: 30 * time.Minute,
        ConnMaxIdleTime: 10 * time.Minute,
    })
    
    // Normal priority pool
    normalDB := createConnection(DatabasePoolConfig{
        MaxIdleConns:    10,
        MaxOpenConns:    50,
        ConnMaxLifetime: 1 * time.Hour,
        ConnMaxIdleTime: 30 * time.Minute,
    })
    
    // Low priority pool - fewer connections, longer timeout
    lowPriorityDB := createConnection(DatabasePoolConfig{
        MaxIdleConns:    5,
        MaxOpenConns:    20,
        ConnMaxLifetime: 2 * time.Hour,
        ConnMaxIdleTime: 1 * time.Hour,
    })
    
    return &PriorityConnectionPool{
        highPriorityPool: highPriorityDB,
        normalPool:       normalDB,
        lowPriorityPool:  lowPriorityDB,
    }
}

func (p *PriorityConnectionPool) GetConnection(priority int) *gorm.DB {
    var db *sql.DB
    
    switch {
    case priority >= 30: // Pro and Enterprise
        db = p.highPriorityPool
    case priority >= 20: // Basic
        db = p.normalPool
    default: // Free
        db = p.lowPriorityPool
    }
    
    return gorm.New(gorm.Config{}, &gorm.Config{
        ConnPool: db,
    })
}
```

### 2. Priority Database Middleware

```go
func (p *PriorityConnectionPool) Middleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        priority := c.GetInt("tenant_priority")
        
        // Get priority-based connection
        db := p.GetConnection(priority)
        
        // Set database in context
        c.Set("db", db)
        
        c.Next()
    }
}
```

## Caching Priority System

### 1. Priority-based Cache

```go
package cache

import (
    "context"
    "time"
    
    "github.com/go-redis/redis/v8"
)

type PriorityCache struct {
    highPriorityClient *redis.Client
    normalClient       *redis.Client
    lowPriorityClient  *redis.Client
}

func NewPriorityCache(config CacheConfig) *PriorityCache {
    // High priority - dedicated Redis instance
    highPriorityClient := redis.NewClient(&redis.Options{
        Addr:         config.HighPriorityAddr,
        Password:     config.Password,
        DB:           0,
        PoolSize:     100,
        MinIdleConns: 20,
    })
    
    // Normal priority - shared Redis instance
    normalClient := redis.NewClient(&redis.Options{
        Addr:         config.NormalAddr,
        Password:     config.Password,
        DB:           1,
        PoolSize:     50,
        MinIdleConns: 10,
    })
    
    // Low priority - basic Redis instance
    lowPriorityClient := redis.NewClient(&redis.Options{
        Addr:         config.LowPriorityAddr,
        Password:     config.Password,
        DB:           2,
        PoolSize:     20,
        MinIdleConns: 5,
    })
    
    return &PriorityCache{
        highPriorityClient: highPriorityClient,
        normalClient:       normalClient,
        lowPriorityClient:  lowPriorityClient,
    }
}

func (c *PriorityCache) GetClient(priority int) *redis.Client {
    switch {
    case priority >= 30: // Pro and Enterprise
        return c.highPriorityClient
    case priority >= 20: // Basic
        return c.normalClient
    default: // Free
        return c.lowPriorityClient
    }
}

func (c *PriorityCache) Set(ctx context.Context, key string, value interface{}, priority int, expiration time.Duration) error {
    client := c.GetClient(priority)
    
    // Priority-based expiration
    if priority >= 30 {
        expiration = expiration * 2 // Longer cache for high priority
    } else if priority <= 15 {
        expiration = expiration / 2 // Shorter cache for low priority
    }
    
    return client.Set(ctx, key, value, expiration).Err()
}
```

## CDN Priority & Performance

### 1. Priority-based CDN Configuration

```go
package cdn

import (
    "fmt"
    "net/http"
)

type PriorityCDN struct {
    enterpriseEndpoint string
    premiumEndpoint    string
    standardEndpoint   string
}

func NewPriorityCDN(config CDNConfig) *PriorityCDN {
    return &PriorityCDN{
        enterpriseEndpoint: config.EnterpriseEndpoint,
        premiumEndpoint:    config.PremiumEndpoint,
        standardEndpoint:   config.StandardEndpoint,
    }
}

func (c *PriorityCDN) GetCDNURL(priority int, path string) string {
    switch {
    case priority >= 40: // Enterprise
        return fmt.Sprintf("%s/%s", c.enterpriseEndpoint, path)
    case priority >= 30: // Pro
        return fmt.Sprintf("%s/%s", c.premiumEndpoint, path)
    default: // Basic and Free
        return fmt.Sprintf("%s/%s", c.standardEndpoint, path)
    }
}

func (c *PriorityCDN) SetCacheHeaders(w http.ResponseWriter, priority int) {
    // Priority-based cache headers
    switch {
    case priority >= 30:
        w.Header().Set("Cache-Control", "public, max-age=86400") // 24 hours
        w.Header().Set("Edge-Cache-Tag", "priority-high")
    case priority >= 20:
        w.Header().Set("Cache-Control", "public, max-age=43200") // 12 hours
        w.Header().Set("Edge-Cache-Tag", "priority-normal")
    default:
        w.Header().Set("Cache-Control", "public, max-age=21600") // 6 hours
        w.Header().Set("Edge-Cache-Tag", "priority-low")
    }
}
```

## Monitoring & Alerts

### 1. Priority-based Monitoring

```go
package monitoring

import (
    "context"
    "time"
    
    "github.com/prometheus/client_golang/prometheus"
)

type PriorityMetrics struct {
    RequestsByPriority  *prometheus.CounterVec
    ResponseTimeByPriority *prometheus.HistogramVec
    ErrorsByPriority    *prometheus.CounterVec
    QueueSizeByPriority *prometheus.GaugeVec
}

func NewPriorityMetrics() *PriorityMetrics {
    return &PriorityMetrics{
        RequestsByPriority: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "requests_total_by_priority",
                Help: "Total requests by tenant priority",
            },
            []string{"priority", "plan_type"},
        ),
        ResponseTimeByPriority: prometheus.NewHistogramVec(
            prometheus.HistogramOpts{
                Name: "response_time_by_priority",
                Help: "Response time by tenant priority",
                Buckets: []float64{0.1, 0.5, 1.0, 2.5, 5.0, 10.0},
            },
            []string{"priority", "plan_type"},
        ),
        ErrorsByPriority: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "errors_total_by_priority",
                Help: "Total errors by tenant priority",
            },
            []string{"priority", "plan_type", "error_type"},
        ),
        QueueSizeByPriority: prometheus.NewGaugeVec(
            prometheus.GaugeOpts{
                Name: "queue_size_by_priority",
                Help: "Queue size by priority level",
            },
            []string{"priority", "queue_name"},
        ),
    }
}

func (m *PriorityMetrics) RecordRequest(priority int, planType string, duration time.Duration) {
    priorityStr := fmt.Sprintf("%d", priority)
    
    m.RequestsByPriority.WithLabelValues(priorityStr, planType).Inc()
    m.ResponseTimeByPriority.WithLabelValues(priorityStr, planType).Observe(duration.Seconds())
}

func (m *PriorityMetrics) RecordError(priority int, planType string, errorType string) {
    priorityStr := fmt.Sprintf("%d", priority)
    
    m.ErrorsByPriority.WithLabelValues(priorityStr, planType, errorType).Inc()
}
```

### 2. Priority-based Alerting

```yaml
# alerting/priority-alerts.yml
groups:
- name: priority-alerts
  rules:
  - alert: HighPriorityTenantDown
    expr: up{priority="40"} == 0
    for: 1m
    labels:
      severity: critical
      priority: enterprise
    annotations:
      summary: "Enterprise tenant service is down"
      description: "Enterprise tenant {{ $labels.tenant_id }} is experiencing downtime"
      
  - alert: HighPrioritySlowResponse
    expr: histogram_quantile(0.95, rate(response_time_by_priority_bucket{priority="40"}[5m])) > 1
    for: 2m
    labels:
      severity: warning
      priority: enterprise
    annotations:
      summary: "Enterprise tenant slow response time"
      description: "Enterprise tenant response time is {{ $value }}s"
      
  - alert: HighPriorityQueueBacklog
    expr: queue_size_by_priority{priority="4"} > 100
    for: 5m
    labels:
      severity: warning
      priority: enterprise
    annotations:
      summary: "High priority queue backlog"
      description: "High priority queue has {{ $value }} pending jobs"
```

## Best Practices

### 1. Priority Implementation
- **Graceful Degradation**: Lower priority tenants get reduced features, not broken features
- **Fair Usage**: Prevent one high-priority tenant from starving others
- **Monitoring**: Track priority-based metrics continuously
- **SLA Compliance**: Ensure high-priority tenants meet SLA requirements

### 2. Resource Management
- **Connection Pooling**: Separate pools for different priorities
- **Queue Management**: Dedicated queues with appropriate worker allocation
- **Cache Strategy**: Priority-based cache distribution
- **CDN Configuration**: Edge caching based on priority

### 3. Security Considerations
- **Priority Validation**: Validate priority levels to prevent escalation
- **Rate Limiting**: Implement progressive rate limiting
- **Audit Logging**: Log priority-based resource usage
- **Access Control**: Prevent priority manipulation

### 4. Performance Optimization
- **Database Indexing**: Optimize queries for priority-based filtering
- **Cache Warming**: Pre-warm cache for high-priority tenants
- **Load Balancing**: Route high-priority requests to faster servers
- **Resource Allocation**: Dynamic resource allocation based on priority

## Implementation Checklist

- [ ] Configure plan-based priority levels
- [ ] Implement priority middleware
- [ ] Set up priority-based rate limiting
- [ ] Configure priority queue system
- [ ] Implement priority database pools
- [ ] Set up priority-based caching
- [ ] Configure CDN priority routing
- [ ] Implement priority monitoring
- [ ] Set up priority-based alerting
- [ ] Test priority system thoroughly
- [ ] Document priority behaviors
- [ ] Train support team on priority handling