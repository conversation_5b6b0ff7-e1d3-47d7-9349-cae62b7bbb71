# Giao tiếp giữa các Module - T<PERSON><PERSON> liệu Tiếng Việt

## Tổng quan

Tài liệu này mô tả các cơ chế giao tiếp giữa các module trong hệ thống Blog API v3, bao gồm event-driven communication, direct service calls, message queues và shared data patterns.

## Mục tiêu

- **Loose Coupling**: Gi<PERSON>m sự phụ thuộc giữa các module
- **Scalability**: Hỗ trợ scale independent cho từng module
- **Reliability**: Đ<PERSON><PERSON> bảo tin cậy trong giao tiếp
- **Performance**: Tối ưu hiệu suất communication
- **Maintainability**: <PERSON><PERSON> bảo trì và mở rộng

## Các phương thức giao tiếp

### 1. Event-Driven Communication

Event-driven là phương thức chính để giao tiếp giữa các module.

```mermaid
graph TD
    A[Module A] -->|Publish Event| B[Event Bus]
    B -->|Subscribe| C[Module B]
    B -->|Subscribe| D[Module C]
    B -->|Subscribe| E[Module D]
    
    F[Event Store] --> B
    B --> F
```

#### Event Bus Architecture

```go
type EventBus interface {
    Publish(event Event) error
    Subscribe(eventType string, handler EventHandler) error
    Unsubscribe(eventType string, handlerID string) error
}

type Event struct {
    ID        string
    Type      string
    Source    string
    Data      interface{}
    Timestamp time.Time
    TenantID  string
}

type EventHandler func(event Event) error
```

#### Ví dụ Events

```yaml
# User Events
user.registered:
  source: auth_module
  data:
    user_id: 123
    email: "<EMAIL>"
    tenant_id: "tenant_1"

user.updated:
  source: auth_module
  data:
    user_id: 123
    changes: ["name", "avatar"]

# Blog Events  
post.created:
  source: blog_module
  data:
    post_id: 456
    author_id: 123
    title: "New Blog Post"
    status: "draft"

post.published:
  source: blog_module
  data:
    post_id: 456
    author_id: 123
    published_at: "2024-01-15T10:30:00Z"

# Media Events
media.uploaded:
  source: media_module
  data:
    file_id: 789
    user_id: 123
    file_type: "image"
    file_size: 1024000
```

### 2. Direct Service Communication

Gọi trực tiếp service giữa các module cho các tác vụ đồng bộ.

```mermaid
sequenceDiagram
    participant Blog as Blog Module
    participant Auth as Auth Service
    participant Media as Media Service
    participant RBAC as RBAC Service
    
    Blog->>Auth: GetUser(userID)
    Auth->>Blog: User data
    
    Blog->>RBAC: CheckPermission(userID, "posts:create")
    RBAC->>Blog: Permission granted
    
    Blog->>Media: ValidateMedia(mediaIDs)
    Media->>Blog: Media validation result
    
    Blog->>Blog: CreatePost()
```

#### Service Interface Pattern

```go
// Shared interfaces
type UserService interface {
    GetUser(id uint) (*User, error)
    GetUsersByIDs(ids []uint) ([]*User, error)
    IsUserActive(id uint) (bool, error)
}

type MediaService interface {
    GetFile(id uint) (*MediaFile, error)
    ValidateFiles(ids []uint) error
    GetFilesByIDs(ids []uint) ([]*MediaFile, error)
}

type RBACService interface {
    CheckPermission(userID uint, permission string) (bool, error)
    GetUserRoles(userID uint) ([]*Role, error)
    HasRole(userID uint, roleName string) (bool, error)
}
```

### 3. Message Queue Communication

Sử dụng message queue cho các tác vụ async và reliable processing.

```mermaid
flowchart TD
    A[Module Producer] --> B[Message Queue]
    B --> C[Worker 1]
    B --> D[Worker 2] 
    B --> E[Worker 3]
    
    C --> F[Process Message]
    D --> F
    E --> F
    
    F --> G[Success]
    F --> H[Retry]
    F --> I[Dead Letter Queue]
```

#### Queue Message Structure

```go
type QueueMessage struct {
    ID          string
    Type        string
    Payload     interface{}
    Priority    int
    Delay       time.Duration
    MaxRetries  int
    RetryCount  int
    CreatedAt   time.Time
    ProcessedAt *time.Time
}
```

### 4. Shared Data Communication

Chia sẻ dữ liệu thông qua database và cache.

```mermaid
flowchart LR
    A[Module A] --> B[Shared Database]
    C[Module B] --> B
    D[Module C] --> B
    
    A --> E[Redis Cache]
    C --> E
    D --> E
    
    B --> F[Data Consistency]
    E --> G[Performance Cache]
```

## Communication Patterns

### 1. Publish-Subscribe Pattern

```mermaid
sequenceDiagram
    participant AuthModule as Auth Module
    participant EventBus as Event Bus
    participant BlogModule as Blog Module
    participant NotificationModule as Notification Module
    participant OnboardingModule as Onboarding Module
    
    AuthModule->>EventBus: Publish "user.registered"
    EventBus->>BlogModule: Notify new user
    EventBus->>NotificationModule: Send welcome email
    EventBus->>OnboardingModule: Start onboarding flow
    
    Note over EventBus: Asynchronous, decoupled communication
```

#### Event Subscription Examples

```go
// Blog Module subscribes to user events
func (b *BlogModule) Init() {
    eventBus.Subscribe("user.registered", b.handleUserRegistered)
    eventBus.Subscribe("user.deleted", b.handleUserDeleted)
}

func (b *BlogModule) handleUserRegistered(event Event) error {
    userData := event.Data.(UserRegisteredData)
    
    // Create default categories for new user
    return b.createDefaultCategories(userData.UserID)
}

// Notification Module subscribes to various events
func (n *NotificationModule) Init() {
    eventBus.Subscribe("post.published", n.handlePostPublished)
    eventBus.Subscribe("comment.created", n.handleCommentCreated)
    eventBus.Subscribe("user.followed", n.handleUserFollowed)
}
```

### 2. Request-Response Pattern

```mermaid
sequenceDiagram
    participant Client as Client
    participant BlogAPI as Blog API
    participant AuthService as Auth Service
    participant RBACService as RBAC Service
    participant MediaService as Media Service
    
    Client->>BlogAPI: Create Post Request
    BlogAPI->>AuthService: Validate User Token
    AuthService->>BlogAPI: User Valid
    BlogAPI->>RBACService: Check Create Permission
    RBACService->>BlogAPI: Permission Granted
    BlogAPI->>MediaService: Validate Media Files
    MediaService->>BlogAPI: Media Valid
    BlogAPI->>Client: Post Created Successfully
```

### 3. Saga Pattern (Distributed Transactions)

```mermaid
sequenceDiagram
    participant Orchestrator as Saga Orchestrator
    participant Auth as Auth Module
    participant Blog as Blog Module
    participant Media as Media Module
    participant Notification as Notification Module
    
    Orchestrator->>Auth: Create User Account
    Auth->>Orchestrator: User Created
    
    Orchestrator->>Blog: Setup User Blog
    Blog->>Orchestrator: Blog Setup Complete
    
    Orchestrator->>Media: Create Media Folder
    Media->>Orchestrator: Folder Created
    
    Orchestrator->>Notification: Send Welcome Email
    Notification->>Orchestrator: Email Sent
    
    Note over Orchestrator: All steps completed successfully
    
    alt If any step fails
        Orchestrator->>Notification: Compensate (Cancel Email)
        Orchestrator->>Media: Compensate (Delete Folder)
        Orchestrator->>Blog: Compensate (Delete Blog Setup)
        Orchestrator->>Auth: Compensate (Delete User)
    end
```

## Event Types và Flows

### User Lifecycle Events

```mermaid
flowchart TD
    A[user.registered] --> B[Onboarding Module]
    A --> C[Blog Module]
    A --> D[Notification Module]
    
    B --> E[Create onboarding journey]
    C --> F[Setup default categories]
    D --> G[Send welcome email]
    
    H[user.updated] --> I[Profile sync across modules]
    J[user.deleted] --> K[Cleanup user data]
```

### Content Lifecycle Events

```mermaid
flowchart TD
    A[post.created] --> B[Media Module]
    A --> C[Search Module]
    A --> D[Analytics Module]
    
    B --> E[Process featured image]
    C --> F[Index content]
    D --> G[Track creation metrics]
    
    H[post.published] --> I[Notification Module]
    H --> J[Socket Module]
    H --> K[SEO Module]
    
    I --> L[Notify followers]
    J --> M[Real-time updates]
    K --> N[Update sitemap]
```

### Media Processing Events

```mermaid
flowchart TD
    A[media.uploaded] --> B[Processing Queue]
    B --> C[Image Processor]
    B --> D[Video Processor]
    B --> E[Security Scanner]
    
    C --> F[media.image.processed]
    D --> G[media.video.processed]
    E --> H[media.security.scanned]
    
    F --> I[Update thumbnails]
    G --> J[Update video metadata]
    H --> K[Update security status]
```

## Module Interaction Examples

### 1. Creating a Blog Post

> **📖 Chi tiết đầy đủ**: Xem [Blog Module - Post Creation Flow](../modules/blog.md#post-creation-flow) và [Blog Submission Flow](../modules/blog-submission-flow.md) để biết sequence diagrams chi tiết và implementation details.

**Tóm tắt luồng**:
1. User gửi POST request tạo bài viết
2. Auth Module validate token và permissions
3. RBAC Module kiểm tra quyền "posts:create"
4. Media Module validate media files (nếu có)
5. Blog Module tạo post trong database
6. Event Bus publish "post.created" event cho các module khác xử lý
    
    EventBus->>NotificationModule: Handle post creation
    NotificationModule->>NotificationModule: Queue notifications
    
    BlogAPI->>User: Post created successfully
```

### 2. User Registration Flow

> **📖 Chi tiết đầy đủ**: Xem [Auth Module - User Registration Flow](../modules/auth.md#user-registration-flow) để biết sequence diagram chi tiết và implementation details.

**Tóm tắt luồng**:
1. User gửi registration request đến Auth API
2. Auth Module tạo user account và publish "user.registered" event
3. Các module khác consume event để thực hiện các tác vụ liên quan:
   - **Onboarding Module**: Tạo user journey
   - **Blog Module**: Setup default categories
   - **Notification Module**: Gửi welcome email
   - **Analytics Module**: Track registration event
    
    AuthAPI->>User: Registration successful
```

### 3. File Upload and Processing

```mermaid
sequenceDiagram
    participant User as User
    participant MediaAPI as Media API
    participant MediaModule as Media Module
    participant Queue as Processing Queue
    participant Worker as Media Worker
    participant EventBus as Event Bus
    participant BlogModule as Blog Module
    
    User->>MediaAPI: Upload file
    MediaAPI->>MediaModule: Store file metadata
    MediaModule->>Queue: Queue processing job
    MediaModule->>EventBus: Publish "media.uploaded"
    
    Worker->>Queue: Process file
    Worker->>Worker: Generate thumbnails
    Worker->>Worker: Extract metadata
    Worker->>MediaModule: Update file record
    Worker->>EventBus: Publish "media.processed"
    
    EventBus->>BlogModule: Update post media
    
    MediaAPI->>User: Upload complete
```

## Error Handling và Compensation

### Error Propagation

```mermaid
flowchart TD
    A[Module Error] --> B{Error Type?}
    B -->|Recoverable| C[Retry Logic]
    B -->|Non-recoverable| D[Compensation Flow]
    
    C --> E{Max Retries?}
    E -->|No| F[Retry with Backoff]
    E -->|Yes| D
    
    F --> G[Log Retry Attempt]
    G --> A
    
    D --> H[Rollback Changes]
    H --> I[Notify Other Modules]
    I --> J[Update Status]
```

### Compensation Pattern

```go
type CompensationAction struct {
    ID          string
    Action      func() error
    Description string
    Module      string
}

type SagaTransaction struct {
    ID                    string
    Steps                []SagaStep
    CompensationActions  []CompensationAction
    Status               string
}

func (s *SagaTransaction) Execute() error {
    for i, step := range s.Steps {
        if err := step.Execute(); err != nil {
            // Compensate all previous steps
            for j := i - 1; j >= 0; j-- {
                s.CompensationActions[j].Action()
            }
            return err
        }
    }
    return nil
}
```

## Performance Optimization

### Connection Pooling

```mermaid
flowchart LR
    A[Module A] --> B[Connection Pool]
    C[Module B] --> B
    D[Module C] --> B
    
    B --> E[Service Instance 1]
    B --> F[Service Instance 2]
    B --> G[Service Instance 3]
    
    E --> H[Database]
    F --> H
    G --> H
```

### Caching Strategy

```mermaid
flowchart TD
    A[Service Call] --> B[Check Cache]
    B --> C{Cache Hit?}
    C -->|Yes| D[Return Cached Data]
    C -->|No| E[Call Target Service]
    E --> F[Store in Cache]
    F --> G[Return Data]
    
    H[Cache Invalidation] --> I[Event-based]
    H --> J[TTL-based]
    H --> K[Manual]
```

### Batch Operations

```go
type BatchRequest struct {
    Operations []Operation
    BatchSize  int
    Timeout    time.Duration
}

type BatchProcessor struct {
    queue   chan Operation
    workers int
}

func (bp *BatchProcessor) ProcessBatch(operations []Operation) error {
    batches := bp.createBatches(operations, 10)
    
    for _, batch := range batches {
        go bp.processBatch(batch)
    }
    
    return nil
}
```

## Monitoring và Observability

### Inter-module Communication Metrics

```mermaid
graph LR
    A[Communication Metrics] --> B[Request Count]
    A --> C[Response Time]
    A --> D[Error Rate]
    A --> E[Queue Depth]
    A --> F[Event Processing Time]
    
    B --> B1[Per Module Pair]
    B --> B2[Per Operation]
    
    C --> C1[Average Latency]
    C --> C2[95th Percentile]
    
    D --> D1[Error Types]
    D --> D2[Failure Reasons]
    
    E --> E1[Queue Backlog]
    E --> E2[Processing Rate]
    
    F --> F1[Event Latency]
    F --> F2[Processing Throughput]
```

### Distributed Tracing

```mermaid
sequenceDiagram
    participant Client as Client
    participant BlogAPI as Blog API
    participant AuthService as Auth Service
    participant RBACService as RBAC Service
    participant MediaService as Media Service
    
    Note over Client,MediaService: Trace ID: abc123
    
    Client->>BlogAPI: Request (Trace: abc123)
    BlogAPI->>AuthService: Validate (Trace: abc123, Span: auth)
    AuthService->>BlogAPI: Response (Trace: abc123, Span: auth)
    BlogAPI->>RBACService: Check (Trace: abc123, Span: rbac)
    RBACService->>BlogAPI: Response (Trace: abc123, Span: rbac)
    BlogAPI->>MediaService: Validate (Trace: abc123, Span: media)
    MediaService->>BlogAPI: Response (Trace: abc123, Span: media)
    BlogAPI->>Client: Response (Trace: abc123)
```

## Security Considerations

### Inter-module Authentication

```go
type InterModuleAuth struct {
    SharedSecret string
    TokenTTL     time.Duration
}

func (ima *InterModuleAuth) GenerateToken(sourceModule string) (string, error) {
    claims := jwt.MapClaims{
        "source": sourceModule,
        "exp":    time.Now().Add(ima.TokenTTL).Unix(),
        "iat":    time.Now().Unix(),
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(ima.SharedSecret))
}
```

### Message Encryption

```mermaid
flowchart LR
    A[Sensitive Data] --> B[Encrypt Message]
    B --> C[Send to Queue]
    C --> D[Receive Message]
    D --> E[Decrypt Message]
    E --> F[Process Data]
```

## Best Practices

### Communication Design
- **Async First**: Ưu tiên giao tiếp bất đồng bộ
- **Idempotent Operations**: Đảm bảo operations có thể retry
- **Circuit Breaker**: Sử dụng circuit breaker pattern
- **Graceful Degradation**: Xử lý lỗi một cách mềm mại

### Event Design
- **Event Versioning**: Version events để backward compatibility
- **Event Schema**: Định nghĩa schema rõ ràng
- **Event Ordering**: Đảm bảo thứ tự events khi cần
- **Event Replay**: Hỗ trợ replay events

### Performance
- **Batch Processing**: Xử lý theo batch khi có thể
- **Connection Pooling**: Sử dụng connection pool
- **Caching**: Cache kết quả gọi service
- **Lazy Loading**: Load dữ liệu khi cần

### Error Handling
- **Retry Logic**: Implement retry với backoff
- **Compensation**: Xử lý rollback khi cần
- **Dead Letter Queue**: Queue cho messages thất bại
- **Circuit Breaker**: Ngăn chặn cascade failures

## Tài liệu liên quan

### Architecture & Design
- **[Architecture Overview](./overview.md)** - Tổng quan kiến trúc hệ thống
- **[Queue System](./queue-system.md)** - Message queue và background processing
- **[Project Structure](./project-structure.md)** - Cấu trúc code và module organization

### Module Implementation
- **[Auth Module](../modules/auth.md)** - Authentication flows và user registration
- **[Blog Module](../modules/blog.md)** - Blog post creation và content management
- **[Notification Module](../modules/notification.md)** - Email và push notification system

### Development & Operations
- **[Testing Guidelines](../development/testing.md)** - Testing inter-module communication
- **[Local Testing](../development/local-testing.md)** - Development environment setup
- **[Performance Best Practices](../best-practices/performance.md)** - Optimization strategies
- **[Security Guidelines](../best-practices/security.md)** - Security implementation