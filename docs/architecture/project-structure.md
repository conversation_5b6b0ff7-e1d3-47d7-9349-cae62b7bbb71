# Project Structure Overview

```
blog-api-v3/
├── cmd/
│   ├── api/
│   │   └── main.go
│   ├── worker/
│   │   └── main.go
│   ├── cli/
│   │   └── main.go
│   └── migrate/
│       └── main.go
├── internal/
│   ├── config/
│   │   ├── config.go
│   │   ├── database.go
│   │   ├── redis.go
│   │   ├── queue.go
│   │   └── logger.go
│   ├── middleware/
│   │   ├── auth.go
│   │   ├── tenant.go
│   │   ├── priority.go
│   │   ├── rate_limit.go
│   │   ├── cors.go
│   │   ├── logger.go
│   │   └── recovery.go
│   ├── modules/
│   │   ├── auth/
│   │   │   ├── models/
│   │   │   │   ├── user.go
│   │   │   │   ├── session.go
│   │   │   │   └── token.go
│   │   │   ├── repositories/
│   │   │   │   ├── user_repository.go
│   │   │   │   ├── session_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── auth_service.go
│   │   │   │   ├── jwt_service.go
│   │   │   │   ├── password_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── auth_handler.go
│   │   │   │   ├── user_handler.go
│   │   │   │   └── password_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   ├── validators/
│   │   │   │   ├── auth_validator.go
│   │   │   │   └── password_validator.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── user/
│   │   │   ├── models/
│   │   │   │   ├── user.go
│   │   │   │   ├── profile.go
│   │   │   │   └── preferences.go
│   │   │   ├── repositories/
│   │   │   │   ├── user_repository.go
│   │   │   │   ├── profile_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── user_service.go
│   │   │   │   ├── profile_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── user_handler.go
│   │   │   │   └── profile_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   ├── validators/
│   │   │   │   └── user_validator.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── blog/
│   │   │   ├── models/
│   │   │   │   ├── post.go
│   │   │   │   ├── category.go
│   │   │   │   ├── tag.go
│   │   │   │   ├── comment.go
│   │   │   │   └── author.go
│   │   │   ├── repositories/
│   │   │   │   ├── post_repository.go
│   │   │   │   ├── category_repository.go
│   │   │   │   ├── tag_repository.go
│   │   │   │   ├── comment_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── post_service.go
│   │   │   │   ├── category_service.go
│   │   │   │   ├── tag_service.go
│   │   │   │   ├── comment_service.go
│   │   │   │   ├── search_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── post_handler.go
│   │   │   │   ├── category_handler.go
│   │   │   │   ├── tag_handler.go
│   │   │   │   └── comment_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   ├── validators/
│   │   │   │   ├── post_validator.go
│   │   │   │   └── category_validator.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── notification/
│   │   │   ├── models/
│   │   │   │   ├── notification.go
│   │   │   │   ├── template.go
│   │   │   │   └── subscription.go
│   │   │   ├── repositories/
│   │   │   │   ├── notification_repository.go
│   │   │   │   ├── template_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── notification_service.go
│   │   │   │   ├── email_service.go
│   │   │   │   ├── sms_service.go
│   │   │   │   ├── push_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── notification_handler.go
│   │   │   │   └── template_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   ├── workers/
│   │   │   │   ├── email_worker.go
│   │   │   │   ├── sms_worker.go
│   │   │   │   └── push_worker.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── media/
│   │   │   ├── models/
│   │   │   │   ├── media.go
│   │   │   │   └── folder.go
│   │   │   ├── repositories/
│   │   │   │   ├── media_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── media_service.go
│   │   │   │   ├── upload_service.go
│   │   │   │   ├── image_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── media_handler.go
│   │   │   │   └── upload_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   ├── storage/
│   │   │   │   ├── local.go
│   │   │   │   ├── s3.go
│   │   │   │   ├── minio.go
│   │   │   │   └── interfaces.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── rbac/
│   │   │   ├── models/
│   │   │   │   ├── role.go
│   │   │   │   ├── permission.go
│   │   │   │   └── user_role.go
│   │   │   ├── repositories/
│   │   │   │   ├── role_repository.go
│   │   │   │   ├── permission_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── rbac_service.go
│   │   │   │   ├── permission_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── role_handler.go
│   │   │   │   └── permission_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── tenant/
│   │   │   ├── models/
│   │   │   │   ├── tenant.go
│   │   │   │   └── website.go
│   │   │   ├── repositories/
│   │   │   │   ├── tenant_repository.go
│   │   │   │   ├── website_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── tenant_service.go
│   │   │   │   ├── website_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── tenant_handler.go
│   │   │   │   └── website_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── website/
│   │   │   ├── models/
│   │   │   │   ├── theme.go
│   │   │   │   ├── page.go
│   │   │   │   ├── menu.go
│   │   │   │   └── widget.go
│   │   │   ├── repositories/
│   │   │   │   ├── theme_repository.go
│   │   │   │   ├── page_repository.go
│   │   │   │   ├── menu_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── theme_service.go
│   │   │   │   ├── page_service.go
│   │   │   │   ├── menu_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── theme_handler.go
│   │   │   │   ├── page_handler.go
│   │   │   │   └── menu_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── onboarding/
│   │   │   ├── models/
│   │   │   │   ├── journey.go
│   │   │   │   ├── step.go
│   │   │   │   └── progress.go
│   │   │   ├── repositories/
│   │   │   │   ├── journey_repository.go
│   │   │   │   ├── progress_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── journey_service.go
│   │   │   │   ├── progress_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── journey_handler.go
│   │   │   │   └── progress_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── seo/
│   │   │   ├── models/
│   │   │   │   ├── seo_meta.go
│   │   │   │   ├── redirect.go
│   │   │   │   └── sitemap.go
│   │   │   ├── repositories/
│   │   │   │   ├── seo_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── seo_service.go
│   │   │   │   ├── sitemap_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── seo_handler.go
│   │   │   │   └── sitemap_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   ├── payment/
│   │   │   ├── models/
│   │   │   │   ├── payment.go
│   │   │   │   ├── subscription.go
│   │   │   │   ├── invoice.go
│   │   │   │   └── customer.go
│   │   │   ├── repositories/
│   │   │   │   ├── payment_repository.go
│   │   │   │   ├── subscription_repository.go
│   │   │   │   └── interfaces.go
│   │   │   ├── services/
│   │   │   │   ├── payment_service.go
│   │   │   │   ├── subscription_service.go
│   │   │   │   ├── stripe_service.go
│   │   │   │   └── interfaces.go
│   │   │   ├── handlers/
│   │   │   │   ├── payment_handler.go
│   │   │   │   └── subscription_handler.go
│   │   │   ├── dto/
│   │   │   │   ├── request.go
│   │   │   │   └── response.go
│   │   │   └── routes/
│   │   │       └── routes.go
│   │   └── socket/
│   │       ├── models/
│   │       │   ├── connection.go
│   │       │   └── room.go
│   │       ├── services/
│   │       │   ├── socket_service.go
│   │       │   ├── room_service.go
│   │       │   └── interfaces.go
│   │       ├── handlers/
│   │       │   ├── socket_handler.go
│   │       │   └── message_handler.go
│   │       ├── events/
│   │       │   ├── user_events.go
│   │       │   ├── post_events.go
│   │       │   └── notification_events.go
│   │       └── routes/
│   │           └── routes.go
│   ├── database/
│   │   ├── migrations/
│   │   │   ├── 001_create_tenants_table.sql
│   │   │   ├── 002_create_websites_table.sql
│   │   │   ├── 003_create_users_table.sql
│   │   │   ├── 004_create_roles_table.sql
│   │   │   ├── 005_create_posts_table.sql
│   │   │   ├── 006_create_categories_table.sql
│   │   │   ├── 007_create_tags_table.sql
│   │   │   ├── 008_create_media_table.sql
│   │   │   ├── 009_create_notifications_table.sql
│   │   │   └── 010_create_payments_table.sql
│   │   ├── seeders/
│   │   │   ├── tenant_seeder.go
│   │   │   ├── user_seeder.go
│   │   │   ├── role_seeder.go
│   │   │   ├── category_seeder.go
│   │   │   └── post_seeder.go
│   │   └── connection.go
│   ├── queue/
│   │   ├── jobs/
│   │   │   ├── email_job.go
│   │   │   ├── notification_job.go
│   │   │   ├── media_processing_job.go
│   │   │   ├── seo_update_job.go
│   │   │   └── analytics_job.go
│   │   ├── workers/
│   │   │   ├── email_worker.go
│   │   │   ├── notification_worker.go
│   │   │   ├── media_worker.go
│   │   │   └── analytics_worker.go
│   │   ├── publishers/
│   │   │   └── event_publisher.go
│   │   └── queue.go
│   ├── cache/
│   │   ├── redis.go
│   │   ├── memory.go
│   │   └── interfaces.go
│   ├── storage/
│   │   ├── local/
│   │   │   └── local_storage.go
│   │   ├── s3/
│   │   │   └── s3_storage.go
│   │   ├── minio/
│   │   │   └── minio_storage.go
│   │   └── interfaces.go
│   ├── search/
│   │   ├── elasticsearch/
│   │   │   ├── client.go
│   │   │   ├── post_index.go
│   │   │   └── user_index.go
│   │   ├── algolia/
│   │   │   └── client.go
│   │   └── interfaces.go
│   ├── monitoring/
│   │   ├── metrics/
│   │   │   ├── prometheus.go
│   │   │   └── custom_metrics.go
│   │   ├── tracing/
│   │   │   └── jaeger.go
│   │   └── health/
│   │       └── health_check.go
│   ├── events/
│   │   ├── handlers/
│   │   │   ├── user_events.go
│   │   │   ├── post_events.go
│   │   │   ├── payment_events.go
│   │   │   └── notification_events.go
│   │   ├── dispatchers/
│   │   │   └── event_dispatcher.go
│   │   └── types/
│   │       ├── user_events.go
│   │       ├── post_events.go
│   │       └── payment_events.go
│   ├── security/
│   │   ├── encryption/
│   │   │   ├── aes.go
│   │   │   └── rsa.go
│   │   ├── hashing/
│   │   │   ├── bcrypt.go
│   │   │   └── argon2.go
│   │   └── jwt/
│   │       ├── jwt.go
│   │       └── claims.go
│   ├── utils/
│   │   ├── pagination/
│   │   │   ├── cursor.go
│   │   │   └── offset.go
│   │   ├── validation/
│   │   │   ├── validator.go
│   │   │   └── custom_rules.go
│   │   ├── response/
│   │   │   ├── json.go
│   │   │   └── error.go
│   │   ├── time/
│   │   │   └── timezone.go
│   │   ├── string/
│   │   │   ├── slug.go
│   │   │   └── uuid.go
│   │   ├── file/
│   │   │   ├── upload.go
│   │   │   └── mime.go
│   │   └── convert/
│   │       ├── json.go
│   │       └── string.go
│   └── server/
│       ├── http/
│       │   ├── server.go
│       │   └── routes.go
│       ├── grpc/
│       │   ├── server.go
│       │   └── services.go
│       └── websocket/
│           ├── server.go
│           └── hub.go
├── pkg/
│   ├── logger/
│   │   ├── zap.go
│   │   ├── logrus.go
│   │   └── interfaces.go
│   ├── database/
│   │   ├── gorm.go
│   │   ├── mysql.go
│   │   ├── postgres.go
│   │   └── interfaces.go
│   ├── redis/
│   │   ├── client.go
│   │   └── pool.go
│   ├── queue/
│   │   ├── asynq/
│   │   │   └── client.go
│   │   ├── rabbitmq/
│   │   │   └── client.go
│   │   └── interfaces.go
│   ├── email/
│   │   ├── smtp/
│   │   │   └── client.go
│   │   ├── sendgrid/
│   │   │   └── client.go
│   │   ├── mailgun/
│   │   │   └── client.go
│   │   └── interfaces.go
│   ├── sms/
│   │   ├── twilio/
│   │   │   └── client.go
│   │   └── interfaces.go
│   ├── payment/
│   │   ├── stripe/
│   │   │   └── client.go
│   │   ├── paypal/
│   │   │   └── client.go
│   │   └── interfaces.go
│   ├── cdn/
│   │   ├── cloudflare/
│   │   │   └── client.go
│   │   ├── cloudfront/
│   │   │   └── client.go
│   │   └── interfaces.go
│   └── social/
│       ├── facebook/
│       │   └── client.go
│       ├── twitter/
│       │   └── client.go
│       ├── google/
│       │   └── client.go
│       └── interfaces.go
├── api/
│   ├── v1/
│   │   ├── openapi.yaml
│   │   └── swagger.json
│   └── proto/
│       ├── user.proto
│       ├── blog.proto
│       └── notification.proto
├── web/
│   ├── templates/
│   │   ├── email/
│   │   │   ├── welcome.html
│   │   │   ├── password_reset.html
│   │   │   └── notification.html
│   │   └── admin/
│   │       ├── dashboard.html
│   │       └── login.html
│   ├── static/
│   │   ├── css/
│   │   │   └── admin.css
│   │   ├── js/
│   │   │   └── admin.js
│   │   └── images/
│   │       └── logo.png
│   └── assets/
│       └── favicon.ico
├── configs/
│   ├── app.yaml
│   ├── database.yaml
│   ├── redis.yaml
│   ├── queue.yaml
│   ├── email.yaml
│   ├── storage.yaml
│   └── monitoring.yaml
├── deployments/
│   ├── docker/
│   │   ├── Dockerfile
│   │   ├── docker-compose.yml
│   │   ├── docker-compose.dev.yml
│   │   └── docker-compose.prod.yml
│   ├── kubernetes/
│   │   ├── namespace.yaml
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   ├── ingress.yaml
│   │   ├── configmap.yaml
│   │   └── secret.yaml
│   ├── terraform/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   └── modules/
│   │       ├── rds/
│   │       ├── redis/
│   │       └── s3/
│   └── helm/
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
│           ├── deployment.yaml
│           ├── service.yaml
│           └── ingress.yaml
├── scripts/
│   ├── build.sh
│   ├── deploy.sh
│   ├── migrate.sh
│   ├── seed.sh
│   ├── backup.sh
│   └── monitoring/
│       ├── setup-prometheus.sh
│       └── setup-grafana.sh
├── tests/
│   ├── unit/
│   │   ├── auth/
│   │   │   ├── auth_service_test.go
│   │   │   └── jwt_service_test.go
│   │   ├── blog/
│   │   │   ├── post_service_test.go
│   │   │   └── category_service_test.go
│   │   └── utils/
│   │       └── pagination_test.go
│   ├── integration/
│   │   ├── auth/
│   │   │   └── auth_integration_test.go
│   │   ├── blog/
│   │   │   └── blog_integration_test.go
│   │   └── api/
│   │       └── api_integration_test.go
│   ├── e2e/
│   │   ├── auth_e2e_test.go
│   │   ├── blog_e2e_test.go
│   │   └── payment_e2e_test.go
│   ├── fixtures/
│   │   ├── users.json
│   │   ├── posts.json
│   │   └── categories.json
│   ├── mocks/
│   │   ├── user_repository_mock.go
│   │   ├── email_service_mock.go
│   │   └── payment_service_mock.go
│   └── helpers/
│       ├── test_helper.go
│       ├── database_helper.go
│       └── auth_helper.go
├── tools/
│   ├── gen/
│   │   ├── protobuf/
│   │   │   └── generate.go
│   │   └── swagger/
│   │       └── generate.go
│   ├── migrate/
│   │   └── migrate.go
│   └── lint/
│       └── golangci.yml
├── resources/
│   ├── templates/
│   │   ├── tenant/
│   │   │   ├── basic/
│   │   │   │   ├── roles.json
│   │   │   │   ├── permissions.json
│   │   │   │   └── settings.json
│   │   │   ├── pro/
│   │   │   │   ├── roles.json
│   │   │   │   ├── permissions.json
│   │   │   │   └── categories.json
│   │   │   └── enterprise/
│   │   │       ├── roles.json
│   │   │       ├── permissions.json
│   │   │       └── workflows.json
│   │   ├── website/
│   │   │   ├── blog/
│   │   │   │   ├── pages.json
│   │   │   │   ├── menus.json
│   │   │   │   └── widgets.json
│   │   │   └── ecommerce/
│   │   │       ├── pages.json
│   │   │       └── products.json
│   │   └── common/
│   │       ├── seo-defaults.json
│   │       └── notification-templates.json
│   ├── i18n/
│   │   ├── en/
│   │   │   ├── messages.json
│   │   │   └── errors.json
│   │   ├── vi/
│   │   │   ├── messages.json
│   │   │   └── errors.json
│   │   └── ja/
│   │       ├── messages.json
│   │       └── errors.json
│   └── docs/
│       ├── api/
│       │   ├── postman/
│       │   │   └── collection.json
│       │   └── insomnia/
│       │       └── workspace.json
│       └── diagrams/
│           ├── architecture.drawio
│           └── database.drawio
├── monitoring/
│   ├── prometheus/
│   │   ├── prometheus.yml
│   │   └── alerts.yml
│   ├── grafana/
│   │   ├── dashboards/
│   │   │   ├── api-dashboard.json
│   │   │   ├── database-dashboard.json
│   │   │   └── queue-dashboard.json
│   │   └── provisioning/
│   │       ├── datasources/
│   │       │   └── prometheus.yml
│   │       └── dashboards/
│   │           └── dashboard.yml
│   ├── jaeger/
│   │   └── jaeger.yml
│   └── logs/
│       ├── fluent-bit/
│       │   └── fluent-bit.conf
│       └── elasticsearch/
│           └── elasticsearch.yml
├── docs/
│   ├── README.md
│   ├── ARCHITECTURE.md
│   ├── _sidebar.md
│   ├── index.html
│   ├── architecture/
│   │   ├── overview.md
│   │   ├── tenant-priority-system.md
│   │   ├── logging-system.md
│   │   ├── inter-module-communication.md
│   │   ├── queue-system.md
│   │   └── cron-scheduler.md
│   ├── database/
│   │   ├── database-design.md
│   │   ├── tenant-initialization.md
│   │   ├── migrations.md
│   │   └── seeding.md
│   ├── modules/
│   │   ├── auth.md
│   │   ├── user.md
│   │   ├── blog.md
│   │   ├── blog-submission-flow.md
│   │   ├── notification.md
│   │   ├── socket.md
│   │   ├── media.md
│   │   ├── rbac.md
│   │   ├── tenant.md
│   │   ├── website.md
│   │   ├── onboarding.md
│   │   ├── seo.md
│   │   └── payment.md
│   ├── development/
│   │   ├── local-testing.md
│   │   ├── golang-libraries.md
│   │   └── testing.md
│   ├── api/
│   │   ├── response-standard.md
│   │   ├── cms-api.md
│   │   └── frontend-api.md
│   └── features/
│       └── i18n.md
├── .env.example
├── .env.local
├── .env.testing
├── .env.production
├── .gitignore
├── .dockerignore
├── go.mod
├── go.sum
├── Makefile
├── README.md
├── LICENSE
└── CHANGELOG.md
```

## Đề xuất tối ưu cấu trúc thư mục

### Tối ưu hóa Platform Services

Để tách biệt rõ ràng hơn giữa **business logic** và **platform/infrastructure concerns**, đề xuất gom các thư mục platform services:

```
internal/
├── modules/                     # Business logic modules
│   ├── auth/
│   ├── user/
│   ├── blog/
│   └── ...
├── platform/                   # Platform/Infrastructure services
│   ├── cache/
│   │   ├── redis.go
│   │   ├── memory.go
│   │   └── interfaces.go
│   ├── storage/
│   │   ├── local/
│   │   ├── s3/
│   │   ├── minio/
│   │   └── interfaces.go
│   ├── search/
│   │   ├── elasticsearch/
│   │   ├── algolia/
│   │   └── interfaces.go
│   ├── monitoring/
│   │   ├── metrics/
│   │   ├── logging/
│   │   ├── tracing/
│   │   └── health/
│   ├── queue/
│   │   ├── redis/
│   │   ├── rabbitmq/
│   │   └── interfaces.go
│   └── notification/
│       ├── email/
│       ├── sms/
│       ├── push/
│       └── interfaces.go
├── shared/                      # Shared utilities
│   ├── utils/
│   ├── constants/
│   ├── errors/
│   └── validators/
└── config/                      # Configuration
    ├── config.go
    ├── database.go
    └── ...
```

### Lợi ích của cấu trúc tối ưu

1. **Tách biệt rõ ràng**: Business logic vs Infrastructure
2. **Dễ maintain**: Platform services được gom lại một chỗ
3. **Scalable**: Dễ thêm platform services mới
4. **Testable**: Dễ mock platform dependencies
5. **Reusable**: Platform services có thể reuse across modules

### Migration Strategy

1. **Phase 1**: Tạo thư mục `internal/platform/`
2. **Phase 2**: Di chuyển từng platform service
3. **Phase 3**: Cập nhật imports trong modules
4. **Phase 4**: Cập nhật documentation và tests

### Best Practices

- **Interface-first**: Mỗi platform service phải có interface
- **Dependency Injection**: Inject platform services vào modules
- **Configuration**: Centralized config cho platform services
- **Monitoring**: Metrics và health checks cho tất cả services
- **Testing**: Unit tests và integration tests riêng biệt