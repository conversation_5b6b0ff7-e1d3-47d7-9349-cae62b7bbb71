# Module vs Plugin Boundaries - Architectural Guidelines

## Tổng quan

Tài liệu này định nghĩa rõ ràng ranh giới giữa **Modules** và **Plugins** trong Blog API v3, đảm bảo architectural consistency và maintainability.

## Định nghĩa

### 🏗️ Module (Core System Components)

**Module** là các thành phần cốt lõi của hệ thống, định nghĩa:

- **Business Logic**: Core business rules và workflows
- **Interfaces & Contracts**: Abstract interfaces cho external integrations
- **Data Models**: Database schemas và domain entities
- **Internal Services**: Core services và repositories
- **API Endpoints**: Public API definitions

**Đặc điểm:**
- ✅ Là phần bắt buộc của hệ thống
- ✅ Chứa business logic core
- ✅ Định nghĩa interfaces cho plugins
- ✅ Không phụ thuộc vào third-party services cụ thể
- ✅ Đượ<PERSON> maintain bởi core team

### 🔌 Plugin (External Integrations)

**Plugin** là các implementation cụ thể cho interfaces của modules:

- **Third-party Integrations**: Stripe, SendGrid, PayPal, etc.
- **Concrete Implementations**: Implement module interfaces
- **Configuration**: Provider-specific settings
- **External Dependencies**: Third-party SDKs và libraries

**Đặc điểm:**
- ✅ Có thể cắm-rút (pluggable)
- ✅ Implement interfaces từ modules
- ✅ Chứa provider-specific logic
- ✅ Có thể được thay thế bởi plugins khác
- ✅ Có thể được maintain bởi community

## Architectural Boundaries

### Core Architecture Pattern

```mermaid
flowchart TD
    subgraph "Application Layer"
        A[API Controllers] --> B[Module Services]
    end
    
    subgraph "Module Layer (Core)"
        B --> C[Business Logic]
        C --> D[Domain Interfaces]
        D --> E[Repository Interfaces]
    end
    
    subgraph "Plugin Layer (Implementations)"
        F[Email Plugins]
        G[Payment Plugins]
        H[Storage Plugins]
        I[Analytics Plugins]
    end
    
    subgraph "Infrastructure Layer"
        J[Database]
        K[Message Queue]
        L[Cache]
    end
    
    D -.-> F
    D -.-> G
    D -.-> H
    D -.-> I
    
    E --> J
    B --> K
    B --> L
```

### Interface-Implementation Pattern

```mermaid
classDiagram
    class EmailService {
        <<interface>>
        +SendEmail(to, subject, body)
        +SendTemplate(template, data)
        +GetDeliveryStatus(messageId)
    }
    
    class PaymentService {
        <<interface>>
        +ProcessPayment(amount, method)
        +CreateSubscription(plan, customer)
        +HandleWebhook(payload)
    }
    
    class SendGridPlugin {
        +SendEmail(to, subject, body)
        +SendTemplate(template, data)
        +GetDeliveryStatus(messageId)
    }
    
    class StripePlugin {
        +ProcessPayment(amount, method)
        +CreateSubscription(plan, customer)
        +HandleWebhook(payload)
    }
    
    EmailService <|.. SendGridPlugin
    EmailService <|.. MailgunPlugin
    PaymentService <|.. StripePlugin
    PaymentService <|.. PayPalPlugin
```

## Examples: Module vs Plugin

### ✅ Correct: Email Module + Email Plugins

**Email Module (Core):**
- Email template engine
- Queue management
- Delivery tracking interfaces
- Email analytics
- Subscription management
- GDPR compliance logic

**Email Plugins:**
- SendGrid Plugin (implements EmailService)
- Mailgun Plugin (implements EmailService)
- SES Plugin (implements EmailService)
- SMTP Plugin (implements EmailService)

### ✅ Correct: Payment Module + Payment Plugins

**Payment Module (Core):**
- Subscription management logic
- Invoice generation
- Payment workflow orchestration
- Transaction recording
- Refund management interfaces

**Payment Plugins:**
- Stripe Plugin (implements PaymentService)
- PayPal Plugin (implements PaymentService)
- VNPay Plugin (implements PaymentService)
- Bank Transfer Plugin (implements PaymentService)

### ❌ Incorrect: Mixing Concerns

**Bad Example:**
```
modules/
├── stripe-payment.md     # ❌ Too specific, should be plugin
├── sendgrid-email.md     # ❌ Too specific, should be plugin
└── paypal-integration.md # ❌ Too specific, should be plugin
```

**Good Example:**
```
modules/
├── payment.md           # ✅ Core payment interfaces & logic
└── email.md             # ✅ Core email interfaces & logic

plugins/
├── payment/
│   ├── stripe.md        # ✅ Stripe-specific implementation
│   ├── paypal.md        # ✅ PayPal-specific implementation
│   └── vnpay.md         # ✅ VNPay-specific implementation
└── email/
    ├── sendgrid.md      # ✅ SendGrid-specific implementation
    ├── mailgun.md       # ✅ Mailgun-specific implementation
    └── ses.md           # ✅ SES-specific implementation
```

## Plugin Lifecycle

### 1. Plugin Discovery
```go
type PluginRegistry interface {
    RegisterPlugin(name string, plugin Plugin) error
    GetPlugin(name string) (Plugin, error)
    ListPlugins() []PluginInfo
}
```

### 2. Plugin Initialization
```go
type Plugin interface {
    Initialize(config map[string]interface{}) error
    GetInfo() PluginInfo
    Shutdown() error
}
```

### 3. Plugin Configuration
```yaml
plugins:
  email:
    active: "sendgrid"
    providers:
      sendgrid:
        api_key: "${SENDGRID_API_KEY}"
        from_email: "<EMAIL>"
      mailgun:
        api_key: "${MAILGUN_API_KEY}"
        domain: "yourdomain.com"
  
  payment:
    active: "stripe"
    providers:
      stripe:
        secret_key: "${STRIPE_SECRET_KEY}"
        webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
      paypal:
        client_id: "${PAYPAL_CLIENT_ID}"
        client_secret: "${PAYPAL_CLIENT_SECRET}"
```

## Benefits of This Architecture

### 🎯 For Core System
- **Lightweight**: Core không bị bloat bởi third-party dependencies
- **Testable**: Dễ dàng mock interfaces cho testing
- **Stable**: Core logic không bị ảnh hưởng bởi third-party changes
- **Flexible**: Có thể swap implementations mà không affect core

### 🔌 For Plugins
- **Independent Updates**: Plugin updates không require core updates
- **Community Contributions**: Community có thể tạo plugins mới
- **Business Model**: Premium plugins có thể có separate pricing
- **Specialized Features**: Plugins có thể có provider-specific features

### 🏢 For Users
- **Choice**: Users chọn providers phù hợp với needs
- **Migration**: Dễ dàng switch giữa providers
- **Cost Optimization**: Chọn providers với pricing tốt nhất
- **Feature Access**: Access đến specialized features của providers

## Implementation Guidelines

### Module Development
1. **Define Clear Interfaces**: Abstract interfaces cho external dependencies
2. **Business Logic Focus**: Concentrate on core business rules
3. **Provider Agnostic**: Không hardcode specific providers
4. **Configuration Driven**: Use configuration để select plugins

### Plugin Development
1. **Implement Interfaces**: Strictly follow module interfaces
2. **Provider Specific**: Focus on provider-specific optimizations
3. **Error Handling**: Handle provider-specific errors gracefully
4. **Documentation**: Clear setup và configuration docs

## Migration Strategy

Để migrate từ current mixed architecture:

1. **Identify Core Logic**: Tách business logic khỏi provider implementations
2. **Extract Interfaces**: Define clear interfaces cho external dependencies
3. **Create Plugins**: Move provider-specific code thành plugins
4. **Update Documentation**: Reflect new boundaries trong docs
5. **Update Configuration**: Plugin-based configuration system

## Related Documentation

- **[Plugin System Overview](../plugins/overview.md)** - Plugin architecture details
- **[Creating Plugins](../plugins/creating-plugins.md)** - Plugin development guide
- **[Email Module](../modules/email.md)** - Core email interfaces
- **[Payment Module](../modules/payment.md)** - Core payment interfaces
