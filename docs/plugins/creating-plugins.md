# Creating Plugins - H<PERSON>ớng dẫn tạo Plugin

## Tổng quan

Tài liệu này hướng dẫn chi tiết cách tạo plugins cho Blog API v3, từ setup development environment đến publish plugin lên store.

## SendGrid, Mailgun, Stripe - Plugin hay Core Integration?

### Khuyến nghị: **Plugin cho tất cả**

#### Lý do chọn Plugin:

**1. Flexibility & Choice**
- Users có thể chọn email provider phù hợp
- Không force users sử dụng specific service
- Easy switching giữa providers

**2. Maintenance & Updates**
- Plugin updates independent của core system
- Third-party API changes không affect core
- Community có thể contribute improvements

**3. Business Model**
- Core system free, plugins có thể có pricing tiers
- Partner integrations với revenue sharing
- Premium features trong plugin

**4. Architecture Benefits**
- Core system lightweight
- Modular architecture
- Better testing isolation

### Implementation Strategy

```mermaid
flowchart TD
    subgraph "Core Email System"
        A[Email Interface] --> B[Plugin Manager]
        B --> C[Active Email Plugin]
    end
    
    subgraph "Email Plugins"
        D[SendGrid Plugin]
        E[Mailgun Plugin]
        F[SMTP Plugin]
        G[SES Plugin]
    end
    
    subgraph "Core Payment System"
        H[Payment Interface] --> I[Plugin Manager]
        I --> J[Active Payment Plugin]
    end
    
    subgraph "Payment Plugins"
        K[Stripe Plugin]
        L[PayPal Plugin]
        M[Square Plugin]
        N[Local Bank Plugin]
    end
    
    C --> D
    C --> E
    C --> F
    C --> G
    
    J --> K
    J --> L
    J --> M
    J --> N
```

## Core Integration Strategy

### 1. Email Service Interface

```go
package email

// Core email interface
type EmailService interface {
    SendEmail(ctx context.Context, email *Email) error
    SendBulkEmail(ctx context.Context, emails []*Email) error
    GetDeliveryStatus(ctx context.Context, messageID string) (*DeliveryStatus, error)
    GetBounces(ctx context.Context, since time.Time) ([]*Bounce, error)
    ValidateEmail(ctx context.Context, email string) (*ValidationResult, error)
}

// Email structure
type Email struct {
    From        string            `json:"from"`
    To          []string          `json:"to"`
    CC          []string          `json:"cc,omitempty"`
    BCC         []string          `json:"bcc,omitempty"`
    Subject     string            `json:"subject"`
    HTML        string            `json:"html,omitempty"`
    Text        string            `json:"text,omitempty"`
    Attachments []Attachment      `json:"attachments,omitempty"`
    Headers     map[string]string `json:"headers,omitempty"`
    Tags        []string          `json:"tags,omitempty"`
    Metadata    map[string]string `json:"metadata,omitempty"`
}

// Core email manager
type EmailManager struct {
    plugins map[string]EmailPlugin
    active  string
    config  EmailConfig
}

type EmailPlugin interface {
    EmailService
    GetInfo() PluginInfo
    Configure(config map[string]interface{}) error
    HealthCheck() error
}

func (em *EmailManager) SendEmail(ctx context.Context, email *Email) error {
    plugin, exists := em.plugins[em.active]
    if !exists {
        return errors.New("no active email plugin")
    }
    
    return plugin.SendEmail(ctx, email)
}
```

### 2. Payment Service Interface

```go
package payment

// Core payment interface
type PaymentService interface {
    CreatePayment(ctx context.Context, payment *Payment) (*PaymentResult, error)
    CapturePayment(ctx context.Context, paymentID string) (*PaymentResult, error)
    RefundPayment(ctx context.Context, paymentID string, amount int64) (*RefundResult, error)
    GetPayment(ctx context.Context, paymentID string) (*Payment, error)
    CreateSubscription(ctx context.Context, subscription *Subscription) (*SubscriptionResult, error)
    CancelSubscription(ctx context.Context, subscriptionID string) error
    HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookEvent, error)
}

// Payment structure
type Payment struct {
    Amount      int64             `json:"amount"`      // in cents
    Currency    string            `json:"currency"`
    Description string            `json:"description"`
    Customer    *Customer         `json:"customer"`
    Metadata    map[string]string `json:"metadata"`
    ReturnURL   string            `json:"return_url"`
    CancelURL   string            `json:"cancel_url"`
}

// Core payment manager
type PaymentManager struct {
    plugins map[string]PaymentPlugin
    active  string
    config  PaymentConfig
}

type PaymentPlugin interface {
    PaymentService
    GetInfo() PluginInfo
    Configure(config map[string]interface{}) error
    HealthCheck() error
}
```

## Plugin Examples

### 1. SendGrid Email Plugin

```yaml
# plugins/email/sendgrid/plugin.yaml
name: "sendgrid-email"
version: "1.0.0"
description: "SendGrid email service integration"
type: "email"
category: "communication"

dependencies:
  required:
    - "core/email-interface": ">=1.0.0"

permissions:
  external:
    - "https://api.sendgrid.com/*"

config_schema:
  type: "object"
  properties:
    api_key:
      type: "string"
      required: true
      description: "SendGrid API Key"
    from_email:
      type: "string"
      required: true
      description: "Default from email"
    from_name:
      type: "string"
      required: true
      description: "Default from name"
    webhook_url:
      type: "string"
      description: "Webhook URL for delivery events"
  required: ["api_key", "from_email", "from_name"]
```

```go
package main

import (
    "context"
    "fmt"
    
    "github.com/sendgrid/sendgrid-go"
    "github.com/sendgrid/sendgrid-go/helpers/mail"
    "github.com/blog-api/plugin-sdk/v1"
)

type SendGridPlugin struct {
    sdk.BasePlugin
    client *sendgrid.Client
    config *SendGridConfig
}

type SendGridConfig struct {
    APIKey     string `json:"api_key"`
    FromEmail  string `json:"from_email"`
    FromName   string `json:"from_name"`
    WebhookURL string `json:"webhook_url"`
}

func (p *SendGridPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
    if err := p.ParseConfig(config, &p.config); err != nil {
        return err
    }
    
    p.client = sendgrid.NewSendClient(p.config.APIKey)
    
    // Register webhook handler
    p.RegisterAPIHandler("POST", "/webhook/sendgrid", p.handleWebhook)
    
    return nil
}

func (p *SendGridPlugin) SendEmail(ctx context.Context, email *sdk.Email) error {
    from := mail.NewEmail(p.config.FromName, p.config.FromEmail)
    to := mail.NewEmail("", email.To[0])
    
    message := mail.NewSingleEmail(from, email.Subject, to, email.Text, email.HTML)
    
    // Add CC/BCC
    for _, cc := range email.CC {
        message.Personalizations[0].AddCCs(mail.NewEmail("", cc))
    }
    
    for _, bcc := range email.BCC {
        message.Personalizations[0].AddBCCs(mail.NewEmail("", bcc))
    }
    
    // Add attachments
    for _, attachment := range email.Attachments {
        attach := mail.NewAttachment()
        attach.SetContent(attachment.Content)
        attach.SetType(attachment.Type)
        attach.SetFilename(attachment.Filename)
        message.AddAttachment(attach)
    }
    
    // Add custom headers
    for key, value := range email.Headers {
        message.SetHeader(key, value)
    }
    
    // Add tracking
    message.SetTrackingSettings(&mail.TrackingSettings{
        ClickTracking:        &mail.ClickTrackingSetting{Enable: &[]bool{true}[0]},
        OpenTracking:         &mail.OpenTrackingSetting{Enable: &[]bool{true}[0]},
        SubscriptionTracking: &mail.SubscriptionTrackingSetting{Enable: &[]bool{false}[0]},
    })
    
    response, err := p.client.Send(message)
    if err != nil {
        return fmt.Errorf("sendgrid error: %w", err)
    }
    
    if response.StatusCode >= 400 {
        return fmt.Errorf("sendgrid API error: %d - %s", response.StatusCode, response.Body)
    }
    
    return nil
}

func (p *SendGridPlugin) handleWebhook(ctx context.Context, req sdk.APIRequest) sdk.APIResponse {
    // Process SendGrid webhook events
    events := []map[string]interface{}{}
    
    for _, event := range events {
        // Emit internal event
        p.EmitEvent("email.delivered", event)
    }
    
    return sdk.SuccessResponse(map[string]string{"status": "ok"})
}

var Plugin = &SendGridPlugin{}
```

### 2. Stripe Payment Plugin

```yaml
# plugins/payment/stripe/plugin.yaml
name: "stripe-payment"
version: "1.0.0"
description: "Stripe payment processing integration"
type: "payment"
category: "commerce"

dependencies:
  required:
    - "core/payment-interface": ">=1.0.0"

permissions:
  external:
    - "https://api.stripe.com/*"

config_schema:
  type: "object"
  properties:
    publishable_key:
      type: "string"
      required: true
      description: "Stripe Publishable Key"
    secret_key:
      type: "string"
      required: true
      description: "Stripe Secret Key"
    webhook_secret:
      type: "string"
      required: true
      description: "Stripe Webhook Secret"
    currency:
      type: "string"
      default: "usd"
      description: "Default currency"
  required: ["publishable_key", "secret_key", "webhook_secret"]
```

```go
package main

import (
    "context"
    "fmt"
    
    "github.com/stripe/stripe-go/v74"
    "github.com/stripe/stripe-go/v74/client"
    "github.com/blog-api/plugin-sdk/v1"
)

type StripePlugin struct {
    sdk.BasePlugin
    client *client.API
    config *StripeConfig
}

type StripeConfig struct {
    PublishableKey string `json:"publishable_key"`
    SecretKey      string `json:"secret_key"`
    WebhookSecret  string `json:"webhook_secret"`
    Currency       string `json:"currency"`
}

func (p *StripePlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
    if err := p.ParseConfig(config, &p.config); err != nil {
        return err
    }
    
    stripe.Key = p.config.SecretKey
    p.client = &client.API{}
    p.client.Init(stripe.Key, nil)
    
    // Register webhook handler
    p.RegisterAPIHandler("POST", "/webhook/stripe", p.handleWebhook)
    
    // Register API endpoints
    p.RegisterAPIHandler("POST", "/stripe/payment-intent", p.createPaymentIntent)
    p.RegisterAPIHandler("GET", "/stripe/payment/{id}", p.getPayment)
    
    return nil
}

func (p *StripePlugin) CreatePayment(ctx context.Context, payment *sdk.Payment) (*sdk.PaymentResult, error) {
    params := &stripe.PaymentIntentParams{
        Amount:   stripe.Int64(payment.Amount),
        Currency: stripe.String(payment.Currency),
    }
    
    if payment.Description != "" {
        params.Description = stripe.String(payment.Description)
    }
    
    // Add metadata
    if payment.Metadata != nil {
        params.Metadata = payment.Metadata
    }
    
    // Create customer if provided
    if payment.Customer != nil {
        customerParams := &stripe.CustomerParams{
            Email: stripe.String(payment.Customer.Email),
            Name:  stripe.String(payment.Customer.Name),
        }
        
        customer, err := p.client.Customers.New(customerParams)
        if err != nil {
            return nil, fmt.Errorf("failed to create customer: %w", err)
        }
        
        params.Customer = stripe.String(customer.ID)
    }
    
    intent, err := p.client.PaymentIntents.New(params)
    if err != nil {
        return nil, fmt.Errorf("stripe error: %w", err)
    }
    
    return &sdk.PaymentResult{
        ID:           intent.ID,
        Status:       string(intent.Status),
        Amount:       intent.Amount,
        Currency:     string(intent.Currency),
        ClientSecret: intent.ClientSecret,
        Created:      time.Unix(intent.Created, 0),
    }, nil
}

func (p *StripePlugin) handleWebhook(ctx context.Context, req sdk.APIRequest) sdk.APIResponse {
    payload := req.Body["payload"].([]byte)
    signature := req.Headers["stripe-signature"]
    
    event, err := webhook.ConstructEvent(payload, signature, p.config.WebhookSecret)
    if err != nil {
        return sdk.ErrorResponse(400, "invalid signature")
    }
    
    // Process webhook event
    switch event.Type {
    case "payment_intent.succeeded":
        p.EmitEvent("payment.succeeded", event.Data.Object)
    case "payment_intent.payment_failed":
        p.EmitEvent("payment.failed", event.Data.Object)
    case "customer.subscription.created":
        p.EmitEvent("subscription.created", event.Data.Object)
    }
    
    return sdk.SuccessResponse(map[string]string{"status": "ok"})
}

var Plugin = &StripePlugin{}
```

### 3. Mailgun Email Plugin

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/mailgun/mailgun-go/v4"
    "github.com/blog-api/plugin-sdk/v1"
)

type MailgunPlugin struct {
    sdk.BasePlugin
    client mailgun.Mailgun
    config *MailgunConfig
}

type MailgunConfig struct {
    APIKey    string `json:"api_key"`
    Domain    string `json:"domain"`
    Region    string `json:"region"` // us, eu
    FromEmail string `json:"from_email"`
    FromName  string `json:"from_name"`
}

func (p *MailgunPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
    if err := p.ParseConfig(config, &p.config); err != nil {
        return err
    }
    
    p.client = mailgun.NewMailgun(p.config.Domain, p.config.APIKey)
    
    // Set region
    if p.config.Region == "eu" {
        p.client.SetAPIBase(mailgun.APIBaseEU)
    }
    
    // Register webhook handler
    p.RegisterAPIHandler("POST", "/webhook/mailgun", p.handleWebhook)
    
    return nil
}

func (p *MailgunPlugin) SendEmail(ctx context.Context, email *sdk.Email) error {
    message := p.client.NewMessage(
        fmt.Sprintf("%s <%s>", p.config.FromName, p.config.FromEmail),
        email.Subject,
        email.Text,
        email.To...,
    )
    
    if email.HTML != "" {
        message.SetHtml(email.HTML)
    }
    
    // Add CC/BCC
    for _, cc := range email.CC {
        message.AddCC(cc)
    }
    
    for _, bcc := range email.BCC {
        message.AddBCC(bcc)
    }
    
    // Add attachments
    for _, attachment := range email.Attachments {
        message.AddBufferAttachment(attachment.Filename, attachment.Content)
    }
    
    // Add custom headers
    for key, value := range email.Headers {
        message.AddHeader(key, value)
    }
    
    // Add tags and metadata
    for _, tag := range email.Tags {
        message.AddTag(tag)
    }
    
    for key, value := range email.Metadata {
        message.AddVariable(key, value)
    }
    
    // Enable tracking
    message.SetTracking(true)
    message.SetTrackingClicks(true)
    message.SetTrackingOpens(true)
    
    ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
    defer cancel()
    
    _, id, err := p.client.Send(ctx, message)
    if err != nil {
        return fmt.Errorf("mailgun error: %w", err)
    }
    
    p.LogInfo("Email sent successfully: %s", id)
    return nil
}

var Plugin = &MailgunPlugin{}
```

## Plugin Configuration Management

### 1. Configuration UI

```go
// Plugin configuration endpoints
// GET /api/v1/plugins/{name}/config/schema
func (h *PluginHandler) GetConfigSchema(c *gin.Context) {
    pluginName := c.Param("name")
    
    plugin, exists := h.pluginManager.GetPlugin(pluginName)
    if !exists {
        c.JSON(404, gin.H{"error": "plugin not found"})
        return
    }
    
    schema := plugin.GetConfigSchema()
    c.JSON(200, gin.H{"schema": schema})
}

// GET /api/v1/plugins/{name}/config
func (h *PluginHandler) GetConfig(c *gin.Context) {
    pluginName := c.Param("name")
    tenantID := c.GetUint32("tenant_id")
    
    config, err := h.configService.GetPluginConfig(c.Request.Context(), tenantID, pluginName)
    if err != nil {
        c.JSON(500, gin.H{"error": "failed to get config"})
        return
    }
    
    c.JSON(200, gin.H{"config": config})
}

// PUT /api/v1/plugins/{name}/config
func (h *PluginHandler) UpdateConfig(c *gin.Context) {
    pluginName := c.Param("name")
    tenantID := c.GetUint32("tenant_id")
    
    var config map[string]interface{}
    if err := c.ShouldBindJSON(&config); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }
    
    // Validate config against schema
    if err := h.validateConfig(pluginName, config); err != nil {
        c.JSON(422, gin.H{"error": err.Error()})
        return
    }
    
    // Save config
    if err := h.configService.UpdatePluginConfig(c.Request.Context(), tenantID, pluginName, config); err != nil {
        c.JSON(500, gin.H{"error": "failed to update config"})
        return
    }
    
    // Reconfigure plugin
    if err := h.pluginManager.ReconfigurePlugin(c.Request.Context(), pluginName, config); err != nil {
        c.JSON(500, gin.H{"error": "failed to reconfigure plugin"})
        return
    }
    
    c.JSON(200, gin.H{"message": "configuration updated"})
}
```

### 2. Plugin Marketplace Integration

```yaml
# Plugin marketplace configuration
marketplace:
  url: "https://marketplace.blog-api.com"
  api_key: "${MARKETPLACE_API_KEY}"
  auto_update: true
  categories:
    - "email"
    - "payment"
    - "analytics"
    - "social"
    - "seo"
    - "security"

featured_plugins:
  email:
    - "sendgrid-email"
    - "mailgun-email"
    - "ses-email"
  payment:
    - "stripe-payment"
    - "paypal-payment"
    - "square-payment"
  analytics:
    - "google-analytics"
    - "mixpanel-analytics"
    - "amplitude-analytics"
```

## Testing Strategy

### 1. Plugin Testing Framework

```go
package testing

import (
    "context"
    "testing"
    
    "github.com/blog-api/plugin-sdk/v1"
)

type PluginTestSuite struct {
    plugin sdk.Plugin
    env    *TestEnvironment
}

func NewPluginTestSuite(plugin sdk.Plugin) *PluginTestSuite {
    return &PluginTestSuite{
        plugin: plugin,
        env:    NewTestEnvironment(),
    }
}

func (pts *PluginTestSuite) TestEmailPlugin(t *testing.T) {
    emailPlugin := pts.plugin.(EmailPlugin)
    
    // Test configuration
    t.Run("Configuration", func(t *testing.T) {
        config := map[string]interface{}{
            "api_key":    "test-key",
            "from_email": "<EMAIL>",
            "from_name":  "Test Sender",
        }
        
        err := emailPlugin.Configure(config)
        assert.NoError(t, err)
    })
    
    // Test email sending
    t.Run("Send Email", func(t *testing.T) {
        email := &sdk.Email{
            To:      []string{"<EMAIL>"},
            Subject: "Test Email",
            Text:    "This is a test email",
            HTML:    "<p>This is a test email</p>",
        }
        
        err := emailPlugin.SendEmail(context.Background(), email)
        assert.NoError(t, err)
    })
    
    // Test webhook handling
    t.Run("Webhook", func(t *testing.T) {
        webhookPayload := map[string]interface{}{
            "event": "delivered",
            "message_id": "test-123",
        }
        
        req := pts.env.NewWebhookRequest("/webhook/email", webhookPayload)
        resp := emailPlugin.HandleWebhook(context.Background(), req)
        
        assert.Equal(t, 200, resp.StatusCode)
    })
}
```

## Deployment & Distribution

### 1. Plugin Packaging

```bash
# Build plugin
make build

# Package plugin
blog-api plugin package --name sendgrid-email --version 1.0.0

# Validate plugin
blog-api plugin validate sendgrid-email-1.0.0.zip

# Publish to marketplace
blog-api plugin publish sendgrid-email-1.0.0.zip --marketplace official
```

### 2. Plugin Installation Flow

```mermaid
sequenceDiagram
    participant Admin as Tenant Admin
    participant UI as Admin UI
    participant API as Plugin API
    participant Store as Plugin Store
    participant PM as Plugin Manager
    
    Admin->>UI: Browse plugins
    UI->>Store: GET /plugins?category=email
    Store->>UI: Return plugin list
    
    Admin->>UI: Install SendGrid plugin
    UI->>API: POST /plugins/install
    API->>Store: Download plugin package
    Store->>API: Return plugin ZIP
    
    API->>PM: Install plugin
    PM->>PM: Validate plugin
    PM->>PM: Extract & load
    PM->>API: Installation complete
    
    API->>UI: Success response
    UI->>Admin: Plugin installed
    
    Admin->>UI: Configure plugin
    UI->>API: PUT /plugins/sendgrid/config
    API->>PM: Update configuration
    PM->>PM: Reconfigure plugin
    
    API->>UI: Configuration saved
    UI->>Admin: Plugin ready to use
```

## Best Practices

### 1. Plugin Architecture
- **Interface Compliance**: Implement all required interfaces
- **Error Handling**: Graceful error handling and recovery
- **Resource Management**: Proper cleanup and resource limits
- **Security**: Input validation and output sanitization

### 2. Configuration Management
- **Schema Validation**: Use JSON schema for configuration
- **Environment Variables**: Support env var substitution
- **Secrets Management**: Secure handling of API keys
- **Hot Reload**: Support configuration updates without restart

### 3. Testing & Quality
- **Unit Tests**: Comprehensive test coverage
- **Integration Tests**: Test with real services in dev mode
- **Performance Tests**: Monitor resource usage
- **Security Tests**: Vulnerability scanning

### 4. Documentation
- **User Guide**: Clear setup and configuration instructions
- **API Reference**: Complete API documentation
- **Examples**: Working code examples
- **Troubleshooting**: Common issues and solutions