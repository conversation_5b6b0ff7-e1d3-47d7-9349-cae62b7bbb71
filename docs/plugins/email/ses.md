# Amazon SES Email Plugin

## Tổng quan

Amazon SES (Simple Email Service) Plugin cung cấp implementation của Email Service interface sử dụng AWS SES API. Plugin này tận dụng tính cost-effective và reliability của AWS infrastructure cho email delivery.

## Features

### ✅ Supported Features
- **Transactional Emails**: Single và bulk email sending
- **Template Engine**: SES templates với substitution data
- **Delivery Tracking**: Delivery, bounce, và complaint tracking
- **Configuration Sets**: Advanced sending configuration
- **Reputation Tracking**: Sending reputation monitoring
- **Suppression Lists**: Automatic bounce và complaint handling
- **SMTP Interface**: Alternative SMTP sending method
- **Regional Support**: Multi-region deployment

### ❌ Limitations
- **Template Limitations**: Basic template features compared to specialized providers
- **Analytics**: Limited built-in analytics (requires CloudWatch)
- **Sandbox Mode**: New accounts start in sandbox mode
- **Sending Limits**: Daily sending quotas và rate limits

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/email/ses"
    "github.com/your-org/blog-api-v3/internal/modules/email"
)

func init() {
    // Register SES plugin
    email.RegisterPlugin("ses", ses.NewPlugin())
}
```

### 2. Configuration

```yaml
email:
  active_provider: "ses"
  providers:
    ses:
      plugin: "ses"
      config:
        # AWS credentials
        access_key: "${AWS_ACCESS_KEY_ID}"
        secret_key: "${AWS_SECRET_ACCESS_KEY}"
        region: "us-east-1"
        
        # Optional: Use IAM role instead of access keys
        use_iam_role: false
        
        # Email settings
        from_email: "<EMAIL>"
        from_name: "Your App Name"
        
        # Configuration set (optional)
        configuration_set: "your-config-set"
        
        # Tracking settings
        track_opens: true
        track_clicks: true
        
        # Rate limiting (respect SES limits)
        rate_limit:
          requests_per_second: 14  # Default SES limit
          burst_size: 1
```

### 3. Environment Variables

```bash
# AWS credentials (if not using IAM role)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Optional
AWS_SESSION_TOKEN=your_session_token  # For temporary credentials
```

### 4. AWS SES Setup

```bash
# Verify your domain
aws ses verify-domain-identity --domain yourdomain.com

# Verify email addresses (for sandbox mode)
aws ses verify-email-identity --email-address <EMAIL>

# Request production access (to exit sandbox)
# This requires manual request through AWS console
```

## Implementation Details

### SES Plugin Structure

```go
type SESPlugin struct {
    client    *ses.SES
    config    *SESConfig
    templates map[string]*ses.Template
    logger    logger.Logger
}

type SESConfig struct {
    AccessKey        string `yaml:"access_key"`
    SecretKey        string `yaml:"secret_key"`
    Region           string `yaml:"region"`
    UseIAMRole       bool   `yaml:"use_iam_role"`
    FromEmail        string `yaml:"from_email"`
    FromName         string `yaml:"from_name"`
    ConfigurationSet string `yaml:"configuration_set"`
    TrackOpens       bool   `yaml:"track_opens"`
    TrackClicks      bool   `yaml:"track_clicks"`
    RateLimit        RateLimitConfig `yaml:"rate_limit"`
}
```

### Email Sending Implementation

```go
func (p *SESPlugin) SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error) {
    // Create SES send email input
    input := &ses.SendEmailInput{
        Source: aws.String(fmt.Sprintf("%s <%s>", p.config.FromName, p.config.FromEmail)),
        Destination: &ses.Destination{
            ToAddresses: aws.StringSlice(email.To),
        },
        Message: &ses.Message{
            Subject: &ses.Content{
                Data:    aws.String(email.Subject),
                Charset: aws.String("UTF-8"),
            },
            Body: &ses.Body{},
        },
    }
    
    // Set HTML content
    if email.HTMLContent != "" {
        input.Message.Body.Html = &ses.Content{
            Data:    aws.String(email.HTMLContent),
            Charset: aws.String("UTF-8"),
        }
    }
    
    // Set text content
    if email.TextContent != "" {
        input.Message.Body.Text = &ses.Content{
            Data:    aws.String(email.TextContent),
            Charset: aws.String("UTF-8"),
        }
    }
    
    // Add CC and BCC
    if len(email.CC) > 0 {
        input.Destination.CcAddresses = aws.StringSlice(email.CC)
    }
    if len(email.BCC) > 0 {
        input.Destination.BccAddresses = aws.StringSlice(email.BCC)
    }
    
    // Set configuration set
    if p.config.ConfigurationSet != "" {
        input.ConfigurationSetName = aws.String(p.config.ConfigurationSet)
    }
    
    // Add custom headers
    if len(email.Headers) > 0 {
        input.Tags = make([]*ses.MessageTag, 0, len(email.Headers))
        for key, value := range email.Headers {
            input.Tags = append(input.Tags, &ses.MessageTag{
                Name:  aws.String(key),
                Value: aws.String(value),
            })
        }
    }
    
    // Send email
    result, err := p.client.SendEmailWithContext(ctx, input)
    if err != nil {
        return nil, fmt.Errorf("ses send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  aws.StringValue(result.MessageId),
        Status:     EmailStatusSent,
        ProviderID: "ses",
        SentAt:     timePtr(time.Now()),
    }, nil
}
```

### Template Implementation

```go
func (p *SESPlugin) SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error) {
    // Prepare template data
    templateData, err := json.Marshal(template.Variables)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal template data: %w", err)
    }
    
    // Create SES templated email input
    input := &ses.SendTemplatedEmailInput{
        Source:      aws.String(fmt.Sprintf("%s <%s>", p.config.FromName, p.config.FromEmail)),
        Template:    aws.String(template.TemplateID),
        TemplateData: aws.String(string(templateData)),
        Destination: &ses.Destination{
            ToAddresses: aws.StringSlice(template.To),
        },
    }
    
    // Set configuration set
    if p.config.ConfigurationSet != "" {
        input.ConfigurationSetName = aws.String(p.config.ConfigurationSet)
    }
    
    // Add metadata as tags
    if template.Metadata != nil {
        input.Tags = make([]*ses.MessageTag, 0, len(template.Metadata))
        for key, value := range template.Metadata {
            input.Tags = append(input.Tags, &ses.MessageTag{
                Name:  aws.String(key),
                Value: aws.String(fmt.Sprintf("%v", value)),
            })
        }
    }
    
    // Send templated email
    result, err := p.client.SendTemplatedEmailWithContext(ctx, input)
    if err != nil {
        return nil, fmt.Errorf("ses template send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  aws.StringValue(result.MessageId),
        Status:     EmailStatusSent,
        ProviderID: "ses",
        Metadata: map[string]interface{}{
            "template": template.TemplateID,
        },
        SentAt: timePtr(time.Now()),
    }, nil
}
```

### Bulk Email Implementation

```go
func (p *SESPlugin) SendBulk(ctx context.Context, emails []*EmailMessage) ([]*EmailResult, error) {
    // SES supports bulk sending via SendBulkTemplatedEmail
    // For non-templated emails, we send individually
    
    results := make([]*EmailResult, 0, len(emails))
    
    // Process emails in batches to respect rate limits
    batchSize := 50 // SES limit for bulk operations
    for i := 0; i < len(emails); i += batchSize {
        end := i + batchSize
        if end > len(emails) {
            end = len(emails)
        }
        
        batch := emails[i:end]
        batchResults, err := p.sendBatch(ctx, batch)
        if err != nil {
            return nil, fmt.Errorf("batch send failed: %w", err)
        }
        
        results = append(results, batchResults...)
    }
    
    return results, nil
}

func (p *SESPlugin) sendBatch(ctx context.Context, emails []*EmailMessage) ([]*EmailResult, error) {
    results := make([]*EmailResult, 0, len(emails))
    
    for _, email := range emails {
        result, err := p.SendEmail(ctx, email)
        if err != nil {
            // Log error but continue with other emails
            p.logger.Error("failed to send email in batch", "error", err, "to", email.To)
            results = append(results, &EmailResult{
                Status: EmailStatusFailed,
                Error:  err.Error(),
            })
            continue
        }
        results = append(results, result)
        
        // Add small delay to respect rate limits
        time.Sleep(time.Millisecond * 100)
    }
    
    return results, nil
}
```

## SNS Integration for Webhooks

### SNS Configuration

```go
func (p *SESPlugin) setupSNSWebhooks() error {
    // SES doesn't have direct webhooks like other providers
    // Instead, it uses SNS notifications for delivery events
    
    snsClient := sns.New(p.client.Config)
    
    // Create SNS topic for SES events
    topicResult, err := snsClient.CreateTopic(&sns.CreateTopicInput{
        Name: aws.String("ses-delivery-events"),
    })
    if err != nil {
        return fmt.Errorf("failed to create SNS topic: %w", err)
    }
    
    // Configure SES to publish to SNS
    _, err = p.client.PutConfigurationSetEventDestination(&ses.PutConfigurationSetEventDestinationInput{
        ConfigurationSetName: aws.String(p.config.ConfigurationSet),
        EventDestination: &ses.EventDestination{
            Name:    aws.String("sns-destination"),
            Enabled: aws.Bool(true),
            MatchingEventTypes: aws.StringSlice([]string{
                "send", "reject", "bounce", "complaint", "delivery",
            }),
            SNSDestination: &ses.SNSDestination{
                TopicARN: topicResult.TopicArn,
            },
        },
    })
    
    return err
}
```

### SNS Webhook Handling

```go
func (p *SESPlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Parse SNS message
    var snsMessage SNSMessage
    if err := json.Unmarshal(payload, &snsMessage); err != nil {
        return nil, fmt.Errorf("failed to parse SNS message: %w", err)
    }
    
    // Parse SES event from SNS message
    var sesEvent SESEvent
    if err := json.Unmarshal([]byte(snsMessage.Message), &sesEvent); err != nil {
        return nil, fmt.Errorf("failed to parse SES event: %w", err)
    }
    
    // Process SES event
    result := p.processSESEvent(ctx, &sesEvent)
    
    return &WebhookResult{
        Provider: "ses",
        Events:   []*WebhookEventResult{result},
    }, nil
}
```

## Configuration Examples

### Basic Configuration

```yaml
email:
  providers:
    ses:
      plugin: "ses"
      config:
        region: "us-east-1"
        from_email: "<EMAIL>"
        from_name: "Your App"
        use_iam_role: true  # Recommended for EC2/ECS
```

### Advanced Configuration

```yaml
email:
  providers:
    ses:
      plugin: "ses"
      config:
        access_key: "${AWS_ACCESS_KEY_ID}"
        secret_key: "${AWS_SECRET_ACCESS_KEY}"
        region: "us-east-1"
        from_email: "<EMAIL>"
        from_name: "Your App"
        
        # Configuration set for tracking
        configuration_set: "production-emails"
        
        # Tracking settings
        track_opens: true
        track_clicks: true
        
        # Rate limiting (respect SES limits)
        rate_limit:
          requests_per_second: 14
          burst_size: 1
```

## Error Handling

### Common Errors

```go
var (
    ErrSESNotVerified     = errors.New("email address not verified in SES")
    ErrSESSandboxMode     = errors.New("SES account in sandbox mode")
    ErrSESRateLimitExceeded = errors.New("SES sending rate exceeded")
    ErrSESQuotaExceeded   = errors.New("SES daily quota exceeded")
)

func (p *SESPlugin) handleSESError(err error) error {
    if awsErr, ok := err.(awserr.Error); ok {
        switch awsErr.Code() {
        case ses.ErrCodeMessageRejected:
            return ErrSESNotVerified
        case ses.ErrCodeSendingPausedException:
            return ErrSESSandboxMode
        case ses.ErrCodeMailFromDomainNotVerifiedException:
            return ErrSESNotVerified
        case ses.ErrCodeConfigurationSetDoesNotExistException:
            return fmt.Errorf("SES configuration set not found: %s", awsErr.Message())
        default:
            return fmt.Errorf("SES error: %s", awsErr.Message())
        }
    }
    return err
}
```

## Testing

### Unit Tests

```go
func TestSESPlugin_SendEmail(t *testing.T) {
    plugin := &SESPlugin{
        client: mockSESClient(),
        config: &SESConfig{
            Region:    "us-east-1",
            FromEmail: "<EMAIL>",
            FromName:  "Test App",
        },
    }
    
    email := &EmailMessage{
        To:          []string{"<EMAIL>"},
        Subject:     "Test Email",
        HTMLContent: "<h1>Hello World</h1>",
        TextContent: "Hello World",
    }
    
    result, err := plugin.SendEmail(context.Background(), email)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.MessageID)
    assert.Equal(t, EmailStatusSent, result.Status)
}
```

## Related Documentation

- **[Email Module](../../modules/email.md)** - Core email interfaces
- **[SendGrid Plugin](./sendgrid.md)** - Alternative email provider
- **[Mailgun Plugin](./mailgun.md)** - Alternative email provider
- **[Plugin System](../overview.md)** - Plugin architecture
- **[AWS SES Documentation](https://docs.aws.amazon.com/ses/)** - Official AWS documentation
