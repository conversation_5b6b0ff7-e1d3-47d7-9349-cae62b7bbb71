# Mailgun Email Plugin

## Tổng quan

Mailgun Plugin cung cấp implementation của Email Service interface sử dụng Mailgun API. Plugin này tận dụng các tính năng mạnh mẽ của Mailgun như email validation, detailed analytics, và flexible routing.

## Features

### ✅ Supported Features
- **Transactional Emails**: Single và bulk email sending
- **Template Engine**: Mailgun templates và handlebars
- **Email Validation**: Real-time email address validation
- **Delivery Tracking**: Comprehensive delivery analytics
- **Webhooks**: Real-time event notifications
- **Attachments**: File attachments support
- **Routing**: Advanced email routing rules
- **Tagging**: Email categorization và filtering

### ❌ Limitations
- **Template Limitations**: Limited template features compared to SendGrid
- **Regional Restrictions**: Some features vary by region
- **Batch Size**: Maximum 1000 recipients per batch

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/email/mailgun"
    "github.com/your-org/blog-api-v3/internal/modules/email"
)

func init() {
    // Register Mailgun plugin
    email.RegisterPlugin("mailgun", mailgun.NewPlugin())
}
```

### 2. Configuration

```yaml
email:
  active_provider: "mailgun"
  providers:
    mailgun:
      plugin: "mailgun"
      config:
        api_key: "${MAILGUN_API_KEY}"
        domain: "yourdomain.com"
        base_url: "https://api.mailgun.net/v3" # or EU: https://api.eu.mailgun.net/v3
        
        # Optional settings
        from_email: "<EMAIL>"
        from_name: "Your App Name"
        
        # Tracking settings
        tracking: true
        tracking_clicks: true
        tracking_opens: true
        
        # Validation
        validate_emails: true
        
        # Rate limiting
        rate_limit:
          requests_per_second: 10
          burst_size: 50
```

### 3. Environment Variables

```bash
# Required
MAILGUN_API_KEY=your_mailgun_api_key_here
MAILGUN_DOMAIN=yourdomain.com

# Optional
MAILGUN_WEBHOOK_SIGNING_KEY=your_webhook_signing_key
```

## Implementation Details

### Mailgun Plugin Structure

```go
type MailgunPlugin struct {
    client    mailgun.Mailgun
    config    *MailgunConfig
    validator mailgun.EmailValidator
    logger    logger.Logger
}

type MailgunConfig struct {
    APIKey         string `yaml:"api_key"`
    Domain         string `yaml:"domain"`
    BaseURL        string `yaml:"base_url"`
    FromEmail      string `yaml:"from_email"`
    FromName       string `yaml:"from_name"`
    Tracking       bool   `yaml:"tracking"`
    TrackingClicks bool   `yaml:"tracking_clicks"`
    TrackingOpens  bool   `yaml:"tracking_opens"`
    ValidateEmails bool   `yaml:"validate_emails"`
    RateLimit      RateLimitConfig `yaml:"rate_limit"`
}
```

### Email Sending Implementation

```go
func (p *MailgunPlugin) SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error) {
    // Validate email addresses if enabled
    if p.config.ValidateEmails {
        if err := p.validateEmailAddresses(email.To); err != nil {
            return nil, fmt.Errorf("email validation failed: %w", err)
        }
    }
    
    // Create Mailgun message
    message := p.client.NewMessage(
        fmt.Sprintf("%s <%s>", p.config.FromName, p.config.FromEmail),
        email.Subject,
        email.TextContent,
        email.To...,
    )
    
    // Set HTML content
    if email.HTMLContent != "" {
        message.SetHtml(email.HTMLContent)
    }
    
    // Add CC and BCC
    for _, cc := range email.CC {
        message.AddCC(cc)
    }
    for _, bcc := range email.BCC {
        message.AddBCC(bcc)
    }
    
    // Set tracking options
    if p.config.Tracking {
        message.SetTracking(p.config.TrackingClicks)
        message.SetTrackingOpens(p.config.TrackingOpens)
    }
    
    // Add custom headers
    for key, value := range email.Headers {
        message.AddHeader(key, value)
    }
    
    // Add attachments
    for _, attachment := range email.Attachments {
        message.AddBufferAttachment(attachment.Filename, attachment.Content)
    }
    
    // Add metadata as tags
    if email.Metadata != nil {
        for key, value := range email.Metadata {
            message.AddTag(fmt.Sprintf("%s:%v", key, value))
        }
    }
    
    // Send email
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    
    msgID, id, err := p.client.Send(ctx, message)
    if err != nil {
        return nil, fmt.Errorf("mailgun send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  msgID,
        Status:     EmailStatusSent,
        ProviderID: "mailgun",
        Metadata: map[string]interface{}{
            "mailgun_id": id,
        },
        SentAt: timePtr(time.Now()),
    }, nil
}
```

### Template Implementation

```go
func (p *MailgunPlugin) SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error) {
    // Get template from Mailgun
    mgTemplate, err := p.getTemplate(template.TemplateID)
    if err != nil {
        return nil, fmt.Errorf("template not found: %w", err)
    }
    
    // Create message with template
    message := p.client.NewMessage(
        fmt.Sprintf("%s <%s>", p.config.FromName, p.config.FromEmail),
        "", // Subject will be from template
        "",
        template.To...,
    )
    
    // Set template
    message.SetTemplate(mgTemplate.Name)
    
    // Add template variables
    for key, value := range template.Variables {
        message.AddTemplateVariable(key, value)
    }
    
    // Set tracking
    if p.config.Tracking {
        message.SetTracking(p.config.TrackingClicks)
        message.SetTrackingOpens(p.config.TrackingOpens)
    }
    
    // Send templated email
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    
    msgID, id, err := p.client.Send(ctx, message)
    if err != nil {
        return nil, fmt.Errorf("mailgun template send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  msgID,
        Status:     EmailStatusSent,
        ProviderID: "mailgun",
        Metadata: map[string]interface{}{
            "mailgun_id": id,
            "template":   mgTemplate.Name,
        },
        SentAt: timePtr(time.Now()),
    }, nil
}
```

### Email Validation

```go
func (p *MailgunPlugin) validateEmailAddresses(emails []string) error {
    if !p.config.ValidateEmails {
        return nil
    }
    
    for _, email := range emails {
        validation, err := p.validator.ValidateEmail(context.Background(), email, false)
        if err != nil {
            return fmt.Errorf("validation request failed for %s: %w", email, err)
        }
        
        if !validation.IsValid {
            return fmt.Errorf("invalid email address: %s (reason: %s)", email, validation.Reason)
        }
        
        // Check for risky emails
        if validation.IsRisky {
            p.logger.Warn("risky email address detected", "email", email, "reason", validation.Reason)
        }
    }
    
    return nil
}
```

## Webhook Handling

### Webhook Configuration

```go
func (p *MailgunPlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Verify webhook signature
    if err := p.verifyWebhookSignature(payload, headers); err != nil {
        return nil, fmt.Errorf("webhook verification failed: %w", err)
    }
    
    // Parse Mailgun webhook
    var webhook MailgunWebhook
    if err := json.Unmarshal(payload, &webhook); err != nil {
        return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
    }
    
    // Process webhook event
    result := p.processWebhookEvent(ctx, &webhook)
    
    return &WebhookResult{
        Provider: "mailgun",
        Events:   []*WebhookEventResult{result},
    }, nil
}

func (p *MailgunPlugin) processWebhookEvent(ctx context.Context, webhook *MailgunWebhook) *WebhookEventResult {
    eventData := webhook.EventData
    
    switch webhook.Event {
    case "delivered":
        return &WebhookEventResult{
            MessageID: eventData.Message.Headers.MessageID,
            Event:     "delivered",
            Timestamp: time.Unix(int64(eventData.Timestamp), 0),
            Status:    EmailStatusDelivered,
            Metadata: map[string]interface{}{
                "recipient": eventData.Recipient,
                "tags":      eventData.Tags,
            },
        }
    case "failed":
        return &WebhookEventResult{
            MessageID: eventData.Message.Headers.MessageID,
            Event:     "bounce",
            Timestamp: time.Unix(int64(eventData.Timestamp), 0),
            Status:    EmailStatusBounced,
            Reason:    eventData.DeliveryStatus.Description,
            Metadata: map[string]interface{}{
                "recipient": eventData.Recipient,
                "severity":  eventData.Severity,
                "code":      eventData.DeliveryStatus.Code,
            },
        }
    case "opened":
        return &WebhookEventResult{
            MessageID: eventData.Message.Headers.MessageID,
            Event:     "open",
            Timestamp: time.Unix(int64(eventData.Timestamp), 0),
            Metadata: map[string]interface{}{
                "recipient":  eventData.Recipient,
                "user_agent": eventData.UserAgent,
                "ip":         eventData.IP,
                "city":       eventData.GeoLocation.City,
                "country":    eventData.GeoLocation.Country,
            },
        }
    case "clicked":
        return &WebhookEventResult{
            MessageID: eventData.Message.Headers.MessageID,
            Event:     "click",
            Timestamp: time.Unix(int64(eventData.Timestamp), 0),
            Metadata: map[string]interface{}{
                "recipient":  eventData.Recipient,
                "url":        eventData.URL,
                "user_agent": eventData.UserAgent,
                "ip":         eventData.IP,
            },
        }
    default:
        return &WebhookEventResult{
            MessageID: eventData.Message.Headers.MessageID,
            Event:     webhook.Event,
            Timestamp: time.Unix(int64(eventData.Timestamp), 0),
        }
    }
}
```

## Configuration Examples

### Basic Configuration

```yaml
email:
  providers:
    mailgun:
      plugin: "mailgun"
      config:
        api_key: "${MAILGUN_API_KEY}"
        domain: "yourdomain.com"
        from_email: "<EMAIL>"
        from_name: "Your App"
```

### Advanced Configuration with Validation

```yaml
email:
  providers:
    mailgun:
      plugin: "mailgun"
      config:
        api_key: "${MAILGUN_API_KEY}"
        domain: "yourdomain.com"
        base_url: "https://api.mailgun.net/v3"
        from_email: "<EMAIL>"
        from_name: "Your App"
        
        # Email validation
        validate_emails: true
        
        # Tracking settings
        tracking: true
        tracking_clicks: true
        tracking_opens: true
        
        # Rate limiting
        rate_limit:
          requests_per_second: 10
          burst_size: 50
```

## Error Handling

### Common Errors

```go
var (
    ErrInvalidAPIKey      = errors.New("invalid Mailgun API key")
    ErrInvalidDomain      = errors.New("invalid Mailgun domain")
    ErrEmailValidationFailed = errors.New("email validation failed")
    ErrRateLimitExceeded  = errors.New("Mailgun rate limit exceeded")
)

func (p *MailgunPlugin) handleMailgunError(err error) error {
    if mgErr, ok := err.(*mailgun.UnexpectedResponseError); ok {
        switch mgErr.Actual {
        case 401:
            return ErrInvalidAPIKey
        case 400:
            return ErrInvalidDomain
        case 429:
            return ErrRateLimitExceeded
        default:
            return fmt.Errorf("mailgun error: %s", mgErr.Message)
        }
    }
    return err
}
```

## Testing

### Unit Tests

```go
func TestMailgunPlugin_SendEmail(t *testing.T) {
    plugin := &MailgunPlugin{
        client: mockMailgunClient(),
        config: &MailgunConfig{
            Domain:    "test.example.com",
            FromEmail: "<EMAIL>",
            FromName:  "Test App",
        },
    }
    
    email := &EmailMessage{
        To:          []string{"<EMAIL>"},
        Subject:     "Test Email",
        HTMLContent: "<h1>Hello World</h1>",
        TextContent: "Hello World",
    }
    
    result, err := plugin.SendEmail(context.Background(), email)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.MessageID)
    assert.Equal(t, EmailStatusSent, result.Status)
}
```

## Related Documentation

- **[Email Module](../../modules/email.md)** - Core email interfaces
- **[SendGrid Plugin](./sendgrid.md)** - Alternative email provider
- **[SES Plugin](./ses.md)** - Amazon SES implementation
- **[Plugin System](../overview.md)** - Plugin architecture
- **[Creating Plugins](../creating-plugins.md)** - Plugin development guide
