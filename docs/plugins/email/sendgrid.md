# SendGrid Email Plugin

## Tổng quan

SendGrid Plugin cung cấp implementation của Email Service interface sử dụng SendGrid API. Plugin này hỗ trợ đầy đủ các tính năng của SendGrid bao gồm transactional emails, marketing campaigns, và advanced analytics.

## Features

### ✅ Supported Features
- **Transactional Emails**: Single và bulk email sending
- **Template Engine**: SendGrid dynamic templates
- **Delivery Tracking**: Real-time delivery status
- **Analytics**: Open, click, bounce, spam tracking
- **Webhooks**: Event notifications từ SendGrid
- **Attachments**: File attachments support
- **Personalization**: Dynamic content substitution
- **Suppression Management**: Unsubscribe và bounce handling

### ❌ Limitations
- **Rate Limits**: Theo SendGrid plan limits
- **Template Sync**: Templates phải được tạo trong SendGrid dashboard
- **Advanced Segmentation**: Requires SendGrid Marketing Campaigns

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/email/sendgrid"
    "github.com/your-org/blog-api-v3/internal/modules/email"
)

func init() {
    // Register SendGrid plugin
    email.RegisterPlugin("sendgrid", sendgrid.NewPlugin())
}
```

### 2. Configuration

```yaml
email:
  active_provider: "sendgrid"
  providers:
    sendgrid:
      plugin: "sendgrid"
      config:
        api_key: "${SENDGRID_API_KEY}"
        from_email: "<EMAIL>"
        from_name: "Your App Name"
        
        # Optional settings
        sandbox_mode: false
        click_tracking: true
        open_tracking: true
        subscription_tracking: true
        
        # Template settings
        template_engine: "sendgrid" # or "internal"
        
        # Rate limiting
        rate_limit:
          requests_per_second: 10
          burst_size: 100
```

### 3. Environment Variables

```bash
# Required
SENDGRID_API_KEY=your_sendgrid_api_key_here

# Optional
SENDGRID_WEBHOOK_VERIFY_KEY=your_webhook_verification_key
```

## Implementation Details

### SendGrid Plugin Structure

```go
type SendGridPlugin struct {
    client    *sendgrid.Client
    config    *SendGridConfig
    templates map[string]*sendgrid.Template
    logger    logger.Logger
}

type SendGridConfig struct {
    APIKey              string `yaml:"api_key"`
    FromEmail           string `yaml:"from_email"`
    FromName            string `yaml:"from_name"`
    SandboxMode         bool   `yaml:"sandbox_mode"`
    ClickTracking       bool   `yaml:"click_tracking"`
    OpenTracking        bool   `yaml:"open_tracking"`
    SubscriptionTracking bool  `yaml:"subscription_tracking"`
    TemplateEngine      string `yaml:"template_engine"`
    RateLimit           RateLimitConfig `yaml:"rate_limit"`
}
```

### Email Sending Implementation

```go
func (p *SendGridPlugin) SendEmail(ctx context.Context, email *EmailMessage) (*EmailResult, error) {
    // Create SendGrid message
    message := mail.NewV3Mail()
    
    // Set from address
    from := mail.NewEmail(p.config.FromName, p.config.FromEmail)
    message.SetFrom(from)
    
    // Set recipients
    personalization := mail.NewPersonalization()
    for _, to := range email.To {
        personalization.AddTos(mail.NewEmail("", to))
    }
    
    // Set content
    message.Subject = email.Subject
    if email.HTMLContent != "" {
        message.AddContent(mail.NewContent("text/html", email.HTMLContent))
    }
    if email.TextContent != "" {
        message.AddContent(mail.NewContent("text/plain", email.TextContent))
    }
    
    // Add personalization
    message.AddPersonalizations(personalization)
    
    // Send email
    response, err := p.client.Send(message)
    if err != nil {
        return nil, fmt.Errorf("sendgrid send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  response.Headers["X-Message-Id"][0],
        Status:     mapSendGridStatus(response.StatusCode),
        ProviderID: "sendgrid",
        SentAt:     timePtr(time.Now()),
    }, nil
}
```

### Template Implementation

```go
func (p *SendGridPlugin) SendTemplate(ctx context.Context, template *TemplateMessage) (*EmailResult, error) {
    // Get SendGrid template
    sgTemplate, err := p.getTemplate(template.TemplateID)
    if err != nil {
        return nil, fmt.Errorf("template not found: %w", err)
    }
    
    // Create message with template
    message := mail.NewV3Mail()
    message.SetTemplateID(sgTemplate.ID)
    
    // Set dynamic template data
    personalization := mail.NewPersonalization()
    for key, value := range template.Variables {
        personalization.SetDynamicTemplateData(key, value)
    }
    
    // Set recipients
    for _, to := range template.To {
        personalization.AddTos(mail.NewEmail("", to))
    }
    
    message.AddPersonalizations(personalization)
    
    // Send templated email
    response, err := p.client.Send(message)
    if err != nil {
        return nil, fmt.Errorf("sendgrid template send failed: %w", err)
    }
    
    return &EmailResult{
        MessageID:  response.Headers["X-Message-Id"][0],
        Status:     EmailStatusSent,
        ProviderID: "sendgrid",
        SentAt:     timePtr(time.Now()),
    }, nil
}
```

## Webhook Handling

### Webhook Configuration

```go
func (p *SendGridPlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Verify webhook signature
    if err := p.verifyWebhookSignature(payload, headers); err != nil {
        return nil, fmt.Errorf("webhook verification failed: %w", err)
    }
    
    // Parse SendGrid events
    var events []SendGridEvent
    if err := json.Unmarshal(payload, &events); err != nil {
        return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
    }
    
    // Process events
    results := make([]*WebhookEventResult, 0, len(events))
    for _, event := range events {
        result := p.processWebhookEvent(ctx, &event)
        results = append(results, result)
    }
    
    return &WebhookResult{
        Provider: "sendgrid",
        Events:   results,
    }, nil
}

func (p *SendGridPlugin) processWebhookEvent(ctx context.Context, event *SendGridEvent) *WebhookEventResult {
    switch event.Event {
    case "delivered":
        return &WebhookEventResult{
            MessageID: event.MessageID,
            Event:     "delivered",
            Timestamp: time.Unix(event.Timestamp, 0),
            Status:    EmailStatusDelivered,
        }
    case "bounce":
        return &WebhookEventResult{
            MessageID: event.MessageID,
            Event:     "bounce",
            Timestamp: time.Unix(event.Timestamp, 0),
            Status:    EmailStatusBounced,
            Reason:    event.Reason,
        }
    case "open":
        return &WebhookEventResult{
            MessageID: event.MessageID,
            Event:     "open",
            Timestamp: time.Unix(event.Timestamp, 0),
            Metadata: map[string]interface{}{
                "user_agent": event.UserAgent,
                "ip":         event.IP,
            },
        }
    case "click":
        return &WebhookEventResult{
            MessageID: event.MessageID,
            Event:     "click",
            Timestamp: time.Unix(event.Timestamp, 0),
            Metadata: map[string]interface{}{
                "url":        event.URL,
                "user_agent": event.UserAgent,
                "ip":         event.IP,
            },
        }
    default:
        return &WebhookEventResult{
            MessageID: event.MessageID,
            Event:     event.Event,
            Timestamp: time.Unix(event.Timestamp, 0),
        }
    }
}
```

## Configuration Examples

### Basic Configuration

```yaml
email:
  providers:
    sendgrid:
      plugin: "sendgrid"
      config:
        api_key: "${SENDGRID_API_KEY}"
        from_email: "<EMAIL>"
        from_name: "Your App"
```

### Advanced Configuration

```yaml
email:
  providers:
    sendgrid:
      plugin: "sendgrid"
      config:
        api_key: "${SENDGRID_API_KEY}"
        from_email: "<EMAIL>"
        from_name: "Your App"
        
        # Tracking settings
        click_tracking: true
        open_tracking: true
        subscription_tracking: true
        
        # Template settings
        template_engine: "sendgrid"
        
        # Rate limiting
        rate_limit:
          requests_per_second: 10
          burst_size: 100
          
        # Retry settings
        retry:
          max_attempts: 3
          backoff_multiplier: 2
          initial_delay: "1s"
```

## Error Handling

### Common Errors

```go
var (
    ErrInvalidAPIKey     = errors.New("invalid SendGrid API key")
    ErrRateLimitExceeded = errors.New("SendGrid rate limit exceeded")
    ErrTemplateNotFound  = errors.New("SendGrid template not found")
    ErrInvalidEmail      = errors.New("invalid email address")
)

func (p *SendGridPlugin) handleSendGridError(err error) error {
    if sgErr, ok := err.(*sendgrid.RestError); ok {
        switch sgErr.StatusCode {
        case 401:
            return ErrInvalidAPIKey
        case 429:
            return ErrRateLimitExceeded
        case 404:
            return ErrTemplateNotFound
        case 400:
            return ErrInvalidEmail
        default:
            return fmt.Errorf("sendgrid error: %s", sgErr.Message)
        }
    }
    return err
}
```

## Testing

### Unit Tests

```go
func TestSendGridPlugin_SendEmail(t *testing.T) {
    plugin := &SendGridPlugin{
        client: mockSendGridClient(),
        config: &SendGridConfig{
            FromEmail: "<EMAIL>",
            FromName:  "Test App",
        },
    }
    
    email := &EmailMessage{
        To:          []string{"<EMAIL>"},
        Subject:     "Test Email",
        HTMLContent: "<h1>Hello World</h1>",
        TextContent: "Hello World",
    }
    
    result, err := plugin.SendEmail(context.Background(), email)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.MessageID)
    assert.Equal(t, EmailStatusSent, result.Status)
}
```

## Related Documentation

- **[Email Module](../../modules/email.md)** - Core email interfaces
- **[Plugin System](../overview.md)** - Plugin architecture
- **[Creating Plugins](../creating-plugins.md)** - Plugin development guide
- **[Mailgun Plugin](./mailgun.md)** - Alternative email provider
- **[SES Plugin](./ses.md)** - Amazon SES implementation
