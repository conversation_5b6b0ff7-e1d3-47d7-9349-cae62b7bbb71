# PayPal Payment Plugin

## Tổng quan

PayPal Plugin cung cấp implementation của Payment Service interface sử dụng PayPal REST API. Plugin này hỗ trợ PayPal payments, PayPal subscriptions, và various payment methods thông qua PayPal ecosystem.

## Features

### ✅ Supported Features
- **PayPal Payments**: PayPal account payments
- **Credit Card Processing**: Direct credit card payments
- **Subscription Management**: Recurring billing plans
- **Webhooks**: Real-time event notifications
- **Multi-currency**: Support 100+ currencies
- **Refunds**: Full và partial refunds
- **Dispute Management**: Chargeback và dispute handling
- **Express Checkout**: Streamlined checkout experience

### ❌ Limitations
- **Geographic Restrictions**: Availability varies by country
- **Processing Fees**: Transaction fees apply
- **Payout Delays**: Funds may be held for new merchants
- **Limited Customization**: Less flexible than some alternatives

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/payment/paypal"
    "github.com/your-org/blog-api-v3/internal/modules/payment"
)

func init() {
    // Register PayPal plugin
    payment.RegisterPlugin("paypal", paypal.NewPlugin())
}
```

### 2. Configuration

```yaml
payment:
  active_gateway: "paypal"
  gateways:
    paypal:
      plugin: "paypal"
      config:
        client_id: "${PAYPAL_CLIENT_ID}"
        client_secret: "${PAYPAL_CLIENT_SECRET}"
        environment: "sandbox" # or "live"
        
        # Webhook settings
        webhook_id: "${PAYPAL_WEBHOOK_ID}"
        
        # Payment settings
        intent: "CAPTURE" # or "AUTHORIZE"
        
        # Return URLs
        return_url: "${BASE_URL}/payment/paypal/return"
        cancel_url: "${BASE_URL}/payment/paypal/cancel"
        
        # Supported payment methods
        payment_methods: ["paypal", "card"]
        
        # Currency settings
        default_currency: "usd"
        supported_currencies: ["usd", "eur", "gbp", "cad", "aud"]
```

### 3. Environment Variables

```bash
# Required
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Optional
PAYPAL_WEBHOOK_ID=your_webhook_id
BASE_URL=https://yourdomain.com
```

## Implementation Details

### PayPal Plugin Structure

```go
type PayPalPlugin struct {
    client    *paypal.Client
    config    *PayPalConfig
    logger    logger.Logger
}

type PayPalConfig struct {
    ClientID         string   `yaml:"client_id"`
    ClientSecret     string   `yaml:"client_secret"`
    Environment      string   `yaml:"environment"`
    WebhookID        string   `yaml:"webhook_id"`
    Intent           string   `yaml:"intent"`
    ReturnURL        string   `yaml:"return_url"`
    CancelURL        string   `yaml:"cancel_url"`
    PaymentMethods   []string `yaml:"payment_methods"`
    DefaultCurrency  string   `yaml:"default_currency"`
    SupportedCurrencies []string `yaml:"supported_currencies"`
}
```

### Payment Processing Implementation

```go
func (p *PayPalPlugin) ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error) {
    // Create PayPal order
    order := &paypal.Order{
        Intent: p.config.Intent,
        PurchaseUnits: []paypal.PurchaseUnit{
            {
                Amount: &paypal.Amount{
                    CurrencyCode: payment.Currency,
                    Value:        payment.Amount.String(),
                },
                Description: payment.Description,
                CustomID:    payment.OrderID,
            },
        },
        ApplicationContext: &paypal.ApplicationContext{
            ReturnURL: p.config.ReturnURL,
            CancelURL: p.config.CancelURL,
            UserAction: "PAY_NOW",
            PaymentMethod: &paypal.PaymentMethod{
                PayerSelected: "PAYPAL",
                PayeePreferred: "IMMEDIATE_PAYMENT_REQUIRED",
            },
        },
    }
    
    // Add payment source if provided
    if payment.PaymentMethodID != "" {
        order.PaymentSource = &paypal.PaymentSource{
            PayPal: &paypal.PayPalPaymentSource{
                ExperienceContext: &paypal.ExperienceContext{
                    ReturnURL: p.config.ReturnURL,
                    CancelURL: p.config.CancelURL,
                },
            },
        }
    }
    
    // Create order
    createdOrder, err := p.client.CreateOrder(ctx, order)
    if err != nil {
        return nil, fmt.Errorf("paypal order creation failed: %w", err)
    }
    
    // Get approval URL
    var approvalURL string
    for _, link := range createdOrder.Links {
        if link.Rel == "approve" {
            approvalURL = link.Href
            break
        }
    }
    
    return &PaymentResult{
        PaymentID:   createdOrder.ID,
        Status:      mapPayPalStatus(createdOrder.Status),
        Amount:      payment.Amount,
        Currency:    payment.Currency,
        ProviderID:  "paypal",
        ApprovalURL: approvalURL,
        Metadata: map[string]interface{}{
            "paypal_order_id": createdOrder.ID,
            "approval_url":    approvalURL,
        },
        CreatedAt: timePtr(time.Now()),
    }, nil
}

func (p *PayPalPlugin) CapturePayment(ctx context.Context, paymentID string, amount *decimal.Decimal) (*PaymentResult, error) {
    // Capture PayPal order
    captureRequest := &paypal.CaptureRequest{}
    if amount != nil {
        captureRequest.Amount = &paypal.Amount{
            CurrencyCode: "USD", // Should be from original order
            Value:        amount.String(),
        }
    }
    
    capturedOrder, err := p.client.CaptureOrder(ctx, paymentID, captureRequest)
    if err != nil {
        return nil, fmt.Errorf("paypal capture failed: %w", err)
    }
    
    // Extract capture details
    var captureID string
    var capturedAmount decimal.Decimal
    if len(capturedOrder.PurchaseUnits) > 0 && len(capturedOrder.PurchaseUnits[0].Payments.Captures) > 0 {
        capture := capturedOrder.PurchaseUnits[0].Payments.Captures[0]
        captureID = capture.ID
        capturedAmount, _ = decimal.NewFromString(capture.Amount.Value)
    }
    
    return &PaymentResult{
        PaymentID:  capturedOrder.ID,
        CaptureID:  captureID,
        Status:     mapPayPalStatus(capturedOrder.Status),
        Amount:     capturedAmount,
        Currency:   capturedOrder.PurchaseUnits[0].Amount.CurrencyCode,
        ProviderID: "paypal",
        Metadata: map[string]interface{}{
            "capture_id": captureID,
        },
        CapturedAt: timePtr(time.Now()),
    }, nil
}
```

### Subscription Management Implementation

```go
func (p *PayPalPlugin) CreateSubscription(ctx context.Context, subscription *SubscriptionRequest) (*SubscriptionResult, error) {
    // First, create or get billing plan
    plan, err := p.getOrCreateBillingPlan(subscription.PlanID, subscription.PlanDetails)
    if err != nil {
        return nil, fmt.Errorf("billing plan creation failed: %w", err)
    }
    
    // Create subscription
    sub := &paypal.Subscription{
        PlanID: plan.ID,
        Subscriber: &paypal.Subscriber{
            EmailAddress: subscription.CustomerEmail,
            Name: &paypal.Name{
                GivenName: subscription.CustomerName,
            },
        },
        ApplicationContext: &paypal.ApplicationContext{
            ReturnURL: p.config.ReturnURL,
            CancelURL: p.config.CancelURL,
            UserAction: "SUBSCRIBE_NOW",
        },
    }
    
    // Add trial period if specified
    if subscription.TrialDays > 0 {
        trialEnd := time.Now().AddDate(0, 0, subscription.TrialDays)
        sub.StartTime = trialEnd.Format(time.RFC3339)
    }
    
    // Create subscription
    createdSub, err := p.client.CreateSubscription(ctx, sub)
    if err != nil {
        return nil, fmt.Errorf("paypal subscription creation failed: %w", err)
    }
    
    // Get approval URL
    var approvalURL string
    for _, link := range createdSub.Links {
        if link.Rel == "approve" {
            approvalURL = link.Href
            break
        }
    }
    
    return &SubscriptionResult{
        SubscriptionID: createdSub.ID,
        Status:         mapPayPalSubscriptionStatus(createdSub.Status),
        PlanID:         plan.ID,
        ApprovalURL:    approvalURL,
        ProviderID:     "paypal",
        Metadata: map[string]interface{}{
            "paypal_subscription_id": createdSub.ID,
            "approval_url":           approvalURL,
        },
        CreatedAt: timePtr(time.Now()),
    }, nil
}

func (p *PayPalPlugin) getOrCreateBillingPlan(planID string, planDetails *PlanDetails) (*paypal.Plan, error) {
    // Try to get existing plan
    plan, err := p.client.GetPlan(context.Background(), planID)
    if err == nil {
        return plan, nil
    }
    
    // Create new billing plan
    newPlan := &paypal.Plan{
        ProductID:   planDetails.ProductID,
        Name:        planDetails.Name,
        Description: planDetails.Description,
        Status:      "ACTIVE",
        BillingCycles: []paypal.BillingCycle{
            {
                Frequency: &paypal.Frequency{
                    IntervalUnit:  planDetails.IntervalUnit,
                    IntervalCount: planDetails.IntervalCount,
                },
                TenureType: "REGULAR",
                Sequence:   1,
                PricingScheme: &paypal.PricingScheme{
                    FixedPrice: &paypal.Money{
                        CurrencyCode: planDetails.Currency,
                        Value:        planDetails.Amount.String(),
                    },
                },
            },
        },
        PaymentPreferences: &paypal.PaymentPreferences{
            AutoBillOutstanding:     true,
            SetupFeeFailureAction:   "CONTINUE",
            PaymentFailureThreshold: 3,
        },
    }
    
    return p.client.CreatePlan(context.Background(), newPlan)
}
```

### Webhook Handling Implementation

```go
func (p *PayPalPlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Verify webhook signature
    if err := p.verifyWebhookSignature(payload, headers); err != nil {
        return nil, fmt.Errorf("webhook verification failed: %w", err)
    }
    
    // Parse PayPal webhook
    var webhook PayPalWebhook
    if err := json.Unmarshal(payload, &webhook); err != nil {
        return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
    }
    
    // Process webhook event
    result := p.processWebhookEvent(ctx, &webhook)
    
    return &WebhookResult{
        Provider: "paypal",
        Events:   []*WebhookEventResult{result},
    }, nil
}

func (p *PayPalPlugin) processWebhookEvent(ctx context.Context, webhook *PayPalWebhook) *WebhookEventResult {
    switch webhook.EventType {
    case "PAYMENT.CAPTURE.COMPLETED":
        return &WebhookEventResult{
            PaymentID: webhook.Resource.ID,
            Event:     "payment_completed",
            Timestamp: webhook.CreateTime,
            Status:    PaymentStatusSucceeded,
            Amount:    parsePayPalAmount(webhook.Resource.Amount),
            Currency:  webhook.Resource.Amount.CurrencyCode,
        }
        
    case "PAYMENT.CAPTURE.DENIED":
        return &WebhookEventResult{
            PaymentID: webhook.Resource.ID,
            Event:     "payment_denied",
            Timestamp: webhook.CreateTime,
            Status:    PaymentStatusFailed,
            Reason:    webhook.Resource.StatusDetails.Reason,
        }
        
    case "BILLING.SUBSCRIPTION.ACTIVATED":
        return &WebhookEventResult{
            SubscriptionID: webhook.Resource.ID,
            Event:          "subscription_activated",
            Timestamp:      webhook.CreateTime,
            Status:         SubscriptionStatusActive,
        }
        
    case "BILLING.SUBSCRIPTION.CANCELLED":
        return &WebhookEventResult{
            SubscriptionID: webhook.Resource.ID,
            Event:          "subscription_cancelled",
            Timestamp:      webhook.CreateTime,
            Status:         SubscriptionStatusCancelled,
            Reason:         webhook.Resource.StatusChangeNote,
        }
        
    case "BILLING.SUBSCRIPTION.PAYMENT.FAILED":
        return &WebhookEventResult{
            SubscriptionID: webhook.Resource.ID,
            Event:          "subscription_payment_failed",
            Timestamp:      webhook.CreateTime,
            Status:         SubscriptionStatusPastDue,
            Reason:         "Payment failed",
        }
        
    default:
        return &WebhookEventResult{
            Event:     webhook.EventType,
            Timestamp: webhook.CreateTime,
            Metadata: map[string]interface{}{
                "paypal_event_id": webhook.ID,
                "resource_type":   webhook.ResourceType,
            },
        }
    }
}
```

## Configuration Examples

### Basic Configuration

```yaml
payment:
  gateways:
    paypal:
      plugin: "paypal"
      config:
        client_id: "${PAYPAL_CLIENT_ID}"
        client_secret: "${PAYPAL_CLIENT_SECRET}"
        environment: "sandbox"
        return_url: "${BASE_URL}/payment/paypal/return"
        cancel_url: "${BASE_URL}/payment/paypal/cancel"
```

### Advanced Configuration

```yaml
payment:
  gateways:
    paypal:
      plugin: "paypal"
      config:
        client_id: "${PAYPAL_CLIENT_ID}"
        client_secret: "${PAYPAL_CLIENT_SECRET}"
        environment: "live"
        webhook_id: "${PAYPAL_WEBHOOK_ID}"
        
        # Payment settings
        intent: "CAPTURE"
        
        # URLs
        return_url: "${BASE_URL}/payment/paypal/return"
        cancel_url: "${BASE_URL}/payment/paypal/cancel"
        
        # Payment methods
        payment_methods: ["paypal", "card"]
        
        # Multi-currency
        default_currency: "usd"
        supported_currencies: ["usd", "eur", "gbp", "cad"]
```

## Error Handling

### Common Errors

```go
var (
    ErrPayPalInvalidCredentials = errors.New("invalid PayPal credentials")
    ErrPayPalPaymentDeclined    = errors.New("payment was declined")
    ErrPayPalInsufficientFunds  = errors.New("insufficient funds")
    ErrPayPalOrderNotFound      = errors.New("PayPal order not found")
)

func (p *PayPalPlugin) handlePayPalError(err error) error {
    if ppErr, ok := err.(*paypal.ErrorResponse); ok {
        switch ppErr.Name {
        case "INVALID_CLIENT_CREDENTIALS":
            return ErrPayPalInvalidCredentials
        case "PAYMENT_DENIED":
            return ErrPayPalPaymentDeclined
        case "INSUFFICIENT_FUNDS":
            return ErrPayPalInsufficientFunds
        case "RESOURCE_NOT_FOUND":
            return ErrPayPalOrderNotFound
        default:
            return fmt.Errorf("paypal error: %s", ppErr.Message)
        }
    }
    return err
}
```

## Testing

### Unit Tests

```go
func TestPayPalPlugin_ProcessPayment(t *testing.T) {
    plugin := &PayPalPlugin{
        client: mockPayPalClient(),
        config: &PayPalConfig{
            ClientID:        "test_client_id",
            ClientSecret:    "test_client_secret",
            Environment:     "sandbox",
            DefaultCurrency: "usd",
        },
    }
    
    payment := &PaymentRequest{
        Amount:      decimal.NewFromFloat(10.00),
        Currency:    "USD",
        Description: "Test payment",
        OrderID:     "test_order_123",
    }
    
    result, err := plugin.ProcessPayment(context.Background(), payment)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.PaymentID)
    assert.NotEmpty(t, result.ApprovalURL)
}
```

## Related Documentation

- **[Payment Module](../../modules/payment.md)** - Core payment interfaces
- **[Stripe Plugin](./stripe.md)** - Alternative payment gateway
- **[VNPay Plugin](./vnpay.md)** - Vietnamese payment gateway
- **[Plugin System](../overview.md)** - Plugin architecture
- **[PayPal Documentation](https://developer.paypal.com/)** - Official PayPal documentation
