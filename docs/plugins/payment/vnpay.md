# VNPay Payment Plugin

## Tổng quan

VNPay Plugin cung cấp implementation của Payment Service interface sử dụng VNPay API. Plugin này được thiết kế đặc biệt cho thị trường Việt Nam, hỗ trợ các phương thức thanh toán phổ biến tại Việt Nam.

## Features

### ✅ Supported Features
- **Domestic Cards**: Thẻ ATM nội địa Việt Nam
- **International Cards**: Visa, MasterCard, JCB, AMEX
- **QR Code Payments**: VNPay QR
- **Mobile Banking**: Thanh toán qua mobile banking
- **E-Wallets**: Ví điện tử VNPay
- **Installment Payments**: Thanh toán trả góp
- **Refunds**: Hoàn tiền full và partial
- **Multi-currency**: VND, USD

### ❌ Limitations
- **Geographic Focus**: Primarily for Vietnamese market
- **Documentation**: Limited English documentation
- **Integration Complexity**: Requires specific Vietnamese banking knowledge
- **Compliance**: Must comply with Vietnamese regulations

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/payment/vnpay"
    "github.com/your-org/blog-api-v3/internal/modules/payment"
)

func init() {
    // Register VNPay plugin
    payment.RegisterPlugin("vnpay", vnpay.NewPlugin())
}
```

### 2. Configuration

```yaml
payment:
  active_gateway: "vnpay"
  gateways:
    vnpay:
      plugin: "vnpay"
      config:
        merchant_id: "${VNPAY_MERCHANT_ID}"
        secret_key: "${VNPAY_SECRET_KEY}"
        environment: "sandbox" # or "production"
        
        # URLs
        return_url: "${BASE_URL}/payment/vnpay/return"
        notify_url: "${BASE_URL}/payment/vnpay/notify"
        
        # Payment settings
        version: "2.1.0"
        command: "pay"
        currency_code: "VND"
        locale: "vn" # or "en"
        
        # Supported payment methods
        payment_methods: ["VNPAYQR", "VNBANK", "INTCARD"]
        
        # Timeout settings
        timeout_minutes: 15
```

### 3. Environment Variables

```bash
# Required
VNPAY_MERCHANT_ID=your_vnpay_merchant_id
VNPAY_SECRET_KEY=your_vnpay_secret_key

# Optional
BASE_URL=https://yourdomain.com
```

## Implementation Details

### VNPay Plugin Structure

```go
type VNPayPlugin struct {
    config    *VNPayConfig
    client    *http.Client
    logger    logger.Logger
}

type VNPayConfig struct {
    MerchantID      string   `yaml:"merchant_id"`
    SecretKey       string   `yaml:"secret_key"`
    Environment     string   `yaml:"environment"`
    ReturnURL       string   `yaml:"return_url"`
    NotifyURL       string   `yaml:"notify_url"`
    Version         string   `yaml:"version"`
    Command         string   `yaml:"command"`
    CurrencyCode    string   `yaml:"currency_code"`
    Locale          string   `yaml:"locale"`
    PaymentMethods  []string `yaml:"payment_methods"`
    TimeoutMinutes  int      `yaml:"timeout_minutes"`
}
```

### Payment Processing Implementation

```go
func (p *VNPayPlugin) ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error) {
    // Generate transaction reference
    txnRef := p.generateTxnRef(payment.OrderID)
    
    // Create VNPay payment parameters
    params := map[string]string{
        "vnp_Version":    p.config.Version,
        "vnp_Command":    p.config.Command,
        "vnp_TmnCode":    p.config.MerchantID,
        "vnp_Amount":     p.formatAmount(payment.Amount),
        "vnp_CurrCode":   p.config.CurrencyCode,
        "vnp_TxnRef":     txnRef,
        "vnp_OrderInfo":  payment.Description,
        "vnp_OrderType":  "other",
        "vnp_Locale":     p.config.Locale,
        "vnp_ReturnUrl":  p.config.ReturnURL,
        "vnp_IpAddr":     p.getClientIP(ctx),
        "vnp_CreateDate": time.Now().Format("**************"),
    }
    
    // Add expiry time
    if p.config.TimeoutMinutes > 0 {
        expireTime := time.Now().Add(time.Duration(p.config.TimeoutMinutes) * time.Minute)
        params["vnp_ExpireDate"] = expireTime.Format("**************")
    }
    
    // Add payment method if specified
    if payment.PaymentMethodID != "" {
        params["vnp_BankCode"] = payment.PaymentMethodID
    }
    
    // Add customer info if available
    if payment.CustomerEmail != "" {
        params["vnp_Bill_Email"] = payment.CustomerEmail
    }
    if payment.CustomerPhone != "" {
        params["vnp_Bill_Mobile"] = payment.CustomerPhone
    }
    
    // Generate secure hash
    secureHash := p.generateSecureHash(params)
    params["vnp_SecureHash"] = secureHash
    
    // Build payment URL
    paymentURL := p.buildPaymentURL(params)
    
    return &PaymentResult{
        PaymentID:   txnRef,
        Status:      PaymentStatusPending,
        Amount:      payment.Amount,
        Currency:    p.config.CurrencyCode,
        ProviderID:  "vnpay",
        PaymentURL:  paymentURL,
        Metadata: map[string]interface{}{
            "vnpay_txn_ref": txnRef,
            "payment_url":   paymentURL,
        },
        CreatedAt: timePtr(time.Now()),
    }, nil
}

func (p *VNPayPlugin) generateSecureHash(params map[string]string) string {
    // Sort parameters by key
    keys := make([]string, 0, len(params))
    for k := range params {
        if k != "vnp_SecureHash" {
            keys = append(keys, k)
        }
    }
    sort.Strings(keys)
    
    // Build query string
    var queryParts []string
    for _, key := range keys {
        if params[key] != "" {
            queryParts = append(queryParts, fmt.Sprintf("%s=%s", key, url.QueryEscape(params[key])))
        }
    }
    queryString := strings.Join(queryParts, "&")
    
    // Generate HMAC SHA512
    mac := hmac.New(sha512.New, []byte(p.config.SecretKey))
    mac.Write([]byte(queryString))
    return hex.EncodeToString(mac.Sum(nil))
}

func (p *VNPayPlugin) buildPaymentURL(params map[string]string) string {
    baseURL := p.getBaseURL()
    
    values := url.Values{}
    for key, value := range params {
        values.Set(key, value)
    }
    
    return fmt.Sprintf("%s?%s", baseURL, values.Encode())
}

func (p *VNPayPlugin) getBaseURL() string {
    if p.config.Environment == "production" {
        return "https://vnpayment.vn/paymentv2/vpcpay.html"
    }
    return "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"
}
```

### Return URL Handling

```go
func (p *VNPayPlugin) HandleReturn(ctx context.Context, params map[string]string) (*PaymentResult, error) {
    // Verify secure hash
    if !p.verifySecureHash(params) {
        return nil, fmt.Errorf("invalid secure hash")
    }
    
    // Extract payment information
    txnRef := params["vnp_TxnRef"]
    responseCode := params["vnp_ResponseCode"]
    amount, _ := strconv.ParseInt(params["vnp_Amount"], 10, 64)
    transactionNo := params["vnp_TransactionNo"]
    
    // Map VNPay response code to payment status
    status := p.mapResponseCodeToStatus(responseCode)
    
    result := &PaymentResult{
        PaymentID:     txnRef,
        Status:        status,
        Amount:        decimal.NewFromInt(amount).Div(decimal.NewFromInt(100)), // VNPay amount is in cents
        Currency:      p.config.CurrencyCode,
        ProviderID:    "vnpay",
        TransactionID: transactionNo,
        Metadata: map[string]interface{}{
            "vnpay_response_code": responseCode,
            "vnpay_transaction_no": transactionNo,
        },
    }
    
    if status == PaymentStatusFailed {
        result.Error = p.getErrorMessage(responseCode)
    }
    
    return result, nil
}

func (p *VNPayPlugin) mapResponseCodeToStatus(responseCode string) PaymentStatus {
    switch responseCode {
    case "00":
        return PaymentStatusSucceeded
    case "07":
        return PaymentStatusPending // Trừ tiền thành công, giao dịch đang được xử lý
    case "09":
        return PaymentStatusPending // Giao dịch chưa hoàn tất
    default:
        return PaymentStatusFailed
    }
}

func (p *VNPayPlugin) getErrorMessage(responseCode string) string {
    errorMessages := map[string]string{
        "01": "Giao dịch chưa hoàn tất",
        "02": "Giao dịch bị lỗi",
        "04": "Giao dịch đảo (Khách hàng đã bị trừ tiền tại Ngân hàng nhưng GD chưa thành công ở VNPAY)",
        "05": "VNPAY đang xử lý giao dịch này (GD hoàn tiền)",
        "06": "VNPAY đã gửi yêu cầu hoàn tiền sang Ngân hàng (GD hoàn tiền)",
        "07": "Giao dịch bị nghi ngờ",
        "09": "GD Hoàn trả bị từ chối",
        "10": "Đã giao hàng",
        "11": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng",
        "12": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa",
        "13": "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP)",
        "24": "Giao dịch không thành công do: Khách hàng hủy giao dịch",
        "51": "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch",
        "65": "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày",
        "75": "Ngân hàng thanh toán đang bảo trì",
        "79": "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định",
        "99": "Các lỗi khác",
    }
    
    if msg, exists := errorMessages[responseCode]; exists {
        return msg
    }
    return "Giao dịch không thành công"
}
```

### Webhook/IPN Handling

```go
func (p *VNPayPlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Parse form data from VNPay IPN
    values, err := url.ParseQuery(string(payload))
    if err != nil {
        return nil, fmt.Errorf("failed to parse IPN data: %w", err)
    }
    
    // Convert to map
    params := make(map[string]string)
    for key, vals := range values {
        if len(vals) > 0 {
            params[key] = vals[0]
        }
    }
    
    // Verify secure hash
    if !p.verifySecureHash(params) {
        return nil, fmt.Errorf("invalid IPN secure hash")
    }
    
    // Process IPN
    result := p.processIPN(ctx, params)
    
    return &WebhookResult{
        Provider: "vnpay",
        Events:   []*WebhookEventResult{result},
    }, nil
}

func (p *VNPayPlugin) processIPN(ctx context.Context, params map[string]string) *WebhookEventResult {
    txnRef := params["vnp_TxnRef"]
    responseCode := params["vnp_ResponseCode"]
    amount, _ := strconv.ParseInt(params["vnp_Amount"], 10, 64)
    transactionNo := params["vnp_TransactionNo"]
    payDate := params["vnp_PayDate"]
    
    // Parse pay date
    var timestamp time.Time
    if payDate != "" {
        timestamp, _ = time.Parse("**************", payDate)
    } else {
        timestamp = time.Now()
    }
    
    status := p.mapResponseCodeToStatus(responseCode)
    
    result := &WebhookEventResult{
        PaymentID: txnRef,
        Event:     "payment_status_update",
        Timestamp: timestamp,
        Status:    status,
        Amount:    decimal.NewFromInt(amount).Div(decimal.NewFromInt(100)),
        Currency:  p.config.CurrencyCode,
        Metadata: map[string]interface{}{
            "vnpay_response_code":  responseCode,
            "vnpay_transaction_no": transactionNo,
            "vnpay_pay_date":      payDate,
        },
    }
    
    if status == PaymentStatusFailed {
        result.Reason = p.getErrorMessage(responseCode)
    }
    
    return result
}
```

## Configuration Examples

### Basic Configuration

```yaml
payment:
  gateways:
    vnpay:
      plugin: "vnpay"
      config:
        merchant_id: "${VNPAY_MERCHANT_ID}"
        secret_key: "${VNPAY_SECRET_KEY}"
        environment: "sandbox"
        return_url: "${BASE_URL}/payment/vnpay/return"
        notify_url: "${BASE_URL}/payment/vnpay/notify"
        currency_code: "VND"
```

### Advanced Configuration

```yaml
payment:
  gateways:
    vnpay:
      plugin: "vnpay"
      config:
        merchant_id: "${VNPAY_MERCHANT_ID}"
        secret_key: "${VNPAY_SECRET_KEY}"
        environment: "production"
        
        # URLs
        return_url: "${BASE_URL}/payment/vnpay/return"
        notify_url: "${BASE_URL}/payment/vnpay/notify"
        
        # Payment settings
        version: "2.1.0"
        currency_code: "VND"
        locale: "vn"
        
        # Supported payment methods
        payment_methods: ["VNPAYQR", "VNBANK", "INTCARD"]
        
        # Timeout
        timeout_minutes: 15
```

## Error Handling

### Common Errors

```go
var (
    ErrVNPayInvalidHash     = errors.New("invalid VNPay secure hash")
    ErrVNPayTransactionFailed = errors.New("VNPay transaction failed")
    ErrVNPayInsufficientFunds = errors.New("insufficient funds")
    ErrVNPayCardBlocked     = errors.New("card is blocked")
)

func (p *VNPayPlugin) handleVNPayError(responseCode string) error {
    switch responseCode {
    case "51":
        return ErrVNPayInsufficientFunds
    case "12":
        return ErrVNPayCardBlocked
    case "24":
        return errors.New("transaction cancelled by customer")
    case "75":
        return errors.New("bank is under maintenance")
    default:
        return ErrVNPayTransactionFailed
    }
}
```

## Testing

### Unit Tests

```go
func TestVNPayPlugin_ProcessPayment(t *testing.T) {
    plugin := &VNPayPlugin{
        config: &VNPayConfig{
            MerchantID:   "test_merchant",
            SecretKey:    "test_secret_key",
            Environment:  "sandbox",
            CurrencyCode: "VND",
            ReturnURL:    "http://localhost/return",
        },
    }
    
    payment := &PaymentRequest{
        Amount:      decimal.NewFromFloat(100000), // 100,000 VND
        Currency:    "VND",
        Description: "Test payment",
        OrderID:     "test_order_123",
    }
    
    result, err := plugin.ProcessPayment(context.Background(), payment)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.PaymentID)
    assert.NotEmpty(t, result.PaymentURL)
    assert.Equal(t, PaymentStatusPending, result.Status)
}
```

## Related Documentation

- **[Payment Module](../../modules/payment.md)** - Core payment interfaces
- **[Stripe Plugin](./stripe.md)** - International payment gateway
- **[PayPal Plugin](./paypal.md)** - Alternative payment gateway
- **[Plugin System](../overview.md)** - Plugin architecture
- **[VNPay Documentation](https://sandbox.vnpayment.vn/apis/)** - Official VNPay documentation
