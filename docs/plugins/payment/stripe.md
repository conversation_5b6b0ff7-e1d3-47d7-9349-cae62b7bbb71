# Stripe Payment Plugin

## Tổng quan

Stripe Plugin cung cấp implementation của Payment Service interface sử dụng Stripe API. Plugin này tận dụng đầy đủ các tính năng mạnh mẽ của Stripe như subscription management, payment methods, webhooks, và comprehensive analytics.

## Features

### ✅ Supported Features
- **Payment Processing**: Credit cards, digital wallets, bank transfers
- **Subscription Management**: Recurring billing với flexible plans
- **Payment Methods**: Save và reuse payment methods
- **Webhooks**: Real-time event notifications
- **Multi-currency**: Support 135+ currencies
- **3D Secure**: Strong Customer Authentication (SCA)
- **Refunds & Disputes**: Comprehensive refund và chargeback handling
- **Connect Platform**: Multi-party payments (marketplace)

### ❌ Limitations
- **Geographic Restrictions**: Not available in all countries
- **Processing Fees**: Transaction fees apply
- **Payout Schedule**: Varies by country và account type

## Installation

### 1. Plugin Registration

```go
import (
    "github.com/your-org/blog-api-v3/plugins/payment/stripe"
    "github.com/your-org/blog-api-v3/internal/modules/payment"
)

func init() {
    // Register Stripe plugin
    payment.RegisterPlugin("stripe", stripe.NewPlugin())
}
```

### 2. Configuration

```yaml
payment:
  active_gateway: "stripe"
  gateways:
    stripe:
      plugin: "stripe"
      config:
        secret_key: "${STRIPE_SECRET_KEY}"
        publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
        webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
        
        # Optional settings
        api_version: "2023-10-16"
        capture_method: "automatic" # or "manual"
        
        # Connect platform (for marketplaces)
        connect_enabled: false
        
        # 3D Secure settings
        require_3ds: "automatic" # "automatic", "required", "disabled"
        
        # Supported payment methods
        payment_methods: ["card", "sepa_debit", "ideal", "sofort"]
        
        # Currency settings
        default_currency: "usd"
        supported_currencies: ["usd", "eur", "gbp", "vnd"]
```

### 3. Environment Variables

```bash
# Required
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Optional
STRIPE_CONNECT_CLIENT_ID=ca_... # For Connect platform
```

## Implementation Details

### Stripe Plugin Structure

```go
type StripePlugin struct {
    client    *stripe.Client
    config    *StripeConfig
    webhooks  *webhook.Client
    logger    logger.Logger
}

type StripeConfig struct {
    SecretKey         string   `yaml:"secret_key"`
    PublishableKey    string   `yaml:"publishable_key"`
    WebhookSecret     string   `yaml:"webhook_secret"`
    APIVersion        string   `yaml:"api_version"`
    CaptureMethod     string   `yaml:"capture_method"`
    ConnectEnabled    bool     `yaml:"connect_enabled"`
    Require3DS        string   `yaml:"require_3ds"`
    PaymentMethods    []string `yaml:"payment_methods"`
    DefaultCurrency   string   `yaml:"default_currency"`
    SupportedCurrencies []string `yaml:"supported_currencies"`
}
```

### Payment Processing Implementation

```go
func (p *StripePlugin) ProcessPayment(ctx context.Context, payment *PaymentRequest) (*PaymentResult, error) {
    // Create payment intent
    params := &stripe.PaymentIntentParams{
        Amount:   stripe.Int64(payment.Amount.IntPart()),
        Currency: stripe.String(strings.ToLower(payment.Currency)),
        PaymentMethodTypes: stripe.StringSlice(p.config.PaymentMethods),
        CaptureMethod: stripe.String(p.config.CaptureMethod),
    }
    
    // Set customer if provided
    if payment.CustomerID != "" {
        params.Customer = stripe.String(payment.CustomerID)
    }
    
    // Set payment method if provided
    if payment.PaymentMethodID != "" {
        params.PaymentMethod = stripe.String(payment.PaymentMethodID)
        params.ConfirmationMethod = stripe.String("manual")
        params.Confirm = stripe.Bool(true)
    }
    
    // Add metadata
    if payment.Metadata != nil {
        params.Metadata = make(map[string]string)
        for key, value := range payment.Metadata {
            params.Metadata[key] = fmt.Sprintf("%v", value)
        }
    }
    
    // Set 3D Secure requirements
    if p.config.Require3DS == "required" {
        params.PaymentMethodOptions = &stripe.PaymentIntentPaymentMethodOptionsParams{
            Card: &stripe.PaymentIntentPaymentMethodOptionsCardParams{
                RequestThreeDSecure: stripe.String("required"),
            },
        }
    }
    
    // Create payment intent
    pi, err := paymentintent.New(params)
    if err != nil {
        return nil, fmt.Errorf("stripe payment intent creation failed: %w", err)
    }
    
    return &PaymentResult{
        PaymentID:    pi.ID,
        Status:       mapStripeStatus(pi.Status),
        Amount:       decimal.NewFromInt(pi.Amount),
        Currency:     strings.ToUpper(string(pi.Currency)),
        ProviderID:   "stripe",
        ClientSecret: pi.ClientSecret,
        Metadata: map[string]interface{}{
            "payment_intent_id": pi.ID,
            "requires_action":   pi.Status == stripe.PaymentIntentStatusRequiresAction,
        },
        CreatedAt: timePtr(time.Unix(pi.Created, 0)),
    }, nil
}
```

### Subscription Management Implementation

```go
func (p *StripePlugin) CreateSubscription(ctx context.Context, subscription *SubscriptionRequest) (*SubscriptionResult, error) {
    // Create or retrieve customer
    customer, err := p.getOrCreateCustomer(subscription.CustomerEmail, subscription.CustomerName)
    if err != nil {
        return nil, fmt.Errorf("customer creation failed: %w", err)
    }
    
    // Attach payment method to customer
    if subscription.PaymentMethodID != "" {
        _, err = paymentmethod.Attach(subscription.PaymentMethodID, &stripe.PaymentMethodAttachParams{
            Customer: stripe.String(customer.ID),
        })
        if err != nil {
            return nil, fmt.Errorf("payment method attachment failed: %w", err)
        }
        
        // Set as default payment method
        _, err = customer.Update(customer.ID, &stripe.CustomerParams{
            InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
                DefaultPaymentMethod: stripe.String(subscription.PaymentMethodID),
            },
        })
        if err != nil {
            return nil, fmt.Errorf("default payment method update failed: %w", err)
        }
    }
    
    // Create subscription
    params := &stripe.SubscriptionParams{
        Customer: stripe.String(customer.ID),
        Items: []*stripe.SubscriptionItemsParams{
            {
                Price: stripe.String(subscription.PriceID),
            },
        },
    }
    
    // Add trial period if specified
    if subscription.TrialDays > 0 {
        trialEnd := time.Now().AddDate(0, 0, subscription.TrialDays).Unix()
        params.TrialEnd = stripe.Int64(trialEnd)
    }
    
    // Add metadata
    if subscription.Metadata != nil {
        params.Metadata = make(map[string]string)
        for key, value := range subscription.Metadata {
            params.Metadata[key] = fmt.Sprintf("%v", value)
        }
    }
    
    // Create subscription
    sub, err := subscription.New(params)
    if err != nil {
        return nil, fmt.Errorf("stripe subscription creation failed: %w", err)
    }
    
    return &SubscriptionResult{
        SubscriptionID: sub.ID,
        Status:         mapStripeSubscriptionStatus(sub.Status),
        CustomerID:     customer.ID,
        PriceID:        subscription.PriceID,
        CurrentPeriodStart: timePtr(time.Unix(sub.CurrentPeriodStart, 0)),
        CurrentPeriodEnd:   timePtr(time.Unix(sub.CurrentPeriodEnd, 0)),
        TrialEnd:       timePtr(time.Unix(sub.TrialEnd, 0)),
        ProviderID:     "stripe",
        CreatedAt:      timePtr(time.Unix(sub.Created, 0)),
    }, nil
}
```

### Webhook Handling Implementation

```go
func (p *StripePlugin) HandleWebhook(ctx context.Context, payload []byte, headers map[string]string) (*WebhookResult, error) {
    // Get Stripe signature
    signature := headers["Stripe-Signature"]
    if signature == "" {
        return nil, fmt.Errorf("missing Stripe signature")
    }
    
    // Verify webhook signature
    event, err := webhook.ConstructEvent(payload, signature, p.config.WebhookSecret)
    if err != nil {
        return nil, fmt.Errorf("webhook signature verification failed: %w", err)
    }
    
    // Process webhook event
    result := p.processWebhookEvent(ctx, &event)
    
    return &WebhookResult{
        Provider: "stripe",
        Events:   []*WebhookEventResult{result},
    }, nil
}

func (p *StripePlugin) processWebhookEvent(ctx context.Context, event *stripe.Event) *WebhookEventResult {
    switch event.Type {
    case "payment_intent.succeeded":
        var pi stripe.PaymentIntent
        json.Unmarshal(event.Data.Raw, &pi)
        
        return &WebhookEventResult{
            PaymentID: pi.ID,
            Event:     "payment_succeeded",
            Timestamp: time.Unix(event.Created, 0),
            Status:    PaymentStatusSucceeded,
            Amount:    decimal.NewFromInt(pi.Amount),
            Currency:  strings.ToUpper(string(pi.Currency)),
        }
        
    case "payment_intent.payment_failed":
        var pi stripe.PaymentIntent
        json.Unmarshal(event.Data.Raw, &pi)
        
        return &WebhookEventResult{
            PaymentID: pi.ID,
            Event:     "payment_failed",
            Timestamp: time.Unix(event.Created, 0),
            Status:    PaymentStatusFailed,
            Reason:    pi.LastPaymentError.Message,
        }
        
    case "invoice.payment_succeeded":
        var invoice stripe.Invoice
        json.Unmarshal(event.Data.Raw, &invoice)
        
        return &WebhookEventResult{
            PaymentID:      invoice.PaymentIntent.ID,
            SubscriptionID: invoice.Subscription.ID,
            Event:          "subscription_payment_succeeded",
            Timestamp:      time.Unix(event.Created, 0),
            Status:         PaymentStatusSucceeded,
            Amount:         decimal.NewFromInt(invoice.AmountPaid),
            Currency:       strings.ToUpper(string(invoice.Currency)),
        }
        
    case "customer.subscription.deleted":
        var sub stripe.Subscription
        json.Unmarshal(event.Data.Raw, &sub)
        
        return &WebhookEventResult{
            SubscriptionID: sub.ID,
            Event:          "subscription_cancelled",
            Timestamp:      time.Unix(event.Created, 0),
            Status:         SubscriptionStatusCancelled,
        }
        
    default:
        return &WebhookEventResult{
            Event:     event.Type,
            Timestamp: time.Unix(event.Created, 0),
            Metadata: map[string]interface{}{
                "stripe_event_id": event.ID,
            },
        }
    }
}
```

## Configuration Examples

### Basic Configuration

```yaml
payment:
  gateways:
    stripe:
      plugin: "stripe"
      config:
        secret_key: "${STRIPE_SECRET_KEY}"
        publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
        webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
        default_currency: "usd"
```

### Advanced Configuration

```yaml
payment:
  gateways:
    stripe:
      plugin: "stripe"
      config:
        secret_key: "${STRIPE_SECRET_KEY}"
        publishable_key: "${STRIPE_PUBLISHABLE_KEY}"
        webhook_secret: "${STRIPE_WEBHOOK_SECRET}"
        
        # API settings
        api_version: "2023-10-16"
        capture_method: "automatic"
        
        # Security settings
        require_3ds: "automatic"
        
        # Payment methods
        payment_methods: ["card", "sepa_debit", "ideal"]
        
        # Multi-currency support
        default_currency: "usd"
        supported_currencies: ["usd", "eur", "gbp", "vnd"]
        
        # Connect platform
        connect_enabled: true
```

## Error Handling

### Common Errors

```go
var (
    ErrStripeInvalidKey    = errors.New("invalid Stripe API key")
    ErrStripeCardDeclined  = errors.New("card was declined")
    ErrStripeInsufficientFunds = errors.New("insufficient funds")
    ErrStripeExpiredCard   = errors.New("card has expired")
)

func (p *StripePlugin) handleStripeError(err error) error {
    if stripeErr, ok := err.(*stripe.Error); ok {
        switch stripeErr.Code {
        case stripe.ErrorCodeCardDeclined:
            return ErrStripeCardDeclined
        case stripe.ErrorCodeInsufficientFunds:
            return ErrStripeInsufficientFunds
        case stripe.ErrorCodeExpiredCard:
            return ErrStripeExpiredCard
        case stripe.ErrorCodeInvalidAPIKey:
            return ErrStripeInvalidKey
        default:
            return fmt.Errorf("stripe error: %s", stripeErr.Msg)
        }
    }
    return err
}
```

## Testing

### Unit Tests

```go
func TestStripePlugin_ProcessPayment(t *testing.T) {
    plugin := &StripePlugin{
        client: mockStripeClient(),
        config: &StripeConfig{
            SecretKey:       "sk_test_...",
            DefaultCurrency: "usd",
            PaymentMethods:  []string{"card"},
        },
    }
    
    payment := &PaymentRequest{
        Amount:          decimal.NewFromFloat(10.00),
        Currency:        "USD",
        PaymentMethodID: "pm_test_card",
        CustomerID:      "cus_test_customer",
    }
    
    result, err := plugin.ProcessPayment(context.Background(), payment)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, result.PaymentID)
    assert.Equal(t, PaymentStatusSucceeded, result.Status)
}
```

## Related Documentation

- **[Payment Module](../../modules/payment.md)** - Core payment interfaces
- **[PayPal Plugin](./paypal.md)** - Alternative payment gateway
- **[VNPay Plugin](./vnpay.md)** - Vietnamese payment gateway
- **[Plugin System](../overview.md)** - Plugin architecture
- **[Stripe Documentation](https://stripe.com/docs)** - Official Stripe documentation
