#!/bin/bash

# Script để gom tất cả các file .md trong thư mục docs vào một file duy nhất.

# Dừng script ngay lập tức nếu có lỗi
set -e

# Định nghĩa các biến
DOCS_DIR="./docs"
OUTPUT_FILE="./ALL_DOCS.md"

echo "Bắt đầu quá trình gom tài liệu..."
echo "Thư mục nguồn: $DOCS_DIR"
echo "File đích: $OUTPUT_FILE"

# Xóa file output cũ nếu tồn tại để bắt đầu lại
> "$OUTPUT_FILE"

# Tìm tất cả các file .md, sắp xếp chúng và gom lại
find "$DOCS_DIR" -type f -name "*.md" | sort | while read -r file; do
  echo "Đang xử lý file: $file"
  
  # Thêm một tiêu đề để biết nội dung này từ file nào
  echo -e "\n\n---\n\n# Nguồn: $file\n\n---\n\n" >> "$OUTPUT_FILE"
  
  # Nối nội dung của file vào file output
  cat "$file" >> "$OUTPUT_FILE"
done

echo "Hoàn thành! File tài liệu tổng hợp đã được tạo tại: $OUTPUT_FILE"